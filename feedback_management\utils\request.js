/**
 * 网络请求工具类
 * 统一处理小程序的网络请求，包括错误处理、loading状态、token管理、智能缓存等
 */

const app = getApp();
const cacheManager = require('./cache-manager');
const loadingManager = require('./loading-manager');
const errorManager = require('./error-manager');

/**
 * 请求配置接口
 * @typedef {Object} RequestConfig
 * @property {string} url - 请求地址
 * @property {string} [method='GET'] - 请求方法
 * @property {Object} [data] - 请求数据
 * @property {Object} [header] - 请求头
 * @property {boolean} [showLoading=false] - 是否显示loading
 * @property {string} [loadingText='加载中...'] - loading文本
 * @property {boolean} [showError=true] - 是否显示错误提示
 * @property {boolean} [needAuth=true] - 是否需要认证
 * @property {boolean} [useCache=true] - 是否使用缓存（仅GET请求）
 * @property {number} [cacheTTL] - 缓存生存时间（毫秒）
 * @property {boolean} [forceRefresh=false] - 是否强制刷新缓存
 */

/**
 * 响应数据接口
 * @typedef {Object} ApiResponse
 * @property {boolean} success - 请求是否成功
 * @property {*} data - 响应数据
 * @property {string} message - 响应消息
 * @property {number} code - 响应代码
 */

class RequestUtil {
  /**
   * 获取完整的请求URL
   * @param {string} url - 相对路径或完整URL
   * @returns {string} 完整的URL
   */
  static getFullUrl(url) {
    if (url.startsWith('http')) {
      return url;
    }
    const baseUrl = app.globalData.baseUrl || 'http://192.168.16.94:8080';
    return `${baseUrl}${url.startsWith('/') ? url : '/' + url}`;
  }

  /**
   * 获取请求头
   * @param {Object} customHeader - 自定义请求头
   * @param {boolean} needAuth - 是否需要认证
   * @returns {Object} 请求头对象
   */
  static getHeaders(customHeader = {}, needAuth = true) {
    const headers = Object.assign({
      'content-type': 'application/json'
    }, customHeader);

    if (needAuth) {
      const token = wx.getStorageSync('authToken');
      if (token) {
        headers.Authorization = token;
      }
    }

    return headers;
  }

  /**
   * 处理响应数据
   * @param {Object} res - 微信请求响应对象
   * @param {boolean} showError - 是否显示错误提示
   * @returns {Promise<ApiResponse>} 处理后的响应数据
   */
  static handleResponse(res, showError = true) {
    return new Promise((resolve, reject) => {
      if (res.statusCode === 200) {
        if (res.data && res.data.success) {
          resolve(res.data);
        } else {
          const error = {
            code: res.data?.code || 'BUSINESS_ERROR',
            message: res.data?.message || '请求失败',
            statusCode: res.statusCode,
            data: res.data
          };

          // 使用错误管理器处理业务错误
          errorManager.handleError(error, {
            source: 'api_response',
            url: res.header?.['request-url'] || 'unknown'
          }, {
            showToUser: showError
          });

          reject(new Error(error.message));
        }
      } else if (res.statusCode === 401) {
        // 未授权，清除本地存储并跳转到登录页
        wx.removeStorageSync('authToken');
        wx.removeStorageSync('userInfo');
        wx.removeStorageSync('currentCompany');

        const authError = {
          code: 401,
          message: '登录已过期，请重新登录',
          statusCode: res.statusCode
        };

        // 使用错误管理器处理认证错误
        errorManager.handleError(authError, {
          source: 'auth_check',
          action: 'redirect_to_login'
        }, {
          showToUser: showError
        });

        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }, 1500);

        reject(new Error('登录已过期'));
      } else {
        const error = {
          code: res.statusCode,
          message: this.getErrorMessage(res.statusCode),
          statusCode: res.statusCode,
          data: res.data
        };

        // 使用错误管理器处理HTTP错误
        errorManager.handleError(error, {
          source: 'http_error',
          url: res.header?.['request-url'] || 'unknown'
        }, {
          showToUser: showError
        });
        reject(new Error(errorMsg));
      }
    });
  }

  /**
   * 根据状态码获取错误消息
   * @param {number} statusCode - HTTP状态码
   * @returns {string} 错误消息
   */
  static getErrorMessage(statusCode) {
    const errorMessages = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '请求的资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时'
    };
    
    return errorMessages[statusCode] || `请求失败 (${statusCode})`;
  }

  /**
   * 发起网络请求
   * @param {RequestConfig} config - 请求配置
   * @returns {Promise<ApiResponse>} 请求结果
   */
  static request(config) {
    const url = config.url;
    const method = config.method || 'GET';
    const data = config.data;
    const header = config.header || {};
    const showLoading = config.showLoading || false;
    const loadingText = config.loadingText || '加载中...';
    const showError = config.showError !== false;
    const needAuth = config.needAuth !== false;
    const useCache = config.useCache !== false;
    const cacheTTL = config.cacheTTL;
    const forceRefresh = config.forceRefresh || false;

    const upperMethod = method.toUpperCase();

    // 只有GET请求才使用缓存
    if (upperMethod === 'GET' && useCache && !forceRefresh) {
      const cacheKey = cacheManager.generateKey(url, upperMethod, data);
      const cachedData = cacheManager.get(cacheKey);

      if (cachedData) {
        return Promise.resolve(cachedData);
      }
    }

    return new Promise((resolve, reject) => {
      // 显示loading
      let loadingId = null;
      if (showLoading) {
        loadingId = loadingManager.show(loadingText);
      }

      wx.request({
        url: this.getFullUrl(url),
        method: upperMethod,
        data,
        header: this.getHeaders(header, needAuth),
        success: (res) => {
          this.handleResponse(res, showError)
            .then((result) => {
              // 缓存GET请求的成功响应
              if (upperMethod === 'GET' && useCache && result.success) {
                const cacheKey = cacheManager.generateKey(url, upperMethod, data);
                const cacheType = cacheManager.getCacheType(url);
                const ttl = cacheTTL || cacheManager.getTTL(cacheType);
                cacheManager.set(cacheKey, result, ttl);
              }
              resolve(result);
            })
            .catch(reject);
        },
        fail: (error) => {

          // 使用错误管理器处理网络错误
          const networkError = {
            code: 'NETWORK_ERROR',
            message: '网络错误，请检查网络连接',
            originalError: error,
            errMsg: error.errMsg
          };

          errorManager.handleError(networkError, {
            source: 'network_request',
            url: this.getFullUrl(url),
            method: upperMethod
          }, {
            showToUser: showError
          });

          reject(new Error('网络错误'));
        },
        complete: () => {
          if (showLoading && loadingId) {
            loadingManager.hide(loadingId);
          }
        }
      });
    });
  }

  /**
   * GET请求
   * @param {string} url - 请求地址
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项（包括缓存配置）
   * @returns {Promise<ApiResponse>} 请求结果
   */
  static get(url, params = {}, options = {}) {
    return this.request(Object.assign({
      url: url,
      method: 'GET',
      data: params
    }, options));
  }

  /**
   * POST请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} options - 其他选项
   * @returns {Promise<ApiResponse>} 请求结果
   */
  static post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} options - 其他选项
   * @returns {Promise<ApiResponse>} 请求结果
   */
  static put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }



  /**
   * 清除指定URL的缓存
   * @param {string} url - 要清除缓存的URL
   * @param {string} method - 请求方法
   * @param {Object} data - 请求数据
   */
  static clearCache(url, method = 'GET', data = {}) {
    const cacheKey = cacheManager.generateKey(url, method, data);
    cacheManager.remove(cacheKey);
  }

  /**
   * 根据URL模式清除缓存
   * @param {string} pattern - URL模式（支持通配符*）
   */
  static clearCacheByPattern(pattern) {
    cacheManager.clearByPattern(pattern);
  }

  /**
   * 清除所有缓存
   */
  static clearAllCache() {
    cacheManager.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  static getCacheStats() {
    return cacheManager.getStats();
  }

  /**
   * 强制刷新指定URL的数据
   * @param {string} url - 请求地址
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项
   * @returns {Promise<ApiResponse>} 请求结果
   */
  static refreshCache(url, params = {}, options = {}) {
    return this.get(url, params, { ...options, forceRefresh: true });
  }
}

module.exports = RequestUtil;
