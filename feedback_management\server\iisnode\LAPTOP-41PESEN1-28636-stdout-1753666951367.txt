上传目录结构初始化完成
[2025-07-28 01:42:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:42:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:42:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:34] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:42:35] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:42:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:42:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:42:38] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:42:38] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:42:39] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:42:39] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:42:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:42:41] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:42:41] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:42:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 9
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 9
[2025-07-28 01:42:44] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:42:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:42:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:43:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:43:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:14] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:14] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:43:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:43:15] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:15] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:43:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:43:17] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:17] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:43:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:18] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:18] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:43:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:43:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:41] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:41] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:43:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:43:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:53] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:53] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:43:53] [DEBUG] 工程统计信息查询成功 | {"total_tasks":0,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":0}
[2025-07-28 01:43:53] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:53] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:43:53] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:54] [DEBUG] 工程统计信息查询成功 | {"total_tasks":3,"supplying_tasks":0,"completed_tasks":1,"total_feedbacks":1}
[2025-07-28 01:43:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:54] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:43:54] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-28 01:43:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:43:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 01:43:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-28 01:44:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:13] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2014,"task_number":"B05-2200236","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:44:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-28 01:44:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:44:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 01:44:17] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:44:17] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 10
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 10
[2025-07-28 01:44:17] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:44:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:23] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:44:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:48:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:49:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:49:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 10
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 10
[2025-07-28 01:49:49] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:49:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:49:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:50:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:50:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:50:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:50:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:50:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:50:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:51:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:51:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:51:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:51:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:51:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:52:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:14:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:14:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 02:14:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 02:15:43] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:43] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:15:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 02:15:43] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:43] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 02:15:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 02:15:45] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:45] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:47] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:47] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 02:15:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 02:15:48] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:48] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 10
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 10
[2025-07-28 02:15:48] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 10
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 10
[2025-07-28 02:15:48] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 02:15:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:15:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 02:15:50] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:50] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:15:51] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 02:15:51] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 02:15:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:16:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:16:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:16:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 02:16:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
