/**
 * 文件管理工具类
 * 统一处理文件删除、路径转换等操作
 */

const fs = require('fs');
const path = require('path');

class FileManager {
  /**
   * 标准化文件路径
   * 将API路径转换为实际文件系统路径
   * @param {string} filePath - 原始文件路径
   * @returns {string} 标准化后的文件路径
   */
  static normalizePath(filePath) {
    if (!filePath) return '';
    
    let normalizedPath = filePath;
    
    // 处理 /api/files/ 格式的路径
    if (normalizedPath.startsWith('/api/files/')) {
      normalizedPath = normalizedPath.replace('/api/files/', 'upload/');
    }
    // 处理 /upload/ 格式的路径
    else if (normalizedPath.startsWith('/upload/')) {
      normalizedPath = normalizedPath.substring(1); // 去掉开头的 "/"
    }
    // 处理 upload/ 格式的路径（已经是正确格式）
    else if (normalizedPath.startsWith('upload/')) {
      // 保持不变
    }
    
    return normalizedPath;
  }



  /**
   * 检查文件是否存在
   * @param {string} filePath - 文件路径
   * @returns {boolean} 文件是否存在
   */
  static fileExists(filePath) {
    try {
      const normalizedPath = this.normalizePath(filePath);
      const fullPath = path.resolve(normalizedPath);
      return fs.existsSync(fullPath);
    } catch (error) {
      return false;
    }
  }



  /**
   * 确保目录存在
   * @param {string} dirPath - 目录路径
   */
  static ensureDirectory(dirPath) {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`创建目录: ${dirPath}`);
      }
    } catch (error) {
      console.error(`创建目录失败: ${dirPath}`, error.message);
      throw error;
    }
  }

  /**
   * 初始化上传目录结构
   * 确保所有必要的上传目录都存在
   */
  static initializeUploadDirectories() {
    const uploadDirs = [
      'upload',
      'upload/images',
      'upload/videos',
      'upload/records',
      'upload/others'
    ];

    uploadDirs.forEach(dir => {
      this.ensureDirectory(dir);
    });

    console.log('上传目录结构初始化完成');
  }

  /**
   * 获取文件信息
   * @param {string} filePath - 文件路径
   * @returns {Object|null} 文件信息或null
   */
  static getFileInfo(filePath) {
    try {
      const normalizedPath = this.normalizePath(filePath);
      const fullPath = path.resolve(normalizedPath);
      
      if (fs.existsSync(fullPath)) {
        const stats = fs.statSync(fullPath);
        return {
          path: fullPath,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory()
        };
      }
      return null;
    } catch (error) {
      console.error(`获取文件信息失败: ${filePath}`, error.message);
      return null;
    }
  }
}

module.exports = FileManager;
