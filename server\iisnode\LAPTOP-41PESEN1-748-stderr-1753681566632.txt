[2025-07-28 05:46:20] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2780ms"}
[2025-07-28 05:46:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"2785ms","threshold":"2000ms"}
[2025-07-28 05:46:21] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":304,"duration":"4062ms"}
[2025-07-28 05:46:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4063ms","threshold":"2000ms"}
[2025-07-28 05:46:26] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":304,"duration":"4649ms"}
[2025-07-28 05:46:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4650ms","threshold":"2000ms"}
[2025-07-28 05:46:27] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"5422ms"}
[2025-07-28 05:46:27] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"5423ms","threshold":"2000ms"}
[2025-07-28 05:46:30] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3363ms"}
[2025-07-28 05:46:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3365ms","threshold":"2000ms"}
[2025-07-28 05:52:53] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":304,"duration":"2664ms"}
[2025-07-28 05:52:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2665ms","threshold":"2000ms"}
[2025-07-28 05:52:54] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3197ms"}
[2025-07-28 05:52:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3198ms","threshold":"2000ms"}
[2025-07-28 05:52:56] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2051ms"}
[2025-07-28 05:52:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2052ms","threshold":"2000ms"}
[2025-07-28 05:54:52] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":304,"duration":"2019ms"}
[2025-07-28 05:54:52] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2020ms","threshold":"2000ms"}
[2025-07-28 05:54:55] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":304,"duration":"3196ms"}
[2025-07-28 05:54:55] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3196ms","threshold":"2000ms"}
[2025-07-28 05:54:55] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3684ms"}
[2025-07-28 05:54:55] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3685ms","threshold":"2000ms"}
[2025-07-28 05:54:57] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2004ms"}
[2025-07-28 05:54:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2005ms","threshold":"2000ms"}
[2025-07-28 05:59:58] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"2140ms"}
[2025-07-28 05:59:58] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2141ms","threshold":"2000ms"}
[2025-07-28 08:00:32] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
获取分类反馈单数据失败: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\NBSDB\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.
[2025-07-28 08:00:32] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-28 08:00:32] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
获取分类反馈单列表失败: RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\NBSDB\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.
    at handleError (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\mssql\lib\tedious\request.js:384:15)
    at Connection.emit (node:events:518:28)
    at Connection.emit (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\connection.js:959:18)
    at RequestTokenHandler.onErrorMessage (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\handler.js:285:21)
    at Readable.<anonymous> (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\token-stream-parser.js:18:33)
    at Readable.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
    at Readable.push (node:internal/streams/readable:393:5)
    at nextAsync (node:internal/streams/from:194:22) {
  code: 'EREQUEST',
  originalError: Error: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\NBSDB\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.
      at handleError (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\mssql\lib\tedious\request.js:382:19)
      at Connection.emit (node:events:518:28)
      at Connection.emit (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\connection.js:959:18)
      at RequestTokenHandler.onErrorMessage (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\handler.js:285:21)
      at Readable.<anonymous> (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\token-stream-parser.js:18:33)
      at Readable.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
      at Readable.push (node:internal/streams/readable:393:5)
      at nextAsync (node:internal/streams/from:194:22) {
    info: ErrorMessageToken {
      name: 'ERROR',
      handlerName: 'onErrorMessage',
      number: 823,
      state: 2,
      class: 24,
      message: "The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.",
      serverName: 'LAPTOP-41PESEN1',
      procName: '',
      lineNumber: 2
    }
  },
  number: 823,
  lineNumber: 2,
  state: 2,
  class: 24,
  serverName: 'LAPTOP-41PESEN1',
  procName: '',
  precedingErrors: []
}
[2025-07-28 08:00:34] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count,\n               -- 计算最近操作时间（取UpdatedAt和CreatedAt的最大值）\n               CASE\n                 WHEN i.UpdatedAt > i.CreatedAt THEN i.UpdatedAt\n                 ELSE i.CreatedAt\n               END as last_operation_time\n        FROM CU_feedbacks i\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY last_operation_time DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
获取简化反馈单列表失败: RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\NBSDB\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.
    at handleError (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\mssql\lib\tedious\request.js:384:15)
    at Connection.emit (node:events:518:28)
    at Connection.emit (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\connection.js:959:18)
    at RequestTokenHandler.onErrorMessage (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\handler.js:285:21)
    at Readable.<anonymous> (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\token-stream-parser.js:18:33)
    at Readable.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
    at Readable.push (node:internal/streams/readable:393:5)
    at nextAsync (node:internal/streams/from:194:22) {
  code: 'EREQUEST',
  originalError: Error: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\NBSDB\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.
      at handleError (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\mssql\lib\tedious\request.js:382:19)
      at Connection.emit (node:events:518:28)
      at Connection.emit (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\connection.js:959:18)
      at RequestTokenHandler.onErrorMessage (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\handler.js:285:21)
      at Readable.<anonymous> (d:\Code-workspace\miniprogram-practice\feedback_management\server\node_modules\tedious\lib\token\token-stream-parser.js:18:33)
      at Readable.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)
      at Readable.push (node:internal/streams/readable:393:5)
      at nextAsync (node:internal/streams/from:194:22) {
    info: ErrorMessageToken {
      name: 'ERROR',
      handlerName: 'onErrorMessage',
      number: 823,
      state: 2,
      class: 24,
      message: "The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.",
      serverName: 'LAPTOP-41PESEN1',
      procName: '',
      lineNumber: 2
    }
  },
  number: 823,
  lineNumber: 2,
  state: 2,
  class: 24,
  serverName: 'LAPTOP-41PESEN1',
  procName: '',
  precedingErrors: []
}
[2025-07-28 08:00:34] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":["2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-28 08:00:34] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-28 08:00:36] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:36] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-28 08:00:36] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":500,"duration":"3071ms"}
[2025-07-28 08:00:36] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3072ms","threshold":"2000ms"}
