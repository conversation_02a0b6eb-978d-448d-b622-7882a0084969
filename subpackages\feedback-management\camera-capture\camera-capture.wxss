/* 相机拍摄页面样式 */
.camera-container {
  width: 100vw;
  height: 100vh;
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.camera-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.camera-view {
  width: 100%;
  height: 100%;
  /* 确保相机预览是竖屏方向 */
  transform: none;
}

/* 拍摄控制区域 */
.capture-controls {
  position: absolute;
  bottom: 120rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 80rpx;
  z-index: 10;
}

.control-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.control-icon {
  font-size: 32rpx;
  color: white;
  line-height: 1;
}

/* 占位元素样式，保持布局平衡 */
.control-placeholder {
  width: 80rpx;
  height: 80rpx;
}

/* 拍摄按钮 */
.capture-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.capture-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.2s ease;
}

.capture-button:active {
  transform: scale(0.95);
}

.capture-inner {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: white;
  transition: all 0.3s ease;
}

.capture-button.video .capture-inner {
  border-radius: 16rpx;
}

.capture-inner.recording {
  background-color: #ff3b30;
  animation: recordingPulse 1s ease-in-out infinite;
}

@keyframes recordingPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 录像信息 */
.recording-info {
  position: absolute;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  background-color: rgba(255, 59, 48, 0.9);
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  z-index: 10;
}

.recording-time {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.recording-indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: white;
  animation: recordingBlink 1s ease-in-out infinite;
}

@keyframes recordingBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 时长提示 */
.duration-hint {
  position: absolute;
  top: 280rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  z-index: 10;
}

.hint-text {
  color: white;
  font-size: 24rpx;
}

/* 调试信息 */
.debug-info {
  position: absolute;
  top: 360rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.debug-text {
  color: #00ff00;
  font-size: 22rpx;
  font-family: monospace;
  text-align: center;
}

/* 媒体预览 */
.media-preview {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持视频原始比例，不裁剪 */
}

/* 预览控制按钮 */
.preview-controls {
  position: absolute;
  bottom: 120rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 40rpx;
  padding: 0 80rpx;
  z-index: 10;
}

.preview-controls .btn {
  flex: 1;
  max-width: 200rpx;
  padding: 24rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10rpx);
}

.btn-primary {
  background-color: #007aff;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
}

/* 移除上传进度相关样式，使用wx.showLoading代替 */
