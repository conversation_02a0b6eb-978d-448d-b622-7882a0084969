<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="title">工程列表</text>
    <text class="subtitle" wx:if="{{currentCompany}}">{{currentCompany.name}}</text>
  </view>
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="search-box">
      <input class="search-input" placeholder="搜索工程名称、项目公司或施工单位" value="{{searchKeyword}}" bindinput="onSearchInput" bindconfirm="onSearch" />
      <button class="search-btn" bindtap="onSearch">搜索</button>
    </view>
  </view>
  <!-- 工程列表 -->
  <view class="project-list" wx:if="{{projectList.length > 0}}">
    <view class="project-item" wx:for="{{projectList}}" wx:key="id">
      <view class="project-info" bindtap="onProjectTap" data-project="{{item}}">
        <view class="project-main">
          <view class="project-title">
            <text class="project-name">{{item.name}}</text>
            <text class="project-code" wx:if="{{item.code}}">工程编号：{{item.code}}</text>
          </view>
        </view>
        <view class="project-details">
          <text class="construction-unit" wx:if="{{item.construction_unit}}">
            施工单位：{{item.construction_unit}}
          </text>
          <view class="project-meta">
            <text class="task-count">{{item.taskCountText}}</text>
            <text class="created-time">{{item.createdTimeText}}</text>
          </view>
        </view>
      </view>

    </view>


  </view>
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{projectList.length === 0 && !loading}}">
    <view class="empty-icon">📋</view>
    <text class="empty-text">暂无工程</text>
    <text class="empty-desc">当前公司还没有工程项目</text>
  </view>
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

</view>
