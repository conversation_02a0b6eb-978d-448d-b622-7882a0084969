/**
 * 模态框组件
 * 提供统一的弹窗功能
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示模态框
    show: {
      type: Boolean,
      value: false
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 内容
    content: {
      type: String,
      value: ''
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      value: true
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      value: '取消'
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      value: '确定'
    },
    // 取消按钮颜色
    cancelColor: {
      type: String,
      value: '#666666'
    },
    // 确认按钮颜色
    confirmColor: {
      type: String,
      value: '#1296DB'
    },
    // 是否点击遮罩关闭
    maskClosable: {
      type: Boolean,
      value: false
    },
    // 模态框宽度
    width: {
      type: String,
      value: '80%'
    },
    // 最大高度
    maxHeight: {
      type: String,
      value: '70%'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 内部状态
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击遮罩层
     */
    onMaskTap() {
      if (this.properties.maskClosable) {
        this.onCancel();
      }
    },

    /**
     * 点击取消按钮
     */
    onCancel() {
      this.triggerEvent('cancel', {});
      this.triggerEvent('close', { action: 'cancel' });
    },

    /**
     * 点击确认按钮
     */
    onConfirm() {
      this.triggerEvent('confirm', {});
      this.triggerEvent('close', { action: 'confirm' });
    },

    /**
     * 点击关闭按钮
     */
    onClose() {
      this.triggerEvent('close', { action: 'close' });
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击模态框内容时的事件冒泡
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
    },
    
    detached() {
      // 组件实例被从页面节点树移除后执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 页面被展示
    },
    
    hide() {
      // 页面被隐藏
    }
  }
});
