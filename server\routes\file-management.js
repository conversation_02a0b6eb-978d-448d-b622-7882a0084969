/**
 * 文件管理路由
 * 提供文件清理、统计和管理功能
 */

const express = require('express');
const router = express.Router();
const { requireAuth } = require('../middleware/auth');
const FileCleanupManager = require('../utils/file-cleanup-manager');

// 创建文件清理管理器实例
const fileCleanupManager = new FileCleanupManager();

// 手动触发文件清理
router.post('/cleanup', requireAuth, async (req, res) => {
  try {
    console.log('手动触发文件清理任务');
    
    await fileCleanupManager.performCleanup();
    
    res.json({
      success: true,
      message: '文件清理任务执行完成'
    });
  } catch (error) {
    console.error('手动文件清理失败:', error);
    res.status(500).json({
      success: false,
      message: '文件清理失败',
      error: error.message
    });
  }
});

// 获取清理统计信息
router.get('/cleanup-stats', requireAuth, async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 7;
    const stats = await fileCleanupManager.getCleanupStats(days);
    
    res.json({
      success: true,
      data: {
        period: `${days}天`,
        stats: stats
      }
    });
  } catch (error) {
    console.error('获取清理统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取清理统计失败',
      error: error.message
    });
  }
});

// 清理指定文件
router.delete('/file', requireAuth, async (req, res) => {
  try {
    const { filePath, reason = 'manual' } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: '文件路径不能为空'
      });
    }
    
    const result = await fileCleanupManager.cleanupFile(filePath, reason);
    
    if (result) {
      res.json({
        success: true,
        message: '文件删除成功'
      });
    } else {
      res.status(404).json({
        success: false,
        message: '文件不存在或删除失败'
      });
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除文件失败',
      error: error.message
    });
  }
});

// 获取文件存储统计
router.get('/storage-stats', requireAuth, async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const uploadDir = path.join(__dirname, '..', 'upload');
    
    // 计算目录大小
    const calculateDirSize = (dir) => {
      let totalSize = 0;
      let fileCount = 0;
      
      const scanDir = (currentDir) => {
        try {
          const items = fs.readdirSync(currentDir);
          
          for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stats = fs.statSync(fullPath);
            
            if (stats.isDirectory()) {
              scanDir(fullPath);
            } else if (stats.isFile()) {
              totalSize += stats.size;
              fileCount++;
            }
          }
        } catch (error) {
          console.error(`扫描目录失败: ${currentDir}`, error);
        }
      };
      
      if (fs.existsSync(dir)) {
        scanDir(dir);
      }
      
      return { totalSize, fileCount };
    };
    
    // 计算各类型文件统计
    const imageStats = calculateDirSize(path.join(uploadDir, 'images'));
    const videoStats = calculateDirSize(path.join(uploadDir, 'videos'));
    const audioStats = calculateDirSize(path.join(uploadDir, 'audios'));
    const tempStats = calculateDirSize(path.join(uploadDir, 'temp'));
    
    const totalStats = {
      totalSize: imageStats.totalSize + videoStats.totalSize + audioStats.totalSize + tempStats.totalSize,
      fileCount: imageStats.fileCount + videoStats.fileCount + audioStats.fileCount + tempStats.fileCount
    };
    
    res.json({
      success: true,
      data: {
        total: {
          size: totalStats.totalSize,
          sizeFormatted: formatFileSize(totalStats.totalSize),
          fileCount: totalStats.fileCount
        },
        breakdown: {
          images: {
            size: imageStats.totalSize,
            sizeFormatted: formatFileSize(imageStats.totalSize),
            fileCount: imageStats.fileCount
          },
          videos: {
            size: videoStats.totalSize,
            sizeFormatted: formatFileSize(videoStats.totalSize),
            fileCount: videoStats.fileCount
          },
          audios: {
            size: audioStats.totalSize,
            sizeFormatted: formatFileSize(audioStats.totalSize),
            fileCount: audioStats.fileCount
          },
          temp: {
            size: tempStats.totalSize,
            sizeFormatted: formatFileSize(tempStats.totalSize),
            fileCount: tempStats.fileCount
          }
        }
      }
    });
  } catch (error) {
    console.error('获取存储统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取存储统计失败',
      error: error.message
    });
  }
});

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
}

// 检查文件系统健康状态
router.get('/health', requireAuth, async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');
    
    const uploadDir = path.join(__dirname, '..', 'upload');
    const tempDir = path.join(uploadDir, 'temp');
    
    // 检查目录是否存在和可写
    const checkDirectory = (dir) => {
      try {
        if (!fs.existsSync(dir)) {
          return { exists: false, writable: false };
        }
        
        // 测试写入权限
        const testFile = path.join(dir, 'test-write.tmp');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        
        return { exists: true, writable: true };
      } catch (error) {
        return { exists: fs.existsSync(dir), writable: false };
      }
    };
    
    const uploadDirStatus = checkDirectory(uploadDir);
    const tempDirStatus = checkDirectory(tempDir);
    
    const isHealthy = uploadDirStatus.exists && uploadDirStatus.writable && 
                     tempDirStatus.exists && tempDirStatus.writable;
    
    res.json({
      success: true,
      data: {
        healthy: isHealthy,
        uploadDirectory: uploadDirStatus,
        tempDirectory: tempDirStatus,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('检查文件系统健康状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查文件系统健康状态失败',
      error: error.message
    });
  }
});

module.exports = router;
