[2025-07-30 07:42:13] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.<PERSON>ongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:13] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:42:26] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:26] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:42:32] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:32] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:42:46] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:46] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:38] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:38] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:45] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:45] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:49] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:53] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:53] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:44:04] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:44:04] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:44:08] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:44:08] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:44:11] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:44:11] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:44:15] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:44:15] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
