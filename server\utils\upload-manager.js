/**
 * 文件上传管理工具类
 * 统一处理文件上传相关的配置和逻辑
 */

const multer = require('multer');
const path = require('path');
const fs = require('fs');
const FileManager = require('./file-manager');

class UploadManager {
  /**
   * 获取允许的文件类型
   */
  static getAllowedTypes() {
    return {
      mimeTypes: [
        // 图片格式
        "image/jpeg",
        "image/jpg", 
        "image/png",
        "image/gif",
        "image/webp",

        // 视频格式
        "video/mp4",           // 真实设备
        "video/webm",          // 调试环境
        "video/avi",
        "video/mov",
        "video/quicktime",
        "video/3gpp",
        "video/x-msvideo",
        "video/x-ms-wmv",

        // 音频格式
        "audio/mp3",
        "audio/wav",
        "audio/m4a",
        "audio/aac",
        "audio/mpeg",
        "audio/mp4",
        "audio/webm",          // 调试环境可能的音频格式

        // 通用格式
        "application/octet-stream",
      ],
      extensions: ['.mp4', '.webm', '.avi', '.mov', '.3gp', '.wmv', '.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp3', '.wav', '.m4a', '.aac']
    };
  }

  /**
   * 获取上传路径
   * @param {string} mimetype - 文件MIME类型
   * @returns {string} 上传路径
   */
  static getUploadPath(mimetype) {
    let uploadPath = "upload/";

    if (mimetype.startsWith("image/")) {
      uploadPath += "images/";
    } else if (mimetype.startsWith("video/")) {
      uploadPath += "videos/";
    } else if (mimetype.startsWith("audio/")) {
      uploadPath += "records/";
    } else {
      uploadPath += "others/";
    }

    return uploadPath;
  }

  /**
   * 生成唯一文件名
   * @param {string} originalname - 原始文件名
   * @param {string} fieldname - 字段名
   * @returns {string} 唯一文件名
   */
  static generateUniqueFilename(originalname, fieldname = 'file') {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const ext = path.extname(originalname);
    return fieldname + "-" + uniqueSuffix + ext;
  }

  /**
   * 文件过滤器
   * @param {Object} req - 请求对象
   * @param {Object} file - 文件对象
   * @param {Function} cb - 回调函数
   */
  static fileFilter(req, file, cb) {
    const allowedTypes = UploadManager.getAllowedTypes();

    // 检查MIME类型
    const mimeTypeAllowed = allowedTypes.mimeTypes.includes(file.mimetype);

    // 检查文件扩展名
    const extensionAllowed = allowedTypes.extensions.some(ext =>
      file.originalname.toLowerCase().endsWith(ext)
    );

    if (mimeTypeAllowed || extensionAllowed) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
    }
  }

  /**
   * 创建multer存储配置
   * @returns {Object} multer存储配置
   */
  static createStorage() {
    return multer.diskStorage({
      destination: function (req, file, cb) {
        const uploadPath = UploadManager.getUploadPath(file.mimetype);
        
        // 确保目录存在
        FileManager.ensureDirectory(uploadPath);
        
        cb(null, uploadPath);
      },
      filename: function (req, file, cb) {
        const filename = UploadManager.generateUniqueFilename(file.originalname, file.fieldname);
        cb(null, filename);
      },
    });
  }

  /**
   * 创建multer实例
   * @param {Object} options - 配置选项
   * @returns {Object} multer实例
   */
  static createUploader(options = {}) {
    const defaultOptions = {
      fileSize: 100 * 1024 * 1024, // 100MB
      files: 10, // 最多10个文件
    };

    const config = { ...defaultOptions, ...options };

    return multer({
      storage: UploadManager.createStorage(),
      fileFilter: UploadManager.fileFilter,
      limits: {
        fileSize: config.fileSize,
        files: config.files,
      },
    });
  }

  /**
   * 处理multer错误
   * @param {Error} error - 错误对象
   * @returns {Object} 标准化的错误响应
   */
  static handleUploadError(error) {
    console.error("文件上传错误详情:", {
      message: error.message,
      code: error.code,
      field: error.field,
      stack: error.stack
    });

    if (error instanceof multer.MulterError) {
      console.log("这是一个multer错误:", error.code);
      
      switch (error.code) {
        case "LIMIT_FILE_SIZE":
          return {
            status: 400,
            success: false,
            message: "文件大小超过限制（最大100MB）",
          };
        case "LIMIT_FILE_COUNT":
          return {
            status: 400,
            success: false,
            message: "文件数量超过限制（最多10个）",
          };
        case "LIMIT_UNEXPECTED_FILE":
          return {
            status: 400,
            success: false,
            message: "上传字段名错误，应为'file'",
          };
        default:
          return {
            status: 400,
            success: false,
            message: error.message || "文件上传失败",
          };
      }
    }

    return {
      status: 400,
      success: false,
      message: error.message || "文件上传失败",
    };
  }

  /**
   * 格式化文件信息
   * @param {Object} file - multer文件对象
   * @returns {Object} 格式化后的文件信息
   */
  static formatFileInfo(file) {
    const normalizedPath = file.path.replace(/\\/g, "/"); // 统一使用正斜杠
    const pathParts = normalizedPath.split("upload/");
    const relativePath = pathParts.length > 1 ? pathParts[1] : file.filename;

    return {
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: normalizedPath,
      url: `/api/files/${relativePath}`,
    };
  }

  /**
   * 批量格式化文件信息
   * @param {Array} files - multer文件对象数组
   * @returns {Array} 格式化后的文件信息数组
   */
  static formatFilesInfo(files) {
    return files.map(file => UploadManager.formatFileInfo(file));
  }

  /**
   * 验证base64图片数据
   * @param {string} imageData - base64图片数据
   * @returns {Object} 验证结果
   */
  static validateBase64Image(imageData) {
    if (!imageData) {
      return {
        valid: false,
        message: "图片数据不能为空"
      };
    }

    // 解析base64数据
    const matches = imageData.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
      return {
        valid: false,
        message: "无效的base64图片数据"
      };
    }

    const mimeType = matches[1];
    const base64Data = matches[2];

    // 检查文件类型
    if (!mimeType.startsWith("image/")) {
      return {
        valid: false,
        message: "只支持图片格式"
      };
    }

    return {
      valid: true,
      mimeType,
      base64Data
    };
  }

  /**
   * 保存base64图片
   * @param {string} mimeType - MIME类型
   * @param {string} base64Data - base64数据
   * @param {string} filename - 文件名（可选）
   * @returns {Object} 文件信息
   */
  static saveBase64Image(mimeType, base64Data, filename = null) {
    // 生成文件名
    const ext = mimeType.split("/")[1];
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const fileName = filename || `image-${uniqueSuffix}.${ext}`;

    // 确保目录存在
    const uploadDir = "upload/images/";
    FileManager.ensureDirectory(uploadDir);

    // 保存文件
    const filePath = path.join(uploadDir, fileName);
    fs.writeFileSync(filePath, base64Data, "base64");

    return {
      filename: fileName,
      originalname: filename || fileName,
      mimetype: mimeType,
      size: Buffer.byteLength(base64Data, "base64"),
      path: filePath.replace(/\\/g, "/"),
      url: `/api/files/images/${fileName}`,
    };
  }
}

module.exports = UploadManager;
