//用户模块的request请求
const http = require('../utils/http');
const requestModule = require('../request/common');
const util = require('../utils/util');
const { OpenId } = require('../config/common')


const login = (rescode)=>{
    http(requestModule.GetWXOpenId,{
        data:{
            code:rescode
        }
    }).then(
        res=>{
              util.setStorage(OpenId,res.data.data.openid);
        }
    ).catch(
        err=>{
            console.log('user-err',err);
        }
    )
}


module.exports={
    login
}