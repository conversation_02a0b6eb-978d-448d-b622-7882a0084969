<!--pages/update_userinfo/update_userinfo.wxml-->
<!-- 弹窗功能（用于确认个人信息） -->
<!-- 遮罩层 -->
<view>
    <form bindsubmit="formSubmit">
        <view wx:if="{{isShow}}" class='cover'>
            <!-- 可在此按需求自定义遮罩 -->
            <view style="position: relative;">
                <view class='cover_child'>
                    <!-- 信息输入界面 -->
                    <text class="child-title">用户信息核验</text>

                    <checkbox-group bindchange="checkboxChange">
                        <view class="cardriver">
                            <view>
                                <checkbox value="driver" checked="true" />
                            </view>
                            <view>司机</view>
                        </view>
                    </checkbox-group>

                    <input class="weui-input" name="userId" type="userId" placeholder="账号" bindinput="bindKeyInput" value="{{student_info.userId}}" />
                    <input class="weui-input" name="Phone" type="Phone" placeholder="电话号码" bindinput="bindKeyInput" value="{{student_info.Phone}}" />
                </view>
                <!-- 取消、确定按钮 -->
                <view class='btn-group'>
                    <button type="default" size="mini" catchtap="hideCover">取消</button>
                    <button type="primary" size="mini" form-type='submit' bindtap="openNews">确定</button>
                </view>
            </view>
        </view>
    </form>
</view>