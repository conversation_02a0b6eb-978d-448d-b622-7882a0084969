<!-- pages/profile/profile.wxml -->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="profile-card">
    <view class="avatar-section">
      <view class="user-info">
        <text class="username">{{userInfo ? userInfo.username : '未登录'}}</text>
        <text class="phone" wx:if="{{userInfo && userInfo.phone}}">{{userInfo.phone}}</text>
      </view>
    </view>
  </view>
  <!-- 所属公司列表 -->
  <view class="companies-card" wx:if="{{companies && companies.length > 0}}">
    <view class="card-header">
      <text class="card-title">所属公司</text>
    </view>
    <view class="companies-list">
      <view class="company-item" wx:for="{{companies}}" wx:key="id">
        <view class="company-info">
          <text class="company-name">{{item.name}}</text>
        </view>
        <view class="company-status" wx:if="{{currentCompany && currentCompany.id === item.id}}">
          <text class="current-tag">当前</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 功能菜单 -->
  <view class="menu-card">
    <view class="menu-item" bindtap="showChangePasswordModal">
      <view class="menu-icon">🔒</view>
      <text class="menu-text">修改密码</text>
      <text class="menu-arrow">></text>
    </view>
    <view class="menu-item" bindtap="logout">
      <view class="menu-icon">🚪</view>
      <text class="menu-text">退出登录</text>
      <text class="menu-arrow">></text>
    </view>
  </view>
</view>
<!-- 修改密码弹窗 -->
<view class="modal" wx:if="{{showChangePassword}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">修改密码</text>
      <text class="modal-close" bindtap="hideChangePasswordModal">×</text>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label required">旧密码</text>
        <input class="form-input" type="password" placeholder="请输入旧密码" value="{{passwordForm.oldPassword}}" bindinput="onOldPasswordInput" />
      </view>
      <view class="form-group">
        <text class="form-label required">新密码</text>
        <input class="form-input" type="password" placeholder="请输入新密码（6-50位）" value="{{passwordForm.newPassword}}" bindinput="onNewPasswordInput" />
        <view class="password-tips">
          <text class="tip-text">密码要求：</text>
          <text class="tip-item">• 长度6-50位</text>
          <text class="tip-item">• 不能与旧密码相同</text>
        </view>
      </view>
      <view class="form-group">
        <text class="form-label required">确认新密码</text>
        <input class="form-input" type="password" placeholder="请再次输入新密码" value="{{passwordForm.confirmPassword}}" bindinput="onConfirmPasswordInput" />
        <!-- 密码匹配提示 -->
        <view class="password-match" wx:if="{{passwordMatch.show}}">
          <text class="match-text {{passwordMatch.isMatch ? 'match-success' : 'match-error'}}">
            {{passwordMatch.message}}
          </text>
        </view>
      </view>
      <view class="form-actions">
        <button class="btn btn-cancel" bindtap="hideChangePasswordModal">取消</button>
        <button class="btn btn-primary" bindtap="submitChangePassword" loading="{{loading}}">
          确认修改
        </button>
      </view>
    </view>
  </view>
</view>