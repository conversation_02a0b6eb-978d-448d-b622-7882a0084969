/**
 * 坐标系转换工具
 * 处理不同坐标系之间的转换：WGS84、GCJ02、BD09
 */

const PI = Math.PI;
const X_PI = PI * 3000.0 / 180.0;
const A = 6378245.0;
const EE = 0.00669342162296594323;

/**
 * 判断坐标是否在中国境内
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {boolean} 是否在中国境内
 */
function isInChina(lng, lat) {
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
}

/**
 * 转换经度
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number} 转换后的经度
 */
function transformLng(lng, lat) {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
  return ret;
}

/**
 * 转换纬度
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number} 转换后的纬度
 */
function transformLat(lng, lat) {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
  return ret;
}

/**
 * WGS84 转 GCJ02
 * @param {number} lng WGS84经度
 * @param {number} lat WGS84纬度
 * @returns {object} GCJ02坐标 {lng, lat}
 */
function wgs84ToGcj02(lng, lat) {
  if (!isInChina(lng, lat)) {
    return { lng, lat };
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  
  const radlat = lat / 180.0 * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI);
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI);
  
  const mglat = lat + dlat;
  const mglng = lng + dlng;
  
  return { lng: mglng, lat: mglat };
}

/**
 * GCJ02 转 WGS84
 * @param {number} lng GCJ02经度
 * @param {number} lat GCJ02纬度
 * @returns {object} WGS84坐标 {lng, lat}
 */
function gcj02ToWgs84(lng, lat) {
  if (!isInChina(lng, lat)) {
    return { lng, lat };
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  
  const radlat = lat / 180.0 * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI);
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI);
  
  const mglat = lat - dlat;
  const mglng = lng - dlng;
  
  return { lng: mglng, lat: mglat };
}

/**
 * GCJ02 转 BD09
 * @param {number} lng GCJ02经度
 * @param {number} lat GCJ02纬度
 * @returns {object} BD09坐标 {lng, lat}
 */
function gcj02ToBd09(lng, lat) {
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI);
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI);
  const bd_lng = z * Math.cos(theta) + 0.0065;
  const bd_lat = z * Math.sin(theta) + 0.006;
  return { lng: bd_lng, lat: bd_lat };
}

/**
 * BD09 转 GCJ02
 * @param {number} lng BD09经度
 * @param {number} lat BD09纬度
 * @returns {object} GCJ02坐标 {lng, lat}
 */
function bd09ToGcj02(lng, lat) {
  const x = lng - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
  const gcj_lng = z * Math.cos(theta);
  const gcj_lat = z * Math.sin(theta);
  return { lng: gcj_lng, lat: gcj_lat };
}

/**
 * 智能坐标转换
 * 根据目标API自动选择合适的坐标系
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @param {string} fromCoordType 源坐标系 'wgs84'|'gcj02'|'bd09'
 * @param {string} toCoordType 目标坐标系 'wgs84'|'gcj02'|'bd09'
 * @returns {object} 转换后的坐标 {lng, lat}
 */
function convertCoordinate(lng, lat, fromCoordType, toCoordType) {
  if (fromCoordType === toCoordType) {
    return { lng, lat };
  }
  
  let result = { lng, lat };
  
  // 先转换到GCJ02作为中间坐标系
  if (fromCoordType === 'wgs84') {
    result = wgs84ToGcj02(lng, lat);
  } else if (fromCoordType === 'bd09') {
    result = bd09ToGcj02(lng, lat);
  }
  
  // 再从GCJ02转换到目标坐标系
  if (toCoordType === 'wgs84') {
    result = gcj02ToWgs84(result.lng, result.lat);
  } else if (toCoordType === 'bd09') {
    result = gcj02ToBd09(result.lng, result.lat);
  }
  
  return result;
}

/**
 * 为天地图API准备坐标
 * 天地图API可能需要WGS84坐标系
 * @param {number} lng GCJ02经度（微信小程序获取的坐标）
 * @param {number} lat GCJ02纬度（微信小程序获取的坐标）
 * @returns {object} 适合天地图API的坐标 {lng, lat}
 */
function prepareForTianditu(lng, lat) {
  // 天地图API通常使用WGS84坐标系
  return gcj02ToWgs84(lng, lat);
}

/**
 * 为微信地图准备坐标
 * 微信地图使用GCJ02坐标系
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @param {string} fromCoordType 源坐标系
 * @returns {object} 适合微信地图的坐标 {lng, lat}
 */
function prepareForWechatMap(lng, lat, fromCoordType = 'gcj02') {
  return convertCoordinate(lng, lat, fromCoordType, 'gcj02');
}

module.exports = {
  wgs84ToGcj02,
  gcj02ToWgs84,
  gcj02ToBd09,
  bd09ToGcj02,
  convertCoordinate,
  prepareForTianditu,
  prepareForWechatMap,
  isInChina
};
