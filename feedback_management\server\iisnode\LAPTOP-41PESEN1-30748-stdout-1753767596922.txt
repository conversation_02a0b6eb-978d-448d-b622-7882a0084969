  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 05:40:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:40:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-29 05:40:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 05:40:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:40:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-29 05:40:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 05:42:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:42:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-29 05:42:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 05:42:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:42:26] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":4011,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-29 05:42:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 05:42:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:42:34] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:42:34] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:42:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 05:42:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 05:42:37] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:42:37] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:42:38] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:42:38] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:42:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 05:42:40] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:42:40] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:49:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 05:49:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:49:34] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:49:34] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:49:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 05:49:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 05:49:37] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:49:38] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:49:38] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:49:38] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:49:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 05:49:40] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:49:40] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:59:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 05:59:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:59:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 05:59:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:59:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:59:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 05:59:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:59:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:59:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 05:59:10] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:59:10] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 05:59:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-29 05:59:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-29 05:59:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 05:59:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-29 05:59:11] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-29 05:59:11] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-29 05:59:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:59:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 05:59:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:00:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:00:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:00:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:00:17] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:00:17] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:00:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:00:18] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:00:18] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:00:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:00:20] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:00:20] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:00:22] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:00:22] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:00:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:00:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:00:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:00:24] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":8}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 06:02:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 06:02:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:02:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-29 06:02:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 06:02:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 06:02:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:02:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:02:43] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:02:43] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:02:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-29 06:02:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-29 06:02:43] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-29 06:02:43] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-29 06:02:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:02:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:02:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:03:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:03:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:03:44] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":8}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 06:03:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-29 06:03:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:03:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:46] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:03:46] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:03:46] [DEBUG] 工程统计信息查询成功 | {"total_tasks":3,"supplying_tasks":0,"completed_tasks":1,"total_feedbacks":0}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-29 06:03:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-29 06:03:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:03:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-29 06:03:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-29 06:04:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:04:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:04:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:04:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:04:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:04:21] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":4012,"task_number":"B05-2200219","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-29 06:04:23] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-29 06:04:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:04:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:04:35] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:04:35] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:12:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:14:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:14:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:14:16] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:14:16] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:18:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:18:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:18:50] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:18:50] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:18:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:18:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:18:53] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:18:53] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:18:54] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:18:55] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:18:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:18:57] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:18:57] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:19:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:19:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:19:12] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:19:12] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:19:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:19:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:19:15] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:19:15] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:19:16] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:19:16] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:19:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:19:18] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:19:18] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:31:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:31:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:31:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:31:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:31:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:31:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:31:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:31:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:34:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:34:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-29 06:34:02] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:34:02] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:34:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:34:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:34:05] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:34:05] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:34:06] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:34:06] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:34:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:34:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-29 06:34:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-29 06:36:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:36:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:36:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:36:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:36:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:36:39] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:39] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:36:39] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:39] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:43] [DEBUG] 工程统计信息查询成功 | {"total_tasks":0,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":0}
[2025-07-29 06:36:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:36:45] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:45] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
开始获取分类反馈单数据，用户ID: 2015160 公司ID: 1004
工程查询结果行数: 0
任务单查询结果行数: 0
用户反馈查询结果行数: 0
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 0
总任务单数量: 0
总反馈数量: 0
[2025-07-29 06:36:54] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015160:1004","ttl":120000}
[2025-07-29 06:36:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:36:55] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:55] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-07-29 06:36:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:36:57] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:57] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:36:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:36:57] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-07-29 06:36:57] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-07-29 06:37:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
[2025-07-29 06:37:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
=== 工程列表API调用 ===
查询公司ID: 1050
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:37:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
[2025-07-29 06:37:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
[2025-07-29 06:37:45] [DEBUG] 项目列表查询成功 | {"totalProjects":908,"filteredProjects":908,"sampleProject":{"id":"B07AJA17026","name":"三宝钢铁电炉改造项目电炉基础","task_count":6}}
[2025-07-29 06:37:45] [DEBUG] 原始表查询成功 | {"totalRows":908,"filteredRows":908,"companyId":"1050"}
[2025-07-29 06:37:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
=== 工程列表API调用 ===
查询公司ID: 1050
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:37:49] [DEBUG] 项目列表查询成功 | {"totalProjects":908,"filteredProjects":908,"sampleProject":{"id":"B07AJA17026","name":"三宝钢铁电炉改造项目电炉基础","task_count":6}}
[2025-07-29 06:37:49] [DEBUG] 原始表查询成功 | {"totalRows":908,"filteredRows":908,"companyId":"1050"}
[2025-07-29 06:37:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
=== 工程列表API调用 ===
查询公司ID: 1050
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-29 06:37:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
=== 工程列表API调用 ===
查询公司ID: 1050
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:37:57] [DEBUG] 项目列表查询成功 | {"totalProjects":908,"filteredProjects":908,"sampleProject":{"id":"B07AJA17026","name":"三宝钢铁电炉改造项目电炉基础","task_count":6}}
[2025-07-29 06:37:57] [DEBUG] 原始表查询成功 | {"totalRows":908,"filteredRows":908,"companyId":"1050"}
[2025-07-29 06:37:59] [DEBUG] 项目列表查询成功 | {"totalProjects":908,"filteredProjects":908,"sampleProject":{"id":"B07AJA17026","name":"三宝钢铁电炉改造项目电炉基础","task_count":6}}
[2025-07-29 06:37:59] [DEBUG] 原始表查询成功 | {"totalRows":908,"filteredRows":908,"companyId":"1050"}
[2025-07-29 06:37:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1050"}
=== 工程列表API调用 ===
查询公司ID: 1050
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-29 06:38:05] [DEBUG] 项目列表查询成功 | {"totalProjects":908,"filteredProjects":908,"sampleProject":{"id":"B07AJA17026","name":"三宝钢铁电炉改造项目电炉基础","task_count":6}}
[2025-07-29 06:38:05] [DEBUG] 原始表查询成功 | {"totalRows":908,"filteredRows":908,"companyId":"1050"}
