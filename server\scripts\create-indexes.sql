-- 创建反馈管理系统所需的数据库索引
-- 这些索引可以提高查询性能

USE NBSTEST;
GO

-- 检查并创建 CU_feedbacks 表的索引

-- 1. 按用户ID和状态查询的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CU_feedbacks_FeedbackUserId_Status')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CU_feedbacks_FeedbackUserId_Status
    ON dbo.CU_feedbacks (FeedbackUserId, Status)
    INCLUDE (Id, TaskNumber, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_CU_feedbacks_FeedbackUserId_Status';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_CU_feedbacks_FeedbackUserId_Status';
END
GO

-- 2. 按任务单号和状态查询的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CU_feedbacks_TaskNumber_Status')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CU_feedbacks_TaskNumber_Status
    ON dbo.CU_feedbacks (TaskNumber, Status)
    INCLUDE (Id, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_CU_feedbacks_TaskNumber_Status';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_CU_feedbacks_TaskNumber_Status';
END
GO

-- 3. 按反馈时间排序的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CU_feedbacks_FeedbackTime')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CU_feedbacks_FeedbackTime
    ON dbo.CU_feedbacks (FeedbackTime DESC)
    WHERE Status = 1
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_CU_feedbacks_FeedbackTime';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_CU_feedbacks_FeedbackTime';
END
GO

-- 检查并创建 CU_feedback_media 表的索引

-- 4. 按反馈ID和状态查询的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CU_feedback_media_FeedbackId_Status')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CU_feedback_media_FeedbackId_Status
    ON dbo.CU_feedback_media (FeedbackId, Status)
    INCLUDE (Id, MediaType, MediaPath, MediaSize, UploadTime)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_CU_feedback_media_FeedbackId_Status';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_CU_feedback_media_FeedbackId_Status';
END
GO

-- 检查并创建其他相关表的索引

-- 5. comPerson 表的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_comPerson_PersonId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_comPerson_PersonId
    ON dbo.comPerson (PersonId)
    INCLUDE (BelongOrgId)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_comPerson_PersonId';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_comPerson_PersonId';
END
GO

-- 6. X_ppProduceOrder 表的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_X_ppProduceOrder_BillNo')
BEGIN
    CREATE NONCLUSTERED INDEX IX_X_ppProduceOrder_BillNo
    ON dbo.X_ppProduceOrder (BillNo)
    INCLUDE (ProjectId, X_JZPart)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_X_ppProduceOrder_BillNo';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_X_ppProduceOrder_BillNo';
END
GO

-- 7. comProject 表的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_comProject_X_OrgId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_comProject_X_OrgId
    ON dbo.comProject (X_OrgId)
    INCLUDE (ProjectId, ProjectName, X_ConsUnitId, X_ProImplement)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON);
    
    PRINT '已创建索引: IX_comProject_X_OrgId';
END
ELSE
BEGIN
    PRINT '索引已存在: IX_comProject_X_OrgId';
END
GO

-- 显示索引创建完成信息
PRINT '========================================';
PRINT '数据库索引创建/检查完成';
PRINT '========================================';

-- 显示当前 CU_feedbacks 表的所有索引
PRINT '当前 CU_feedbacks 表的索引:';
SELECT 
    i.name AS IndexName,
    i.type_desc AS IndexType,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS KeyColumns,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 1
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS IncludedColumns
FROM sys.indexes i
WHERE i.object_id = OBJECT_ID('dbo.CU_feedbacks')
    AND i.type > 0  -- 排除堆
ORDER BY i.name;
GO
