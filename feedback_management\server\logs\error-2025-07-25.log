[2025-07-25 00:29:24] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1013"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:30:39] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1004"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:30:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1005"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:30:54] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1009"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:33:08] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1013"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:34:06] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1014"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:38:56] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1014"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:40:01] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1014"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:40:33] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1014"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 00:41:41] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1014"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-25 01:02:00] [ERROR] API请求失败 | {"method":"GET","url":"/api/files/images/file-1753165273424-632199809.jpg","statusCode":404,"duration":"4ms","memoryDiff":{"rss":57344,"heapUsed":76408,"heapTotal":0,"external":-20970},"timestamp":"2025-07-25T01:02:00.544Z"}
[2025-07-25 01:03:09] [ERROR] API请求失败 | {"method":"GET","url":"/1009","statusCode":404,"duration":"9ms","memoryDiff":{"rss":0,"heapUsed":216768,"heapTotal":0,"external":1604},"timestamp":"2025-07-25T01:03:09.251Z"}
[2025-07-25 01:50:03] [ERROR] API请求失败 | {"method":"POST","url":"/api/auth/login","statusCode":400,"duration":"167ms","memoryDiff":{"rss":917504,"heapUsed":910936,"heapTotal":0,"external":8282},"timestamp":"2025-07-25T01:50:03.241Z"}
[2025-07-25 01:52:24] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":404,"duration":"13ms","memoryDiff":{"rss":237568,"heapUsed":148912,"heapTotal":262144,"external":24},"timestamp":"2025-07-25T01:52:24.154Z"}
[2025-07-25 01:52:24] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":404,"duration":"2ms","memoryDiff":{"rss":36864,"heapUsed":50320,"heapTotal":0,"external":24},"timestamp":"2025-07-25T01:52:24.283Z"}
[2025-07-25 01:56:22] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":404,"duration":"4ms","memoryDiff":{"rss":241664,"heapUsed":111552,"heapTotal":0,"external":24},"timestamp":"2025-07-25T01:56:22.267Z"}
[2025-07-25 03:32:49] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":401,"duration":"4ms","memoryDiff":{"rss":69632,"heapUsed":67696,"heapTotal":0,"external":24},"timestamp":"2025-07-25T03:32:49.422Z"}
[2025-07-25 03:32:49] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":401,"duration":"3ms","memoryDiff":{"rss":65536,"heapUsed":57976,"heapTotal":0,"external":24},"timestamp":"2025-07-25T03:32:49.438Z"}
[2025-07-25 03:33:18] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":401,"duration":"2ms","memoryDiff":{"rss":114688,"heapUsed":55792,"heapTotal":0,"external":24},"timestamp":"2025-07-25T03:33:18.063Z"}
[2025-07-25 03:33:18] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":401,"duration":"5ms","memoryDiff":{"rss":65536,"heapUsed":55464,"heapTotal":0,"external":24},"timestamp":"2025-07-25T03:33:18.075Z"}
[2025-07-25 03:35:06] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":401,"duration":"31ms","memoryDiff":{"rss":0,"heapUsed":160616,"heapTotal":0,"external":48},"timestamp":"2025-07-25T03:35:06.843Z"}
[2025-07-25 05:48:49] [ERROR] API请求失败 | {"method":"GET","url":"/?projectId=1","statusCode":401,"duration":"8ms","memoryDiff":{"rss":159744,"heapUsed":211992,"heapTotal":0,"external":8256},"timestamp":"2025-07-25T05:48:49.464Z"}
[2025-07-25 05:53:20] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"17ms","memoryDiff":{"rss":946176,"heapUsed":914168,"heapTotal":262144,"external":113},"timestamp":"2025-07-25T05:53:20.088Z"}
[2025-07-25 05:53:47] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":401,"duration":"652ms","memoryDiff":{"rss":909312,"heapUsed":197392,"heapTotal":1048576,"external":10406},"timestamp":"2025-07-25T05:53:47.819Z"}
