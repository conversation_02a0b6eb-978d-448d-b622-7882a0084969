//订单模块的request请求
const http = require('../utils/http');
const requestModule = require('../request/common');
const util = require('../utils/util');

//获取维修申请单清单
const GetServerApplyList = (data) => {
    data.FuzzyStr.FixWay=0;
    return new Promise((resolve, reject) => {
        http(requestModule.GetServerApplyList,{data}).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);

        })
    })
}
//获取维修申请单明细
const GetServerApplyDetail = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.GetServerApplyDetail, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

//登打维修申请单接口
const CreatCarServApply = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.CreatCarServApply, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}


//修改维修申请单接口
const UpdateApplyBill = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.UpdateApplyBill, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

//删除维修申请单接口
const DeleteServApply = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.DeleteServApply, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

//获取车辆主数据
const GetCarInfo = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.GetCarInfo, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

//获取维修人员主数据
const GetFixPerson = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.GetFixPerson, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

//维修申请单状态更新/单据审核
const UpdateFixState = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.UpdateFixState, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

//获取报修项目主数据
const GetRepairProject = (data) => {
    return new Promise((resolve, reject) => {
        http(requestModule.GetRepairProject, { data }).then(res => {
            resolve(res);
        }).catch(err => {
            reject(err);
        })
    })
}

module.exports = {
    GetServerApplyList,
    GetServerApplyDetail,
    CreatCarServApply,
    GetCarInfo,
    UpdateApplyBill,
    DeleteServApply,
    UpdateFixState,
    GetRepairProject
}