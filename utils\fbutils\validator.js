/**
 * 数据验证工具类
 * 提供常用的数据验证方法，统一验证逻辑
 */

class Validator {
  /**
   * 验证手机号格式
   * @param {string} phone - 手机号
   * @returns {boolean} 是否有效
   */
  static isValidPhone(phone) {
    if (!phone || typeof phone !== 'string') {
      return false;
    }
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证邮箱格式
   * @param {string} email - 邮箱地址
   * @returns {boolean} 是否有效
   */
  static isValidEmail(email) {
    if (!email || typeof email !== 'string') {
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @param {Object} options - 验证选项
   * @param {number} [options.minLength=6] - 最小长度
   * @param {number} [options.maxLength=20] - 最大长度
   * @param {boolean} [options.requireNumber=false] - 是否需要数字
   * @param {boolean} [options.requireLetter=false] - 是否需要字母
   * @param {boolean} [options.requireSpecial=false] - 是否需要特殊字符
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validatePassword(password, options = {}) {
    const {
      minLength = 6,
      maxLength = 20,
      requireNumber = false,
      requireLetter = false,
      requireSpecial = false
    } = options;

    if (!password || typeof password !== 'string') {
      return { valid: false, message: '密码不能为空' };
    }

    if (password.length < minLength) {
      return { valid: false, message: `密码长度不能少于${minLength}位` };
    }

    if (password.length > maxLength) {
      return { valid: false, message: `密码长度不能超过${maxLength}位` };
    }

    if (requireNumber && !/\d/.test(password)) {
      return { valid: false, message: '密码必须包含数字' };
    }

    if (requireLetter && !/[a-zA-Z]/.test(password)) {
      return { valid: false, message: '密码必须包含字母' };
    }

    if (requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return { valid: false, message: '密码必须包含特殊字符' };
    }

    return { valid: true, message: '密码格式正确' };
  }

  /**
   * 验证用户名格式
   * @param {string} username - 用户名
   * @param {Object} options - 验证选项
   * @param {number} [options.minLength=2] - 最小长度
   * @param {number} [options.maxLength=20] - 最大长度
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validateUsername(username, options = {}) {
    const { minLength = 2, maxLength = 20 } = options;

    if (!username || typeof username !== 'string') {
      return { valid: false, message: '用户名不能为空' };
    }

    const trimmedUsername = username.trim();
    
    if (trimmedUsername.length < minLength) {
      return { valid: false, message: `用户名长度不能少于${minLength}位` };
    }

    if (trimmedUsername.length > maxLength) {
      return { valid: false, message: `用户名长度不能超过${maxLength}位` };
    }

    // 检查是否包含特殊字符（只允许中文、英文、数字、下划线）
    const usernameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(trimmedUsername)) {
      return { valid: false, message: '用户名只能包含中文、英文、数字和下划线' };
    }

    return { valid: true, message: '用户名格式正确' };
  }

  /**
   * 验证身份证号格式
   * @param {string} idCard - 身份证号
   * @returns {boolean} 是否有效
   */
  static isValidIdCard(idCard) {
    if (!idCard || typeof idCard !== 'string') {
      return false;
    }
    
    // 18位身份证号正则
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return idCardRegex.test(idCard);
  }

  /**
   * 验证必填字段
   * @param {Object} data - 要验证的数据对象
   * @param {Array<string>} requiredFields - 必填字段数组
   * @returns {Object} 验证结果 {valid: boolean, message: string, field: string}
   */
  static validateRequired(data, requiredFields) {
    if (!data || typeof data !== 'object') {
      return { valid: false, message: '数据不能为空', field: null };
    }

    for (const field of requiredFields) {
      const value = data[field];
      if (value === undefined || value === null || value === '') {
        return { 
          valid: false, 
          message: `${this.getFieldDisplayName(field)}不能为空`, 
          field 
        };
      }
    }

    return { valid: true, message: '验证通过', field: null };
  }

  /**
   * 获取字段显示名称
   * @param {string} field - 字段名
   * @returns {string} 显示名称
   */
  static getFieldDisplayName(field) {
    const fieldNames = {
      phone: '手机号',
      password: '密码',
      username: '用户名',
      email: '邮箱',
      name: '姓名',
      title: '标题',
      content: '内容',
      company: '公司',
      project: '项目',
      task: '任务'
    };

    return fieldNames[field] || field;
  }

  /**
   * 验证数字范围
   * @param {number} value - 要验证的值
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validateNumberRange(value, min, max) {
    if (typeof value !== 'number' || isNaN(value)) {
      return { valid: false, message: '必须是有效的数字' };
    }

    if (value < min) {
      return { valid: false, message: `值不能小于${min}` };
    }

    if (value > max) {
      return { valid: false, message: `值不能大于${max}` };
    }

    return { valid: true, message: '验证通过' };
  }

  /**
   * 验证字符串长度
   * @param {string} str - 要验证的字符串
   * @param {number} minLength - 最小长度
   * @param {number} maxLength - 最大长度
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validateStringLength(str, minLength, maxLength) {
    if (!str || typeof str !== 'string') {
      return { valid: false, message: '必须是有效的字符串' };
    }

    const length = str.trim().length;

    if (length < minLength) {
      return { valid: false, message: `长度不能少于${minLength}个字符` };
    }

    if (length > maxLength) {
      return { valid: false, message: `长度不能超过${maxLength}个字符` };
    }

    return { valid: true, message: '验证通过' };
  }

  /**
   * 验证文件类型
   * @param {string} fileName - 文件名
   * @param {Array<string>} allowedTypes - 允许的文件类型
   * @returns {boolean} 是否有效
   */
  static isValidFileType(fileName, allowedTypes) {
    if (!fileName || typeof fileName !== 'string') {
      return false;
    }

    const extension = fileName.toLowerCase().split('.').pop();
    return allowedTypes.includes(extension);
  }

  /**
   * 验证文件大小
   * @param {number} fileSize - 文件大小（字节）
   * @param {number} maxSize - 最大大小（字节）
   * @returns {Object} 验证结果 {valid: boolean, message: string}
   */
  static validateFileSize(fileSize, maxSize) {
    if (typeof fileSize !== 'number' || fileSize < 0) {
      return { valid: false, message: '文件大小无效' };
    }

    if (fileSize > maxSize) {
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(1);
      return { valid: false, message: `文件大小不能超过${maxSizeMB}MB` };
    }

    return { valid: true, message: '文件大小符合要求' };
  }
}

module.exports = Validator;
