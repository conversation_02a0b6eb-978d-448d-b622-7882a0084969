/* pages/feedback-detail/feedback-detail.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 任务单信息 */
.task-info {
  background-color: white;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.info-header {
  background: linear-gradient(135deg, #1296DB 0%, #42a5f5 100%);
  padding: 30rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.project-name {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.construction-unit {
  font-size: 26rpx;
  opacity: 0.9;
}

.task-details {
  padding: 30rpx;
}

.task-details text {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333;
}

.task-details .task-status {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.status-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: white;
}

.status-tag.status-supplying {
  background-color: #e3f2fd;
  color: #1296DB;
}

.status-tag.status-completed {
  background-color: #e8f5e8;
  color: #4caf50;
}

/* 信息区域 */
.info-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 4rpx solid #1976d2;
}

.info-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  min-height: 40rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.location-authorized {
  color: #4caf50;
}

.location-denied {
  color: #f44336;
}

.location-unavailable {
  color: #999;
}

.location-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}







/* 备注卡片 */
.notes-card {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border-left: 4rpx solid #ffc107;
}

.notes-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 多媒体区域 */
.media-section {
  margin-bottom: 30rpx;
}

.media-group {
  margin-bottom: 40rpx;
}

.media-group:last-child {
  margin-bottom: 0;
}

.media-group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #555;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  position: relative;
}

.media-group-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #1296DB;
  border-radius: 50%;
}

/* 图片网格 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
}

.image-item {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.media-image {
  width: 100%;
  height: 200rpx;
  display: block;
}

.image-info {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.file-name {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  line-height: 1.4;
}

.file-size {
  font-size: 22rpx;
  color: #999;
}

/* 视频列表 */
.video-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.video-item {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.media-video {
  width: 100%;
  height: 400rpx;
}

.video-info {
  padding: 20rpx;
}

.file-meta {
  display: flex;
  gap: 20rpx;
  margin-top: 8rpx;
}

.file-duration {
  font-size: 22rpx;
  color: #666;
}

/* 音频列表 */
.audio-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.audio-item {
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.audio-player {
  display: flex;
  align-items: center;
  padding: 25rpx;
  gap: 20rpx;
  transition: all 0.3s ease;
}

.audio-player:active {
  background-color: #f8f9fa;
}

.audio-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #1296DB;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.play-icon,
.pause-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.audio-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.audio-status {
  flex-shrink: 0;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

/* 无附件提示 */
.no-media {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.no-media-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.no-media-text {
  font-size: 28rpx;
  color: #999;
}



/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
