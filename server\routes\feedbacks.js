const express = require("express");
const router = express.Router();
const Feedback = require("../models/Feedback");
const Task = require("../models/Task");
const Project = require("../models/Project");
const cacheManager = require("../utils/cache-manager");
const {
  requireAuth,
} = require("../middleware/auth");

// 不需要认证的路由列表
const publicRoutes = ['/test', '/ping', '/health'];

// 全局认证中间件，但排除公共路由
router.use((req, res, next) => {
  // 检查是否是公共路由
  if (publicRoutes.includes(req.path)) {
    return next();
  }
  // 其他路由需要认证
  return requireAuth(req, res, next);
});

// 创建现场信息反馈记录
router.post("/", async (req, res) => {
  try {
    const {
      task_number,
      feedback_time,
      notes,
      category,
      media_files,
      longitude,
      latitude,
      location_desc,
      location_status
    } = req.body;
    const userId = req.session.user.id;
    const companyId = req.session.currentCompany.id;

    if (!task_number || !feedback_time) {
      return res.status(400).json({
        success: false,
        message: "任务单号和反馈时间不能为空",
      });
    }

    // 检查任务单是否存在且属于当前公司（使用新的数据库结构）
    const db = require("../config/database");
    const [taskResult] = await db.execute(
      `
      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId
      FROM dbo.X_ppProduceOrder xpo
      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
      WHERE xpo.BillNo = ? AND cp.X_OrgId = ?
      `,
      [task_number, companyId]
    );

    if (taskResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: "任务单不存在或无权访问",
      });
    }

    // 创建现场信息反馈记录
    const feedbackId = await Feedback.create({
      task_number,
      feedback_user_id: userId,
      feedback_time,
      notes,
      category,
      longitude,
      latitude,
      location_desc,
      location_status
    });

    // 添加多媒体文件
    if (media_files && Array.isArray(media_files)) {
      for (let i = 0; i < media_files.length; i++) {
        const media = media_files[i];
        try {
          await Feedback.addMedia(feedbackId, {
            file_type: media.file_type,
            file_name: media.file_name,
            file_path: media.file_path,
            file_size: media.file_size,
            duration: media.duration,
          });
        } catch (mediaError) {
          // 继续处理其他文件，不中断整个流程
        }
      }
    }

    res.json({
      success: true,
      message: "创建现场信息反馈记录成功",
      data: { feedbackId },
    });
  } catch (error) {
    // 根据错误类型返回不同的错误信息
    let errorMessage = "创建现场信息反馈记录失败";
    let statusCode = 500;

    if (error.code === 'ER_NO_SUCH_TABLE') {
      errorMessage = "数据库表不存在，请联系管理员";
    } else if (error.code === 'ER_BAD_FIELD_ERROR') {
      errorMessage = "数据库字段错误，请联系管理员";
    } else if (error.code === 'ER_DUP_ENTRY') {
      errorMessage = "记录已存在";
      statusCode = 409;
    } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
      errorMessage = "关联数据不存在";
      statusCode = 400;
    }

    res.status(statusCode).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 获取当前用户的所有现场信息反馈记录
router.get("/user", async (req, res) => {
  try {
    const companyId = req.session.currentCompany.id;
    const userId = req.session.user.id;

    const feedbacks = await Feedback.getByUserId(userId, companyId);

    res.json({
      success: true,
      data: feedbacks,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取用户现场信息反馈记录列表失败",
    });
  }
});

// 获取当前用户的分类反馈单数据（按工程和任务单分类）
router.get("/user/grouped", async (req, res) => {
  try {
    // 检查必要的session数据
    if (!req.session?.currentCompany?.id) {
      return res.status(400).json({
        success: false,
        message: "缺少公司信息",
      });
    }

    if (!req.session?.user?.id) {
      return res.status(400).json({
        success: false,
        message: "缺少用户信息",
      });
    }

    const companyId = req.session.currentCompany.id;
    const userId = req.session.user.id;

    // 调用实际的数据库查询方法
    const groupedFeedbacks = await Feedback.getGroupedByUserId(userId, companyId);

    res.json({
      success: true,
      data: groupedFeedbacks,
    });
  } catch (error) {
    console.error("获取分类反馈单列表失败:", error);

    // 检查是否是数据库硬件故障
    if (error.message && error.message.includes('设备未就绪')) {
      return res.status(503).json({
        success: false,
        message: "数据库服务暂时不可用，请联系系统管理员检查硬盘状态",
        error_code: "DATABASE_HARDWARE_ERROR"
      });
    }

    res.status(500).json({
      success: false,
      message: "获取分类反馈单列表失败",
      error_code: "INTERNAL_ERROR",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 获取当前用户的简化反馈单数据（快速加载）
router.get("/user/simple", async (req, res) => {
  try {
    const companyId = req.session.currentCompany.id;
    const userId = req.session.user.id;
    const limit = parseInt(req.query.limit) || 20; // 简化版本默认只返回20条
    const offset = parseInt(req.query.offset) || 0;

    // 直接查询用户的反馈记录，不进行复杂分组
    const feedbacks = await Feedback.getByUserId(userId, companyId, limit, offset);

    res.json({
      success: true,
      data: feedbacks,
    });
  } catch (error) {
    console.error("获取简化反馈单列表失败:", error);

    // 检查是否是数据库硬件故障
    if (error.message && error.message.includes('设备未就绪')) {
      return res.status(503).json({
        success: false,
        message: "数据库服务暂时不可用，请联系系统管理员检查硬盘状态",
        error_code: "DATABASE_HARDWARE_ERROR"
      });
    }

    res.status(500).json({
      success: false,
      message: "获取简化反馈单列表失败",
      error_code: "INTERNAL_ERROR"
    });
  }
});

// 获取缓存统计信息（调试用）
router.get("/cache/stats", requireAuth, async (req, res) => {
  try {
    const stats = cacheManager.getStats();
    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取缓存统计信息失败",
    });
  }
});

// 清除缓存（调试用）
router.post("/cache/clear", requireAuth, async (req, res) => {
  try {
    const { pattern } = req.body;

    if (pattern) {
      cacheManager.invalidatePattern(pattern);
    } else {
      cacheManager.clear();
    }

    res.json({
      success: true,
      message: pattern ? `已清除匹配 ${pattern} 的缓存` : "已清除所有缓存",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "清除缓存失败",
    });
  }
});

// 获取现场信息反馈记录详情
router.get("/:id", requireAuth, async (req, res) => {
  try {
    const feedbackId = req.params.id;
    const companyId = req.session.currentCompany.id;
    const userId = req.session.user.id;



    const feedback = await Feedback.findById(feedbackId);
    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: "现场信息反馈记录不存在",
      });
    }

    // 简化权限检查 - 只检查是否是用户自己的反馈记录
    if (feedback.feedback_user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: "无权访问该现场信息反馈记录",
      });
    }

    // 获取多媒体文件
    let media = [];
    try {
      media = await Feedback.getMedia(feedbackId);
    } catch (mediaError) {
      console.error("获取多媒体文件失败:", mediaError);
      // 如果获取多媒体文件失败，返回空数组，不影响主要功能
      media = [];
    }

    res.json({
      success: true,
      data: {
        feedback,
        media,
      },
    });
  } catch (error) {
    console.error("获取现场信息反馈记录详情失败:", error);

    // 检查是否是数据库连接问题
    if (error.message && error.message.includes('设备未就绪')) {
      return res.status(503).json({
        success: false,
        message: "数据库服务暂时不可用，请稍后重试",
        code: "DATABASE_UNAVAILABLE"
      });
    }

    res.status(500).json({
      success: false,
      message: "获取现场信息反馈记录详情失败",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// 获取任务单的现场信息反馈记录列表
router.get(
  "/task/:taskId",
  requireAuth,
  async (req, res) => {
    try {
      const taskId = req.params.taskId;
      const companyId = req.session.currentCompany.id;

      // 检查任务单是否存在且属于当前公司
      const task = await Task.findById(taskId);
      if (!task) {
        return res.status(404).json({
          success: false,
          message: "任务单不存在",
        });
      }

      const project = await Project.findById(task.project_id);
      if (!project || project.company_id !== companyId) {
        return res.status(403).json({
          success: false,
          message: "无权访问该任务单",
        });
      }

      const feedbacks = await Feedback.getByTaskId(taskId);

      res.json({
        success: true,
        data: feedbacks,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "获取现场信息反馈记录列表失败",
      });
    }
  }
);

// 更新现场信息反馈记录
router.put("/:id", requireAuth, async (req, res) => {
  try {
    const feedbackId = req.params.id;
    const companyId = req.session.currentCompany.id;
    const userId = req.session.user.id;

    // 检查现场信息反馈记录是否存在
    const feedback = await Feedback.findById(feedbackId);
    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: "现场信息反馈记录不存在",
      });
    }

    // 检查权限（只有反馈员本人可以修改）
    if (feedback.feedback_user_id !== userId) {
      return res.status(403).json({
        success: false,
        message: "只能修改自己的现场信息反馈记录",
      });
    }

    const task = await Task.findById(feedback.task_id);
    const project = await Project.findById(task.project_id);
    if (!project || project.company_id !== companyId) {
      return res.status(403).json({
        success: false,
        message: "无权修改该现场信息反馈记录",
      });
    }

    const { feedback_time, notes } = req.body;

    const updated = await Feedback.update(feedbackId, {
      feedback_time,
      notes,
    });

    if (!updated) {
      return res.status(400).json({
        success: false,
        message: "更新现场信息反馈记录失败",
      });
    }

    res.json({
      success: true,
      message: "更新现场信息反馈记录成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "更新现场信息反馈记录失败",
    });
  }
});





// 添加多媒体文件到现场信息反馈记录
router.post(
  "/:id/media",
  requireAuth,
  async (req, res) => {
    try {
      const feedbackId = req.params.id;
      const { file_type, file_name, file_path, file_size, duration } = req.body;
      const userId = req.session.user.id;

      // 检查现场信息反馈记录是否存在
      const feedback = await Feedback.findById(feedbackId);
      if (!feedback) {
        return res.status(404).json({
          success: false,
          message: "现场信息反馈记录不存在",
        });
      }

      // 检查权限
      if (feedback.feedback_user_id !== userId) {
        return res.status(403).json({
          success: false,
          message: "只能为自己的现场信息反馈记录添加文件",
        });
      }

      const mediaId = await Feedback.addMedia(feedbackId, {
        file_type,
        file_name,
        file_path,
        file_size,
        duration,
      });

      res.json({
        success: true,
        message: "添加多媒体文件成功",
        data: { mediaId },
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "添加多媒体文件失败",
      });
    }
  }
);



// 获取现场信息反馈统计信息
router.get(
  "/stats/summary",
  requireAuth,
  async (req, res) => {
    try {
      const companyId = req.session.currentCompany.id;
      const { project_id, date_from, date_to } = req.query;

      const filters = { company_id: companyId };
      if (project_id) filters.project_id = project_id;
      if (date_from) filters.date_from = date_from;
      if (date_to) filters.date_to = date_to;

      const stats = await Feedback.getStats(filters);

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "获取现场信息反馈统计信息失败",
      });
    }
  }
);

// 调试媒体文件接口
router.get("/:id/debug-media", requireAuth, async (req, res) => {
  try {
    const feedbackId = req.params.id;
    const db = require("../config/database");

    console.log(`调试媒体文件 - 反馈ID: ${feedbackId}`);

    // 首先测试基本的数据库连接
    console.log('测试数据库连接...');
    const testResult = await db.execute('SELECT 1 as test');
    console.log('数据库连接测试结果:', testResult);

    // 查询反馈记录是否存在
    console.log('查询反馈记录...');
    const feedbackResult = await db.execute(
      'SELECT Id, TaskNumber, FeedbackUserId FROM CU_feedbacks WHERE Id = ?',
      [feedbackId]
    );
    console.log('反馈记录查询结果:', feedbackResult);

    // 查询所有媒体文件（不过滤状态）
    console.log('查询所有媒体文件...');
    const allMediaResult = await db.execute(
      'SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Status FROM CU_feedback_media WHERE FeedbackId = ?',
      [feedbackId]
    );
    console.log('所有媒体文件查询结果:', allMediaResult);

    // 查询状态为1的媒体文件
    console.log('查询状态为1的媒体文件...');
    const result = await db.execute(
      `SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt
       FROM CU_feedback_media
       WHERE FeedbackId = ? AND Status = 1
       ORDER BY CreatedAt ASC`,
      [feedbackId]
    );

    console.log(`SQL查询原始结果:`, result);
    const mediaResult = result[0] || [];

    console.log(`数据库查询结果:`, mediaResult);

    // 检查文件是否存在
    const fs = require('fs');
    const path = require('path');

    const mediaWithFileCheck = mediaResult.map(item => {
      const filePath = item.FilePath;
      let fileExists = false;
      let fullPath = '';

      if (filePath) {
        // 尝试不同的路径组合
        const possiblePaths = [
          filePath,
          path.join(__dirname, '..', filePath),
          path.join(__dirname, '..', 'upload', filePath),
          path.join(__dirname, '..', filePath.replace('/api/files/', 'upload/')),
        ];

        for (const testPath of possiblePaths) {
          if (fs.existsSync(testPath)) {
            fileExists = true;
            fullPath = testPath;
            break;
          }
        }
      }

      return {
        ...item,
        fileExists,
        fullPath,
        possiblePaths: [
          filePath,
          path.join(__dirname, '..', filePath),
          path.join(__dirname, '..', 'upload', filePath),
          path.join(__dirname, '..', filePath.replace('/api/files/', 'upload/')),
        ]
      };
    });

    res.json({
      success: true,
      data: {
        feedbackId,
        mediaCount: mediaResult.length,
        mediaFiles: mediaWithFileCheck,
        uploadDir: path.join(__dirname, '..', 'upload'),
        currentDir: __dirname
      }
    });

  } catch (error) {
    console.error("调试媒体文件失败:", error);
    res.status(500).json({
      success: false,
      message: "调试失败",
      error: error.message
    });
  }
});

// 测试数据库表结构
router.get("/test-db-structure", async (req, res) => {
  try {
    const db = require("../config/database");

    // 检查CU_feedback_media表结构
    const tableStructure = await db.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'CU_feedback_media'
      ORDER BY ORDINAL_POSITION
    `);

    // 检查表中的数据总数
    const countResult = await db.execute('SELECT COUNT(*) as total_count FROM CU_feedback_media');

    // 检查最近的几条记录
    const recentRecords = await db.execute(`
      SELECT TOP 5 Id, FeedbackId, MediaType, FileName, FilePath, Status, CreatedAt
      FROM CU_feedback_media
      ORDER BY CreatedAt DESC
    `);

    res.json({
      success: true,
      data: {
        tableStructure: tableStructure[0],
        totalCount: countResult[0],
        recentRecords: recentRecords[0]
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "数据库测试失败",
      error: error.message
    });
  }
});

// 测试接口 - 不需要认证
router.get("/test", (req, res) => {
  res.json({
    success: true,
    message: "测试接口正常",
    timestamp: new Date().toISOString()
  });
});

// 简单媒体文件测试 - 不需要认证
router.get("/test-media/:id", async (req, res) => {
  try {
    const feedbackId = req.params.id;
    const db = require("../config/database");

    console.log(`=== 开始测试媒体文件 - 反馈ID: ${feedbackId} ===`);

    // 1. 测试基本数据库连接
    console.log('1. 测试数据库连接...');
    const testResult = await db.execute('SELECT 1 as test');
    console.log('数据库连接结果:', testResult);

    // 2. 检查反馈记录是否存在
    console.log('2. 检查反馈记录...');
    const feedbackCheck = await db.execute(
      'SELECT Id, TaskNumber FROM CU_feedbacks WHERE Id = ?',
      [feedbackId]
    );
    console.log('反馈记录查询结果:', feedbackCheck);

    // 3. 查询媒体文件（不过滤状态）
    console.log('3. 查询所有媒体文件...');
    const allMedia = await db.execute(
      'SELECT * FROM CU_feedback_media WHERE FeedbackId = ?',
      [feedbackId]
    );
    console.log('所有媒体文件:', allMedia);

    // 4. 查询状态为1的媒体文件
    console.log('4. 查询状态为1的媒体文件...');
    const activeMedia = await db.execute(
      'SELECT * FROM CU_feedback_media WHERE FeedbackId = ? AND Status = 1',
      [feedbackId]
    );
    console.log('状态为1的媒体文件:', activeMedia);

    res.json({
      success: true,
      data: {
        feedbackId,
        databaseTest: testResult,
        feedbackExists: feedbackCheck[0]?.length > 0,
        feedbackData: feedbackCheck[0],
        allMediaCount: allMedia[0]?.length || 0,
        allMediaData: allMedia[0],
        activeMediaCount: activeMedia[0]?.length || 0,
        activeMediaData: activeMedia[0]
      }
    });

  } catch (error) {
    console.error('媒体文件测试失败:', error);
    res.status(500).json({
      success: false,
      message: "测试失败",
      error: error.message,
      stack: error.stack
    });
  }
});

// 简单测试接口 - 不需要认证
router.get("/ping", (req, res) => {
  res.json({
    success: true,
    message: "pong",
    timestamp: new Date().toISOString()
  });
});

// 数据库健康检查接口 - 不需要认证
router.get("/health", async (req, res) => {
  try {
    const db = require("../config/database");

    // 简单的数据库连接测试
    const result = await db.execute("SELECT 1 as test, GETDATE() as server_time");

    res.json({
      success: true,
      message: "数据库连接正常",
      server_time: result[0][0].server_time,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("数据库健康检查失败:", error);

    let errorCode = "DATABASE_ERROR";
    let message = "数据库连接失败";

    if (error.message && error.message.includes('设备未就绪')) {
      errorCode = "DATABASE_HARDWARE_ERROR";
      message = "检测到数据库硬件故障，请联系系统管理员";
    }

    res.status(503).json({
      success: false,
      message: message,
      error_code: errorCode,
      timestamp: new Date().toISOString()
    });
  }
});

// 获取当前用户的现场信息反馈统计信息
router.get(
  "/stats/user",
  requireAuth,
  async (req, res) => {
    try {
      const userId = req.session.user.id;
      const companyId = req.session.currentCompany.id;

      // 查询用户的现场信息反馈统计信息
      const db = require("../config/database");
      const statsQuery = `
        SELECT
          COUNT(DISTINCT f.Id) as total_feedbacks,
          COUNT(DISTINCT f.TaskNumber) as feedback_tasks,
          COUNT(DISTINCT cp.ProjectId) as feedback_projects
        FROM dbo.CU_feedbacks f
        LEFT JOIN dbo.X_ppProduceOrder xpo ON f.TaskNumber = xpo.BillNo
        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
        WHERE f.FeedbackUserId = ? AND f.Status = 1 AND cp.X_OrgId = ?
      `;

      const result = await db.execute(statsQuery, [userId, companyId]);

      const stats = {
        total_feedbacks: result[0][0]?.total_feedbacks || 0,
        feedback_tasks: result[0][0]?.feedback_tasks || 0,
        feedback_projects: result[0][0]?.feedback_projects || 0
      };

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error("获取用户现场信息反馈统计信息失败:", error);
      res.status(500).json({
        success: false,
        message: "获取用户现场信息反馈统计信息失败",
      });
    }
  }
);





module.exports = router;
