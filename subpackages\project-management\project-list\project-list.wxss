/* project-list.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部 */
.header {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 筛选栏 */
.filter-bar {
  background-color: white;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  box-sizing: border-box;
}

.search-btn {
  padding: 20rpx 30rpx;
  background-color: #1296DB;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 工程列表 */
.project-list {
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.project-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.project-item:last-child {
  border-bottom: none;
}

.project-info {
  flex: 1;
  cursor: pointer;
}

.project-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.project-title {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.project-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.project-code {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
  border: 1rpx solid #e0e0e0;
}



.project-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.project-company,
.construction-unit {
  font-size: 24rpx;
  color: #666;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}

.task-count,
.created-time {
  font-size: 22rpx;
  color: #999;
  padding-right: 100rpx;
}




.btn-small {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}



/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background-color: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12rpx;
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 30rpx;
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.modal-warning {
  font-size: 24rpx;
  color: #f44336;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-footer .btn {
  flex: 1;
  border-radius: 0;
  border-right: 1rpx solid #eee;
}

.modal-footer .btn:last-child {
  border-right: none;
}

/* 浮动按钮 */
.fab {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #1976d2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(25, 118, 210, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.fab:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 8rpx rgba(25, 118, 210, 0.3);
}

.fab-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: 300;
}


