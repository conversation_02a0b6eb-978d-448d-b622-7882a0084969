/**
 * 天地图地理编码工具类
 * 统一的地理编码解决方案，包含逆地理编码、位置获取等功能
 * API文档：http://lbs.tianditu.gov.cn/server/geocoding.html
 */

const CONFIG = require('../config/index.js');
const coordinateConverter = require('./coordinate-converter.js');

/**
 * 天地图逆地理编码
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @param {object} options 配置选项
 * @returns {Promise<object>} 逆地理编码结果
 */
function tiandituReverseGeocode(latitude, longitude, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      showProgress = true,
      useCache = true,
      cacheExpireHours = 24,
      coordinateType = CONFIG.MAP.coordinateSystem.tiandituCoordType || 'auto' // 'auto', 'gcj02', 'wgs84'
    } = options;

    // 参数验证
    if (!latitude || !longitude) {
      reject(new Error('经纬度参数不能为空'));
      return;
    }

    // 检查是否使用服务端代理
    const useProxy = CONFIG.MAP.tiandituUseProxy || false;

    // 如果不使用代理，则需要检查API密钥
    if (!useProxy && (!CONFIG.MAP.tiandituKey || CONFIG.MAP.tiandituKey === 'YOUR_TIANDITU_API_KEY_HERE')) {
      reject(new Error('天地图API密钥未配置，请配置密钥或启用服务端代理模式'));
      return;
    }

    // 检查缓存
    if (useCache) {
      const cacheKey = `tianditu_geocode_${latitude.toFixed(4)}_${longitude.toFixed(4)}`;
      const cached = wx.getStorageSync(cacheKey);
      
      if (cached && cached.timestamp) {
        const now = Date.now();
        const cacheAge = (now - cached.timestamp) / (1000 * 60 * 60); // 小时
        
        if (cacheAge < cacheExpireHours) {
          console.log('使用天地图缓存结果:', cached.data);
          resolve({
            success: true,
            source: 'tianditu_cache',
            data: cached.data,
            formatted_address: cached.data.formatted_address
          });
          return;
        }
      }
    }

    if (showProgress) {
      wx.showLoading({ title: '获取位置信息...' });
    }

    // 坐标转换：根据配置决定是否转换坐标系
    let apiLongitude = longitude;
    let apiLatitude = latitude;
    let coordTypeUsed = 'original';

    if (coordinateType === 'wgs84') {
      // 强制转换为WGS84坐标系
      const wgs84Coord = coordinateConverter.prepareForTianditu(longitude, latitude);
      apiLongitude = wgs84Coord.lng;
      apiLatitude = wgs84Coord.lat;
      coordTypeUsed = 'wgs84';
    } else if (coordinateType === 'auto') {
      // 自动模式：先尝试原始坐标，如果在中国境内且可能是GCJ02，则考虑转换
      if (coordinateConverter.isInChina(longitude, latitude)) {
        // 在中国境内，微信小程序默认是GCJ02，可以尝试转换为WGS84
        // 这里暂时保持原始坐标，让用户通过诊断工具来判断
        coordTypeUsed = 'gcj02_assumed';
      }
    }

    console.log(`坐标转换: 原始坐标(${latitude}, ${longitude}) -> API坐标(${apiLatitude}, ${apiLongitude}), 坐标系: ${coordTypeUsed}`);

    // 构建请求参数
    const postStr = JSON.stringify({
      lon: apiLongitude,
      lat: apiLatitude,
      ver: 1
    });

    if (useProxy) {
      // 通过服务端代理访问天地图API
      wx.request({
        url: `${CONFIG.SERVER_URL}/api/geocoding/tianditu`,
        method: 'POST',
        data: {
          latitude: latitude,
          longitude: longitude
        },
        success(res) {
          if (showProgress) {
            wx.hideLoading();
          }

          console.log('天地图代理响应:', res);

          if (res.statusCode === 200 && res.data.success) {
            const result = res.data.data;

            // 缓存结果
            if (useCache) {
              const cacheKey = `tianditu_geocode_${latitude.toFixed(4)}_${longitude.toFixed(4)}`;
              wx.setStorageSync(cacheKey, {
                data: result,
                timestamp: Date.now()
              });
            }

            resolve({
              success: true,
              source: 'tianditu_proxy',
              data: result,
              formatted_address: result.formatted_address
            });
          } else {
            const errorMsg = res.data.message || '服务端代理请求失败';
            console.error('天地图代理错误:', res);
            reject(new Error(`代理请求失败: ${errorMsg}`));
          }
        },
        fail(err) {
          if (showProgress) {
            wx.hideLoading();
          }

          console.error('天地图代理网络请求失败:', err);
          reject(new Error(`代理网络请求失败: ${err.errMsg || '未知错误'}`));
        }
      });
    } else {
      // 直接访问天地图API
      wx.request({
        url: CONFIG.MAP.tiandituGeocoderUrl,
        method: 'GET',
        data: {
          postStr: postStr,
          type: 'geocode',
          tk: CONFIG.MAP.tiandituKey
        },
        success(res) {
          if (showProgress) {
            wx.hideLoading();
          }

          console.log('天地图逆地理编码响应:', res);

          if (res.statusCode === 200) {
            const responseData = res.data;

            if (responseData.status === '0' && responseData.result) {
              // 成功获取结果
              const result = responseData.result;

              // 缓存结果
              if (useCache) {
                const cacheKey = `tianditu_geocode_${latitude.toFixed(4)}_${longitude.toFixed(4)}`;
                wx.setStorageSync(cacheKey, {
                  data: result,
                  timestamp: Date.now()
                });
              }

              resolve({
                success: true,
                source: 'tianditu_api',
                data: result,
                formatted_address: result.formatted_address
              });
            } else {
              // API返回错误
              const errorMsg = responseData.msg || '天地图API返回错误';
              console.error('天地图API错误:', responseData);
              reject(new Error(`天地图API错误: ${errorMsg} (状态码: ${responseData.status})`));
            }
          } else {
            // HTTP状态码错误
            console.error('天地图API请求失败:', res);
            reject(new Error(`HTTP请求失败: ${res.statusCode}`));
          }
        },
        fail(err) {
          if (showProgress) {
            wx.hideLoading();
          }

          console.error('天地图API网络请求失败:', err);
          reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`));
        }
      });
    }
  });
}

/**
 * 格式化天地图地址信息
 * @param {object} result 天地图API返回的结果
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @returns {string} 格式化后的地址描述
 */
function formatTiandituAddress(result, latitude, longitude) {
  if (!result) {
    return `位置: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
  }

  let address = result.formatted_address || '';
  
  // 如果有详细的地址组件信息
  if (result.addressComponent) {
    const component = result.addressComponent;
    
    // 添加POI信息
    if (component.poi && component.poi_distance) {
      const distance = parseInt(component.poi_distance);
      if (distance < 100) {
        address += ` (${component.poi})`;
      } else {
        address += ` (附近${distance}米: ${component.poi})`;
      }
    }
    
    // 添加道路信息
    if (component.road && component.road_distance) {
      const roadDistance = parseInt(component.road_distance);
      if (roadDistance < 50) {
        address += ` [${component.road}]`;
      }
    }
  }
  
  // 添加精确坐标
  address += ` (${latitude.toFixed(6)}, ${longitude.toFixed(6)})`;
  
  return address;
}

/**
 * 天地图逆地理编码（带格式化）
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @param {object} options 配置选项
 * @returns {Promise<object>} 包含格式化地址的结果
 */
function tiandituReverseGeocodeFormatted(latitude, longitude, options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      const result = await tiandituReverseGeocode(latitude, longitude, options);
      
      if (result.success) {
        const formattedAddress = formatTiandituAddress(result.data, latitude, longitude);
        
        resolve({
          success: true,
          source: result.source,
          address: formattedAddress,
          rawData: result.data,
          formatted_address: result.data.formatted_address
        });
      } else {
        reject(new Error('天地图逆地理编码失败'));
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 检查天地图API是否可用
 * @returns {Promise<boolean>} API是否可用
 */
function checkTiandituAPIAvailability() {
  return new Promise((resolve) => {
    // 检查API密钥是否配置
    if (!CONFIG.MAP.tiandituKey || CONFIG.MAP.tiandituKey === 'YOUR_TIANDITU_API_KEY_HERE') {
      resolve(false);
      return;
    }

    // 使用北京天安门的坐标进行测试
    const testLat = 39.9042;
    const testLon = 116.4074;
    
    tiandituReverseGeocode(testLat, testLon, { 
      showProgress: false, 
      useCache: false 
    })
    .then(() => {
      resolve(true);
    })
    .catch(() => {
      resolve(false);
    });
  });
}

/**
 * 获取当前位置
 * @returns {Promise<object>} 位置信息对象
 */
function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    wx.getLocation({
      type: 'gcj02', // 使用国测局坐标系
      success(res) {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          altitude: res.altitude,
          verticalAccuracy: res.verticalAccuracy,
          horizontalAccuracy: res.horizontalAccuracy,
          speed: res.speed
        });
      },
      fail(err) {
        reject(err);
      }
    });
  });
}

/**
 * 简化的地理编码接口 - 直接返回地址字符串
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @returns {Promise<string>} 地址描述
 */
function reverseGeocode(latitude, longitude) {
  return tiandituReverseGeocodeFormatted(latitude, longitude)
    .then(result => {
      if (result.success) {
        return result.address;
      } else {
        throw new Error('天地图逆地理编码失败');
      }
    })
    .catch(error => {
      console.error('天地图逆地理编码失败:', error);
      // 失败时使用经纬度作为备选
      return `纬度: ${latitude.toFixed(6)}, 经度: ${longitude.toFixed(6)}`;
    });
}

/**
 * 智能地理编码 - 带缓存和进度显示的地理编码
 * @param {number} latitude 纬度
 * @param {number} longitude 经度
 * @param {object} options 选项
 * @returns {Promise<object>} 地理编码结果
 */
function smartGeocode(latitude, longitude, options = {}) {
  const {
    useCache = true,         // 是否使用缓存
    showProgress = true      // 是否显示进度
  } = options;

  return tiandituReverseGeocodeFormatted(latitude, longitude, {
    useCache,
    showProgress
  });
}

/**
 * 获取推荐的地理编码配置
 * @returns {Promise<object>} 推荐配置
 */
async function getRecommendedConfig() {
  const apiAvailable = await checkTiandituAPIAvailability();

  return {
    useTiandituAPI: apiAvailable,
    reason: apiAvailable ? '天地图API可用' : '天地图API不可用'
  };
}

module.exports = {
  // 核心功能
  tiandituReverseGeocode,
  tiandituReverseGeocodeFormatted,
  formatTiandituAddress,
  checkTiandituAPIAvailability,

  // 简化接口
  reverseGeocode,
  getCurrentLocation,
  smartGeocode,
  getRecommendedConfig,

  // 兼容性别名
  tiandituGeocode: smartGeocode
};
