// pages/orderUpdate/orderUpdate.js
const requestOrder = require('../../request/order');
const utils = require('../../utils/util');
const config = require('../../config/common');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        CurrentBillNo: '',
        FromDetail: {},
        ShowDetail: {},
        RepairListId: [],
        RepairList: [],
        index: ''
    },
    findRepairProject(){
        let data = {
            ProgId: "X_CarServApply",
        }
        requestOrder.GetRepairProject(data).then(res => {
            if (res.data.status == "suc") {
                console.log('list', res.data.data);
                let templist = [];
                let templistId =[];
                let tempindex='';
                res.data.data.forEach((item,index) => {
                    if(this.data.FromDetail.repairProjectDetail[0].RepairProjectId==item.RepairProjectId)
                    {
                        tempindex=index
                    }
                    templistId.push(item.RepairProjectId)
                    templist.push(item.RepairProjectName)
                })
                this.setData({
                    RepairListId:templistId,
                    RepairList: templist,
                    index:tempindex
                })
            }
        })
    },
    bindPickerValue: function (e) {
        this.setData({
            index: e.detail.value
        })
    },
    cancleOrder() {
        wx.navigateBack();
    },
    formSubmit(e) {
        if (!this.data.FromDetail.CarName) {
            wx.showToast({
                icon: 'error',
                title: '找不到该车辆！',
            })
        }
        else if (!e.detail.value.TotalMileage) {
            wx.showModal({
                showCancel: false,
                title: '警告',
                content: '请输入累计行驶公里数！',
                success(res) {
                    if (res.confirm) {
                        console.log('用户点击确定')
                    } else if (res.cancel) {
                        console.log('用户点击取消')
                    }
                }
            })
        }
        else if (e.detail.value.TotalMileage < this.data.FromDetail.OldTotalMileage) {
            wx.showModal({
                showCancel: false,
                title: '警告',
                content: '累计行驶公里数小于车辆主数据公里数，请重新输入！',
                success(res) {
                    if (res.confirm) {
                        console.log('用户点击确定')
                    } else if (res.cancel) {
                        console.log('用户点击取消')
                    }
                }
            })
        }
        else {
            let repairProjectDetail1=[];
            let Tag1=0;
            if(this.data.index>=0)
            {
                if(this.data.FromDetail.repairProjectDetail)
                {
                    Tag1=1;
                }
                repairProjectDetail1=[{
                    RowCode: 1,
                    Tag:Tag1,
                    RepairProjectId:this.data.RepairListId[this.data.index],          
                }]
                console.log('1111',repairProjectDetail1);
            }
            let data = {
                ProgId: "X_CarServApply",
                BillNo: this.data.CurrentBillNo,
                master: {
                    BillNo: this.data.CurrentBillNo,
                    // BillDate:utils.formatDate(new Date()),
                    // OrgId:this.data.mainCompanyId,
                    // TypeId:"10",
                    // CompId:this.data.mainCompanyId,RepairProjectId
                    ServReason: e.detail.value.FixReason,
                    // Proposer:this.data.userId,
                    // FixWay:0,
                    CarId: this.data.FromDetail.CarId,
                    CarName: this.data.FromDetail.CarName,
                    CarMark: e.detail.value.CarMark,
                    TotalMileage: e.detail.value.TotalMileage
                },
                repairProjectsDetail: repairProjectDetail1
            }
            requestOrder.UpdateApplyBill(data).then(res => {
                if (res.data.status == "suc") {
                    wx.showModal({
                        showCancel: false,
                        title: '修改成功',
                        content: res.data.data,
                        complete: (rres) => {
                            if (rres.confirm) {
                                wx.switchTab({
                                    url: '../order/order',
                                })
                            }
                        }
                    })
                }
                else {
                    wx.showModal({
                        showCancel: false,
                        title: '修改失败',
                        content: res.data.data,
                    })
                }
            }).catch(err => {
                console.log("失败", err);
            })
        }

    },
    outCarMark(e) {
        if (e.detail.value && e.detail.value != 0) {
            let data = { FuzzyStr: { CarMark: e.detail.value, CompId: this.data.mainCompanyId } };
            requestOrder.GetCarInfo(data).then(res => {
                if (res.data.status == "suc") {
                    this.setData({
                        'FromDetail.CarId': res.data.data[0].CarId,
                        'FromDetail.CarName': res.data.data[0].CarName,
                        "FromDetail.OldTotalMileage": res.data.data[0].TotalMileage
                    })
                }
                else {
                    this.setData({
                        FromDetail: {}
                    })
                }
            }).catch(err => {
                console.log("失败", err);
            })
        }

    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.data.CurrentBillNo = options.BillNo;
        requestOrder.GetServerApplyDetail({ ProgId: 'X_CarServApply', BillNo: this.data.CurrentBillNo }).then(res => {
            if (res.data.status == 'suc') {
                let tempDetail = {};
                tempDetail.BillNo = res.data.data.BillNo;
                tempDetail.BillDate = res.data.data.BillDate;
                tempDetail.CompName = res.data.data.CompName;
                tempDetail.CarName = res.data.data.CarName;
                tempDetail.TotalMileage = res.data.data.TotalMileage;
                tempDetail.ServReason = res.data.data.ServReason;
                tempDetail.ProposerName = res.data.data.ProposerName;
                tempDetail.ProposeDeptName = res.data.data.ProposeDeptName;
                tempDetail.FixState = res.data.data.FixState;
                if (tempDetail.FixState == 0) { tempDetail.FixStateName = "申报中"; }
                if (tempDetail.FixState == 1) { tempDetail.FixStateName = "待修中"; }
                if (tempDetail.FixState == 2) { tempDetail.FixStateName = "维修中"; }
                if (tempDetail.FixState == 3) { tempDetail.FixStateName = "维修完成"; }
                if (tempDetail.FixState == 4) { tempDetail.FixStateName = "已验收"; }
                if (res.data.data.PermitState == 0) { tempDetail.PermitStateName = "未审核"; }
                if (res.data.data.PermitState == 1) { tempDetail.PermitStateName = "审核中"; }
                if (res.data.data.PermitState == 2) { tempDetail.PermitStateName = "已审核"; }
                tempDetail.FixPersonName = res.data.data.FixPersonName;
                tempDetail.CarMark = res.data.data.CarMark;
                tempDetail.repairProjectDetail = res.data.data.repairProjectDetail;
                this.setData({
                    FromDetail: tempDetail
                })
                this.findRepairProject();
            }
            else {
                console.log('找不到单据');
            }
        }).catch(err => {
            console.log('detail-err', err);
        })
        
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})