/**
 * 环境检测和诊断工具类
 * 提供环境信息检测、服务器配置检查等功能
 */

class EnvironmentHelper {
  /**
   * 获取环境诊断信息
   * @returns {Object} 诊断信息
   */
  static getDiagnosticInfo() {
    // 使用新的API替代已废弃的wx.getSystemInfoSync
    const deviceInfo = wx.getDeviceInfo();
    const appBaseInfo = wx.getAppBaseInfo();
    const accountInfo = wx.getAccountInfoSync();

    return {
      environment: {
        isRealDevice: deviceInfo.platform !== 'devtools',
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        version: appBaseInfo.version,
        isIOS: deviceInfo.system.toLowerCase().includes('ios'),
        isAndroid: deviceInfo.system.toLowerCase().includes('android'),
        isDevelopment: accountInfo.miniProgram.envVersion === 'develop',
        isProduction: accountInfo.miniProgram.envVersion === 'release'
      },
      config: {
        serverUrl: this.getServerUrl(),
        apiBaseUrl: this.getApiBaseUrl()
      },
      network: {
        networkType: 'unknown' // 网络类型需要异步获取
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取服务器URL
   * @returns {string} 服务器URL
   */
  static getServerUrl() {
    // 根据环境返回不同的服务器地址
    const deviceInfo = wx.getDeviceInfo();
    const accountInfo = wx.getAccountInfoSync();

    // 开发环境
    if (accountInfo.miniProgram.envVersion === 'develop' || deviceInfo.platform === 'devtools') {
      return 'http://localhost:8080';
    }

    // 测试环境
    if (accountInfo.miniProgram.envVersion === 'trial') {
      return 'http://localhost:8080';
    }
  }

  /**
   * 获取API基础URL
   * @returns {string} API基础URL
   */
  static getApiBaseUrl() {
    return this.getServerUrl() + '/api';
  }

  /**
   * 获取服务器配置信息
   * @returns {Object} 服务器配置
   */
  static getServerConfig() {
    const serverUrl = this.getServerUrl();
    const diagnosticInfo = this.getDiagnosticInfo();
    
    // 检查是否使用内网地址
    const isInternalIP = serverUrl.includes('192.168.') || 
                        serverUrl.includes('10.') || 
                        serverUrl.includes('172.') ||
                        serverUrl.includes('localhost') ||
                        serverUrl.includes('127.0.0.1');
    
    let warning = null;
    let isAccessible = true;
    
    // 在真机环境下使用内网地址时给出警告
    if (diagnosticInfo.environment.isRealDevice && isInternalIP) {
      warning = '当前使用内网地址，请确保手机和服务器在同一网络环境下';
      isAccessible = false; // 可能无法访问
    }
    
    return {
      serverUrl,
      isInternalIP,
      isAccessible,
      warning
    };
  }

  /**
   * 检查功能支持情况
   * @param {string} feature - 功能名称
   * @returns {Object} 支持情况
   */
  static checkFeatureSupport(feature) {
    const diagnosticInfo = this.getDiagnosticInfo();
    
    switch (feature) {
      case 'image_preview':
        return this.checkImagePreviewSupport(diagnosticInfo);
      case 'video_record':
        return this.checkVideoRecordSupport(diagnosticInfo);
      case 'audio_record':
        return this.checkAudioRecordSupport(diagnosticInfo);
      case 'camera':
        return this.checkCameraSupport(diagnosticInfo);
      default:
        return {
          accessible: true,
          warning: null
        };
    }
  }

  /**
   * 检查图片预览支持
   * @param {Object} diagnosticInfo - 诊断信息
   * @returns {Object} 支持情况
   */
  static checkImagePreviewSupport(diagnosticInfo) {
    const serverConfig = this.getServerConfig();
    
    if (diagnosticInfo.environment.isRealDevice && !serverConfig.isAccessible) {
      return {
        accessible: false,
        warning: '真机环境下无法访问内网服务器，图片预览可能失败'
      };
    }
    
    return {
      accessible: true,
      warning: null
    };
  }

  /**
   * 检查视频录制支持
   * @param {Object} diagnosticInfo - 诊断信息
   * @returns {Object} 支持情况
   */
  static checkVideoRecordSupport(diagnosticInfo) {
    // 检查平台支持
    if (diagnosticInfo.environment.platform === 'devtools') {
      return {
        accessible: true,
        warning: '开发工具环境，视频格式可能为WebM'
      };
    }
    
    return {
      accessible: true,
      warning: null
    };
  }

  /**
   * 检查音频录制支持
   * @param {Object} diagnosticInfo - 诊断信息
   * @returns {Object} 支持情况
   */
  static checkAudioRecordSupport(diagnosticInfo) {
    return {
      accessible: true,
      warning: null
    };
  }

  /**
   * 检查相机支持
   * @param {Object} diagnosticInfo - 诊断信息
   * @returns {Object} 支持情况
   */
  static checkCameraSupport(diagnosticInfo) {
    if (diagnosticInfo.environment.platform === 'devtools') {
      return {
        accessible: true,
        warning: '开发工具环境，相机功能可能受限'
      };
    }
    
    return {
      accessible: true,
      warning: null
    };
  }

  /**
   * 获取网络状态信息
   * @returns {Promise<Object>} 网络状态
   */
  static getNetworkInfo() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            networkType: res.networkType,
            isConnected: res.networkType !== 'none'
          });
        },
        fail: () => {
          resolve({
            networkType: 'unknown',
            isConnected: false
          });
        }
      });
    });
  }

  /**
   * 生成环境报告
   * @returns {string} 环境报告文本
   */
  static generateEnvironmentReport() {
    const diagnosticInfo = this.getDiagnosticInfo();
    const serverConfig = this.getServerConfig();
    
    let report = '=== 环境诊断报告 ===\n';
    report += `运行环境: ${diagnosticInfo.environment.isRealDevice ? '真机' : '开发工具'}\n`;
    report += `平台: ${diagnosticInfo.environment.platform}\n`;
    report += `系统: ${diagnosticInfo.environment.system}\n`;
    report += `版本: ${diagnosticInfo.environment.version}\n`;
    report += `网络类型: ${diagnosticInfo.network.networkType}\n`;
    report += `服务器地址: ${serverConfig.serverUrl}\n`;
    
    if (serverConfig.warning) {
      report += `\n⚠️ 警告: ${serverConfig.warning}\n`;
    }
    
    report += `\n检查时间: ${diagnosticInfo.timestamp}`;
    
    return report;
  }

  /**
   * 检查是否为开发环境
   * @returns {boolean} 是否为开发环境
   */
  static isDevelopmentEnvironment() {
    const deviceInfo = wx.getDeviceInfo();
    const accountInfo = wx.getAccountInfoSync();

    return deviceInfo.platform === 'devtools' ||
           accountInfo.miniProgram.envVersion === 'develop';
  }

  /**
   * 检查是否为生产环境
   * @returns {boolean} 是否为生产环境
   */
  static isProductionEnvironment() {
    const accountInfo = wx.getAccountInfoSync();
    return accountInfo.miniProgram.envVersion === 'release';
  }
}

module.exports = EnvironmentHelper;
