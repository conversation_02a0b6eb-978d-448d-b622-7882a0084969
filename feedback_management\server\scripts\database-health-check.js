/**
 * 数据库健康检查脚本
 * 用于检查SQL Server数据库的健康状态和硬件问题
 */

const sql = require('mssql');
const config = require('../config');
const logger = require('../utils/logger');

// 数据库配置
const dbConfig = {
  server: config.get('sqlserver.server'),
  port: config.get('sqlserver.port'),
  user: config.get('sqlserver.user'),
  password: config.get('sqlserver.password'),
  database: config.get('sqlserver.database'),
  options: {
    encrypt: config.get('sqlserver.encrypt', false),
    trustServerCertificate: config.get('sqlserver.trustServerCertificate', true),
    enableArithAbort: true,
    requestTimeout: 60000, // 增加超时时间
    connectionTimeout: 30000
  }
};

/**
 * 检查数据库连接
 */
async function checkConnection() {
  console.log('🔍 检查数据库连接...');
  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool.request().query('SELECT 1 as test, GETDATE() as current_time');
    console.log('✅ 数据库连接正常');
    console.log(`   服务器时间: ${result.recordset[0].current_time}`);
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

/**
 * 检查数据库完整性
 */
async function checkDatabaseIntegrity() {
  console.log('🔍 检查数据库完整性...');
  try {
    const pool = await sql.connect(dbConfig);
    
    // 运行 DBCC CHECKDB 检查数据库完整性
    console.log('   正在运行 DBCC CHECKDB，这可能需要几分钟...');
    const result = await pool.request()
      .query(`DBCC CHECKDB('${dbConfig.database}') WITH NO_INFOMSGS`);
    
    console.log('✅ 数据库完整性检查完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库完整性检查失败:', error.message);
    
    if (error.message.includes('设备未就绪') || error.message.includes('error 21')) {
      console.error('⚠️  检测到硬盘故障！请立即检查硬件状态！');
    }
    return false;
  }
}

/**
 * 检查数据库文件状态
 */
async function checkDatabaseFiles() {
  console.log('🔍 检查数据库文件状态...');
  try {
    const pool = await sql.connect(dbConfig);
    
    const result = await pool.request().query(`
      SELECT 
        name,
        physical_name,
        state_desc,
        size * 8 / 1024 as size_mb,
        max_size,
        growth
      FROM sys.database_files
    `);
    
    console.log('✅ 数据库文件状态:');
    result.recordset.forEach(file => {
      console.log(`   文件: ${file.name}`);
      console.log(`   路径: ${file.physical_name}`);
      console.log(`   状态: ${file.state_desc}`);
      console.log(`   大小: ${file.size_mb} MB`);
      console.log('   ---');
    });
    
    return true;
  } catch (error) {
    console.error('❌ 检查数据库文件失败:', error.message);
    return false;
  }
}

/**
 * 检查磁盘空间
 */
async function checkDiskSpace() {
  console.log('🔍 检查磁盘空间...');
  try {
    const pool = await sql.connect(dbConfig);
    
    const result = await pool.request().query(`
      EXEC xp_fixeddrives
    `);
    
    console.log('✅ 磁盘空间状态:');
    result.recordset.forEach(drive => {
      console.log(`   驱动器 ${drive.drive}: ${drive['MB free']} MB 可用`);
    });
    
    return true;
  } catch (error) {
    console.error('❌ 检查磁盘空间失败:', error.message);
    return false;
  }
}

/**
 * 检查错误日志
 */
async function checkErrorLog() {
  console.log('🔍 检查SQL Server错误日志...');
  try {
    const pool = await sql.connect(dbConfig);
    
    const result = await pool.request().query(`
      EXEC xp_readerrorlog 0, 1, N'error', N'21'
    `);
    
    if (result.recordset.length > 0) {
      console.log('⚠️  发现相关错误日志:');
      result.recordset.slice(0, 5).forEach(log => {
        console.log(`   ${log.LogDate}: ${log.Text}`);
      });
    } else {
      console.log('✅ 未发现相关错误');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 检查错误日志失败:', error.message);
    return false;
  }
}

/**
 * 测试关键表的访问
 */
async function testTableAccess() {
  console.log('🔍 测试关键表访问...');
  
  const tables = [
    'dbo.CU_feedbacks',
    'dbo.comProject', 
    'dbo.comPerson',
    'dbo.X_ppProduceOrder'
  ];
  
  try {
    const pool = await sql.connect(dbConfig);
    
    for (const table of tables) {
      try {
        const result = await pool.request()
          .query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`   ✅ ${table}: ${result.recordset[0].count} 条记录`);
      } catch (error) {
        console.error(`   ❌ ${table}: ${error.message}`);
        if (error.message.includes('设备未就绪')) {
          console.error('      ⚠️  硬盘故障影响此表！');
        }
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ 测试表访问失败:', error.message);
    return false;
  }
}

/**
 * 主检查函数
 */
async function runHealthCheck() {
  console.log('🏥 开始数据库健康检查...\n');
  
  const checks = [
    { name: '数据库连接', fn: checkConnection },
    { name: '数据库文件状态', fn: checkDatabaseFiles },
    { name: '磁盘空间', fn: checkDiskSpace },
    { name: '关键表访问', fn: testTableAccess },
    { name: 'SQL Server错误日志', fn: checkErrorLog },
    { name: '数据库完整性', fn: checkDatabaseIntegrity }
  ];
  
  let passedChecks = 0;
  
  for (const check of checks) {
    console.log(`\n--- ${check.name} ---`);
    const result = await check.fn();
    if (result) {
      passedChecks++;
    }
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
  }
  
  console.log('\n📊 检查结果汇总:');
  console.log(`   通过: ${passedChecks}/${checks.length}`);
  
  if (passedChecks < checks.length) {
    console.log('\n⚠️  建议措施:');
    console.log('   1. 检查硬盘健康状态 (运行 chkdsk E: /f)');
    console.log('   2. 检查硬盘连接和电源');
    console.log('   3. 考虑将数据库移动到健康的硬盘');
    console.log('   4. 立即备份重要数据');
    console.log('   5. 联系系统管理员或硬件供应商');
  } else {
    console.log('✅ 数据库健康状态良好');
  }
  
  // 关闭连接
  await sql.close();
}

// 如果直接运行此脚本
if (require.main === module) {
  runHealthCheck().catch(error => {
    console.error('健康检查失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runHealthCheck,
  checkConnection,
  checkDatabaseIntegrity,
  checkDatabaseFiles,
  checkDiskSpace,
  checkErrorLog,
  testTableAccess
};
