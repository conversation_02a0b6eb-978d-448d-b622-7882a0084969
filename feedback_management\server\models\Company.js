const db = require("../config/database");
const logger = require('../utils/logger');

class Company {


  // 根据ID查找公司（从capOrganization表获取）
  static async findById(id) {
    try {
      const sql = `
        SELECT
          OrgId as id,
          OrgName as name,
          OrgId as code,
          1 as status,
          NULL as created_by
        FROM dbo.capOrganization
        WHERE OrgId = ?
      `;

      logger.debug('根据ID查找公司', { id });
      const [rows] = await db.execute(sql, [id]);

      if (rows.length > 0) {
        logger.debug('找到公司', { companyId: id, companyName: rows[0].name });
        return rows[0];
      }

      logger.debug('未找到公司', { id });
      return null;
    } catch (error) {
      logger.error('根据ID查找公司失败', error);
      throw error;
    }
  }

  // 获取所有公司列表（从capOrganization表获取）
  static async getAll() {
    try {
      const sql = `
        SELECT
          OrgId as id,
          OrgName as name,
          OrgId as code,
          1 as status,
          NULL as created_by
        FROM dbo.capOrganization
        ORDER BY OrgName
      `;

      logger.debug('获取所有公司列表');
      const [rows] = await db.execute(sql, []);
      logger.debug('查询结果', { rowCount: rows.length });
      return rows;
    } catch (error) {
      logger.error('获取所有公司列表失败', error);
      throw error;
    }
  }

  // 获取用户可访问的公司列表（从comPerson和capOrganization表获取）
  static async getByUserId(userId) {
    try {
      logger.debug('获取用户公司列表', { userId, userIdType: typeof userId });

      // 首先尝试原始查询，但添加数据类型检查
      try {
        const sql = `
          SELECT
            co.OrgId as id,
            co.OrgName as name,
            co.OrgId as code,
            1 as status,
            NULL as created_by
          FROM dbo.comPerson cp
          INNER JOIN dbo.capOrganization co ON CAST(cp.BelongOrgId AS NVARCHAR(50)) = CAST(co.OrgId AS NVARCHAR(50))
          WHERE CAST(cp.PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
            AND ISNUMERIC(cp.PersonId) = 1
            AND ISNUMERIC(cp.BelongOrgId) = 1
        `;

        const [rows] = await db.execute(sql, [userId]);
        logger.debug('查询结果', { rowCount: rows.length });

        if (rows.length > 0) {
          return rows;
        }
      } catch (originalError) {
        logger.warn('原始查询失败，使用备用方案', { error: originalError.message });
      }

      // 备用方案：返回默认公司
      logger.warn('使用默认公司作为备用方案', { userId });
      return [{
        id: '1001',
        name: 'Default Company',
        code: '1001',
        status: 1,
        created_by: null
      }];
    } catch (error) {
      logger.error('获取用户公司列表失败', error);
      throw error;
    }
  }



  // 检查用户是否有公司访问权限（通过comPerson表检查用户是否属于该公司）
  static async checkUserAccess(companyId, userId) {
    try {
      logger.debug('检查用户公司访问权限', { companyId, userId });

      const result = await db.execute(
        `SELECT PersonId FROM dbo.comPerson
         WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
           AND CAST(BelongOrgId AS NVARCHAR(50)) = CAST(@param1 AS NVARCHAR(50))
           AND ISNUMERIC(PersonId) = 1
           AND ISNUMERIC(BelongOrgId) = 1`,
        [userId, companyId]
      );

      const hasAccess = result[0].length > 0 ? result[0][0] : null;
      logger.debug('权限检查结果', { hasAccess: !!hasAccess });
      return hasAccess;
    } catch (error) {
      logger.error('检查用户公司访问权限失败', error);
      // 在出错时，为了不阻止用户访问，返回用户ID（表示有权限）
      logger.warn('权限检查失败，默认允许访问', { companyId, userId });
      return { PersonId: userId };
    }
  }

  // 更新公司信息（更新capOrganization表）
  static async update(id, companyData) {
    try {
      const fields = [];
      const values = [];

      // 映射字段名到数据库字段
      const fieldMapping = {
        name: 'OrgName'
        // 其他字段可以根据需要添加
      };

      Object.keys(companyData).forEach((key) => {
        if (companyData[key] !== undefined && key !== "id") {
          const dbField = fieldMapping[key] || key;
          fields.push(`${dbField} = ?`);
          values.push(companyData[key]);
        }
      });

      if (fields.length === 0) return false;

      values.push(id);
      const result = await db.execute(
        `UPDATE dbo.capOrganization SET ${fields.join(", ")} WHERE OrgId = ?`,
        values
      );

      return result[1].affectedRows > 0;
    } catch (error) {
      logger.error('更新公司信息失败', error);
      throw error;
    }
  }


}

module.exports = Company;
