-- 现场信息反馈系统数据库完整设置脚本
-- 包含表创建、索引优化和性能调优
-- 整合自 init-feedback-tables.sql 和 optimize-performance.sql

USE NBSTEST;
GO

PRINT '开始执行现场信息反馈系统数据库完整设置...';
PRINT '';

-- ========================================
-- 第一部分：数据库表创建和初始化
-- ========================================

PRINT '=== 第一部分：创建数据库表 ===';

-- 创建CU_feedbacks表（现场信息反馈记录表）
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CU_feedbacks] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [TaskNumber] NVARCHAR(100) NOT NULL,
        [FeedbackUserId] NVARCHAR(50) NOT NULL,
        [FeedbackTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [Notes] NTEXT,
        [Category] NVARCHAR(100),
        [Status] INT NOT NULL DEFAULT 1,
        [Longitude] DECIMAL(10,7) NULL,
        [Latitude] DECIMAL(10,7) NULL,
        [LocationDesc] NVARCHAR(500) NULL,
        [LocationStatus] NVARCHAR(20) NULL DEFAULT 'unavailable',
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETDATE()
    );

    -- 创建优化的复合索引
    CREATE INDEX IX_CU_feedbacks_TaskNumber ON [dbo].[CU_feedbacks] ([TaskNumber]);
    CREATE INDEX IX_CU_feedbacks_FeedbackUserId_Status ON [dbo].[CU_feedbacks] ([FeedbackUserId], [Status]) INCLUDE ([FeedbackTime]);
    CREATE INDEX IX_CU_feedbacks_Status_FeedbackTime ON [dbo].[CU_feedbacks] ([Status], [FeedbackTime] DESC);
    CREATE INDEX IX_CU_feedbacks_TaskNumber_Status ON [dbo].[CU_feedbacks] ([TaskNumber], [Status]) INCLUDE ([FeedbackTime], [FeedbackUserId]);
    CREATE INDEX IX_CU_feedbacks_Location ON [dbo].[CU_feedbacks] ([Longitude], [Latitude]) WHERE [Longitude] IS NOT NULL AND [Latitude] IS NOT NULL;

    PRINT '✅ CU_feedbacks表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ CU_feedbacks表已存在，跳过创建';
END
GO

-- 创建CU_feedback_media表（反馈多媒体文件表）
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedback_media]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CU_feedback_media] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [FeedbackId] INT NOT NULL,
        [MediaType] NVARCHAR(20) NOT NULL,
        [FileName] NVARCHAR(255) NOT NULL,
        [FilePath] NVARCHAR(500) NOT NULL,
        [FileSize] BIGINT,
        [Duration] FLOAT,
        [UploadTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [Status] INT NOT NULL DEFAULT 1,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),

        -- 外键约束
        CONSTRAINT FK_CU_feedback_media_FeedbackId
            FOREIGN KEY ([FeedbackId]) REFERENCES [dbo].[CU_feedbacks]([Id])
            ON DELETE CASCADE
    );
    
    -- 创建优化的索引
    CREATE INDEX IX_CU_feedback_media_FeedbackId_Status ON [dbo].[CU_feedback_media] ([FeedbackId], [Status]) INCLUDE ([MediaType], [FileName], [FilePath]);
    CREATE INDEX IX_CU_feedback_media_MediaType ON [dbo].[CU_feedback_media] ([MediaType]);
    CREATE INDEX IX_CU_feedback_media_UploadTime ON [dbo].[CU_feedback_media] ([UploadTime] DESC);
    
    PRINT '✅ CU_feedback_media表创建成功';
END
ELSE
BEGIN
    PRINT '⚠️ CU_feedback_media表已存在，跳过创建';
END
GO

-- 为已存在的CU_feedbacks表添加位置相关字段（向后兼容）
-- 添加经度字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'Longitude')
BEGIN
    ALTER TABLE [dbo].[CU_feedbacks] ADD [Longitude] DECIMAL(10,7) NULL;
    PRINT '✅ 添加Longitude字段成功';
END
ELSE
BEGIN
    PRINT '⚠️ Longitude字段已存在，跳过添加';
END

-- 添加纬度字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'Latitude')
BEGIN
    ALTER TABLE [dbo].[CU_feedbacks] ADD [Latitude] DECIMAL(10,7) NULL;
    PRINT '✅ 添加Latitude字段成功';
END
ELSE
BEGIN
    PRINT '⚠️ Latitude字段已存在，跳过添加';
END

-- 添加位置描述字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'LocationDesc')
BEGIN
    ALTER TABLE [dbo].[CU_feedbacks] ADD [LocationDesc] NVARCHAR(500) NULL;
    PRINT '✅ 添加LocationDesc字段成功';
END
ELSE
BEGIN
    PRINT '⚠️ LocationDesc字段已存在，跳过添加';
END

-- 添加位置状态字段 (authorized: 已授权, denied: 拒绝授权, unavailable: 不可用)
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'LocationStatus')
BEGIN
    ALTER TABLE [dbo].[CU_feedbacks] ADD [LocationStatus] NVARCHAR(20) NULL DEFAULT 'unavailable';
    PRINT '✅ 添加LocationStatus字段成功';
END
ELSE
BEGIN
    PRINT '⚠️ LocationStatus字段已存在，跳过添加';
END

-- 创建位置相关索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'IX_CU_feedbacks_Location')
BEGIN
    CREATE INDEX IX_CU_feedbacks_Location ON [dbo].[CU_feedbacks] ([Longitude], [Latitude]);
    PRINT '✅ 创建位置索引成功';
END
ELSE
BEGIN
    PRINT '⚠️ 位置索引已存在，跳过创建';
END
GO

-- 检查表是否创建成功
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND type in (N'U'))
    AND EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedback_media]') AND type in (N'U'))
BEGIN
    PRINT '🎉 所有反馈相关表创建完成！';

    -- 显示表结构
    PRINT '';
    PRINT '=== CU_feedbacks表结构 ===';
    SELECT
        COLUMN_NAME as '字段名',
        DATA_TYPE as '数据类型',
        IS_NULLABLE as '允许空值',
        COLUMN_DEFAULT as '默认值'
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'CU_feedbacks'
    ORDER BY ORDINAL_POSITION;

    PRINT '';
    PRINT '=== CU_feedback_media表结构 ===';
    SELECT
        COLUMN_NAME as '字段名',
        DATA_TYPE as '数据类型',
        IS_NULLABLE as '允许空值',
        COLUMN_DEFAULT as '默认值'
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'CU_feedback_media'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT '❌ 表创建失败，请检查错误信息';
END
GO

PRINT '';
PRINT '=== 第二部分：数据库性能优化 ===';

-- 1. 更新表统计信息
PRINT '正在更新表统计信息...';
UPDATE STATISTICS dbo.CU_feedbacks WITH FULLSCAN;
UPDATE STATISTICS dbo.CU_feedback_media WITH FULLSCAN;
UPDATE STATISTICS dbo.X_ppProduceOrder WITH FULLSCAN;
UPDATE STATISTICS dbo.comProject WITH FULLSCAN;
UPDATE STATISTICS dbo.comPerson WITH FULLSCAN;
UPDATE STATISTICS dbo.comGroupPerson WITH FULLSCAN;
PRINT '✅ 表统计信息更新完成';

-- 2. 重建索引以提高查询性能
PRINT '正在重建索引...';

-- 重建CU_feedbacks表的索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'IX_CU_feedbacks_FeedbackUserId_Status')
BEGIN
    ALTER INDEX IX_CU_feedbacks_FeedbackUserId_Status ON dbo.CU_feedbacks REBUILD WITH (FILLFACTOR = 90);
    PRINT '✅ CU_feedbacks用户状态索引重建完成';
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND name = 'IX_CU_feedbacks_TaskNumber_Status')
BEGIN
    ALTER INDEX IX_CU_feedbacks_TaskNumber_Status ON dbo.CU_feedbacks REBUILD WITH (FILLFACTOR = 90);
    PRINT '✅ CU_feedbacks任务状态索引重建完成';
END

-- 重建CU_feedback_media表的索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedback_media]') AND name = 'IX_CU_feedback_media_FeedbackId_Status')
BEGIN
    ALTER INDEX IX_CU_feedback_media_FeedbackId_Status ON dbo.CU_feedback_media REBUILD WITH (FILLFACTOR = 90);
    PRINT '✅ CU_feedback_media反馈状态索引重建完成';
END

-- 3. 创建额外的优化索引（如果不存在）
PRINT '正在创建优化索引...';

-- 为X_ppProduceOrder表创建项目ID索引（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[X_ppProduceOrder]') AND name = 'IX_X_ppProduceOrder_ProjectId')
BEGIN
    CREATE INDEX IX_X_ppProduceOrder_ProjectId ON dbo.X_ppProduceOrder (ProjectId) INCLUDE (BillNo, X_JZPart, MaterialId, X_ImperviousId, X_FolderId, DemandBeginDate);
    PRINT '✅ X_ppProduceOrder项目ID索引创建完成';
END

-- 为comProject表创建组织ID索引（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[comProject]') AND name = 'IX_comProject_X_OrgId')
BEGIN
    CREATE INDEX IX_comProject_X_OrgId ON dbo.comProject (X_OrgId) INCLUDE (ProjectId, ProjectName, X_ConsUnitId);
    PRINT '✅ comProject组织ID索引创建完成';
END

-- 为comPerson表创建PersonId索引（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[comPerson]') AND name = 'IX_comPerson_PersonId')
BEGIN
    CREATE INDEX IX_comPerson_PersonId ON dbo.comPerson (PersonId);
    PRINT '✅ comPerson人员ID索引创建完成';
END

-- 4. 设置数据库优化选项
PRINT '正在设置数据库优化选项...';

-- 启用自动更新统计信息
ALTER DATABASE NBSTEST SET AUTO_UPDATE_STATISTICS ON;
ALTER DATABASE NBSTEST SET AUTO_UPDATE_STATISTICS_ASYNC ON;

-- 启用自动创建统计信息
ALTER DATABASE NBSTEST SET AUTO_CREATE_STATISTICS ON;

-- 设置兼容级别（如果需要）
-- ALTER DATABASE NBSTEST SET COMPATIBILITY_LEVEL = 150; -- SQL Server 2019

PRINT '✅ 数据库优化选项设置完成';

-- 5. 创建存储过程用于快速查询用户反馈
PRINT '正在创建优化存储过程...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetUserFeedbacksOptimized]') AND type in (N'P', N'PC'))
    DROP PROCEDURE dbo.sp_GetUserFeedbacksOptimized;
GO

CREATE PROCEDURE dbo.sp_GetUserFeedbacksOptimized
    @UserId NVARCHAR(50),
    @CompanyId NVARCHAR(50),
    @Limit INT = 50,
    @Offset INT = 0
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        i.Id as id,
        i.TaskNumber as task_number,
        i.FeedbackUserId as feedback_user_id,
        FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,
        CAST(i.Notes AS NVARCHAR(MAX)) as notes,
        i.Category as category,
        i.Status as status,
        i.Longitude as longitude,
        i.Latitude as latitude,
        i.LocationDesc as location_desc,
        i.LocationStatus as location_status,
        cgp.PersonName as feedback_user_name,
        xpo.X_JZPart as part_name,
        cp.ProjectName as project_name,
        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,
        (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count
    FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))
    LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId
    LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId
    LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo
    LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
    LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
    WHERE i.FeedbackUserId = @UserId AND i.Status = 1 AND cp.X_OrgId = @CompanyId
    ORDER BY i.FeedbackTime DESC
    OFFSET @Offset ROWS FETCH NEXT @Limit ROWS ONLY;
END;
GO

PRINT '✅ 优化存储过程创建完成';

-- 6. 分析查询计划缓存
PRINT '正在清理查询计划缓存...';
DBCC FREEPROCCACHE;
PRINT '✅ 查询计划缓存清理完成';

-- 7. 显示优化结果
PRINT '';
PRINT '=== 数据库优化完成 ===';
PRINT '优化内容包括：';
PRINT '1. 更新了所有相关表的统计信息';
PRINT '2. 重建了关键索引以提高查询性能';
PRINT '3. 创建了额外的优化索引';
PRINT '4. 设置了数据库优化选项';
PRINT '5. 创建了优化的存储过程';
PRINT '6. 清理了查询计划缓存';
PRINT '';
PRINT '建议：';
PRINT '- 定期运行此脚本以维护最佳性能';
PRINT '- 监控查询性能并根据需要调整索引';
PRINT '- 考虑在低峰时段运行维护任务';

-- 8. 显示索引使用情况统计
PRINT '';
PRINT '=== 当前索引统计信息 ===';
SELECT
    OBJECT_NAME(i.object_id) AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates
FROM sys.indexes i
LEFT JOIN sys.dm_db_index_usage_stats s ON i.object_id = s.object_id AND i.index_id = s.index_id
WHERE OBJECT_NAME(i.object_id) IN ('CU_feedbacks', 'CU_feedback_media', 'X_ppProduceOrder', 'comProject')
    AND i.name IS NOT NULL
ORDER BY TableName, IndexName;

PRINT '';
PRINT '🎉 现场信息反馈系统数据库完整设置脚本执行完成！';
PRINT '包含了表创建、字段添加、索引优化和性能调优的所有功能。';
