{"setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true, "minifyWXSS": true, "minifyJS": true, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [{"value": "server/", "type": "folder"}, {"value": "server/**", "type": "glob"}, {"value": "server/**/*", "type": "glob"}, {"value": "node_modules/", "type": "folder"}, {"value": "node_modules/**", "type": "glob"}, {"value": "node_modules/**/*", "type": "glob"}, {"value": "*.md", "type": "glob"}, {"value": "*.txt", "type": "glob"}, {"value": "*.log", "type": "glob"}, {"value": ".git/", "type": "folder"}, {"value": ".git/**", "type": "glob"}, {"value": ".git/**/*", "type": "glob"}, {"value": ".giti<PERSON>re", "type": "file"}, {"value": ".eslintrc.js", "type": "file"}, {"value": "README.md", "type": "file"}, {"value": "package.json", "type": "file"}, {"value": "package-lock.json", "type": "file"}, {"value": ".env*", "type": "glob"}, {"value": "*.tmp", "type": "glob"}, {"value": "*.temp", "type": "glob"}, {"value": "*.mp4", "type": "glob"}, {"value": "*.mp3", "type": "glob"}, {"value": "*.wav", "type": "glob"}, {"value": "*.avi", "type": "glob"}, {"value": "*.mov", "type": "glob"}, {"value": "test/", "type": "folder"}, {"value": "tests/", "type": "folder"}, {"value": "test/**", "type": "glob"}, {"value": "tests/**", "type": "glob"}, {"value": "*.test.js", "type": "glob"}, {"value": "*.spec.js", "type": "glob"}], "include": []}, "appid": "wxbbca6b19bcfde041", "editorSetting": {}, "libVersion": "3.8.9"}