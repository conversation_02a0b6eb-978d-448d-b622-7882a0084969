<!--components/CarOrder/CarOrderCard/CarOrderCard.wxml-->
<view class="CarCard" bindtap="onNavigateClick">

    <view style="display: flex;justify-content:space-between;">
        <view>
            <text class="TextStyle">车号: </text>{{CarCard.CarMark}}
        </view>
        <view>
            审核:<text class="TextStyle" space="ensp" wx:if="{{CarCard.PermitStateName=='未审核'}}" style="color:red;"> {{CarCard.PermitStateName}} </text>
            <text class="TextStyle" space="ensp" wx:else style="color:black;"> {{CarCard.PermitStateName}} </text>
        </view>
    </view>
    <view style="display: flex;justify-content:space-between;">
        <view>
            <text class="TextStyle">车辆名称: </text>{{CarCard.CarName}}
        </view>
        <view>
            <text class="TextStyle" space="ensp" wx:if="{{CarCard.IsApproveName=='未审核'}}" > 未审批 </text>
            <text class="TextStyle" space="ensp" wx:else style="color:black;"> {{CarCard.IsApproveName}} </text>
        </view>
    </view>
    <view style="display: flex;justify-content:space-between;">
        <view>
            <text class="TextStyle">单据编号: </text>{{CarCard.BillNo}}
        </view>
        <view>
            <text class="TextStyle" space="ensp"> {{CarCard.FixStateName}} </text>
        </view>
    </view>
    <view>
        <text class="TextStyle">单据日期: </text>{{CarCard.BillDate}}
    </view>
    <view>
        <text class="TextStyle">申报人: </text>{{CarCard.WxOperaPersonName}}
        <text class="TextStyle" space="ensp"> 维修人:</text>{{CarCard.FixPersonName}}
    </view>
</view>