/**
 * 用户模型
 * 处理用户相关的数据库操作
 * 从新数据库的comPerson和comGroupPerson表获取用户数据
 */

const bcrypt = require("bcryptjs");
const logger = require('../utils/logger');
const db = require('../config/database');

class User {
  // 不继承BaseModel，避免status字段的问题

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @param {string} userData.username - 用户名
   * @param {string} userData.password - 密码
   * @param {string} userData.phone - 手机号
   * @returns {Promise<number>} 用户ID
   */
  static async create(userData) {
    const { username, password, phone } = userData;

    // 密码加密
    const hashedPassword = await bcrypt.hash(password, 10);

    const data = {
      username,
      password: hashedPassword,
      phone: phone || null
    };

    logger.info('创建用户', { username, phone });
    // 注意：User类不继承BaseModel，这里需要实现具体的创建逻辑
    throw new Error('User.create方法需要实现具体的数据库插入逻辑');
  }



  /**
   * 根据手机号查找用户（从新数据库的comPerson和comGroupPerson表联合查询）
   * @param {string} phone - 手机号
   * @returns {Promise<Object|null>} 用户信息
   */
  static async findByPhone(phone) {
    try {
      const sql = `
        SELECT
          cp.PersonId as id,
          cgp.PersonName as username,
          cgp.Phone as phone,
          cp.BelongOrgId as company_id,
          cp.PersonId as person_id
        FROM dbo.comPerson cp
        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))
        WHERE cgp.Phone = ? AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1
      `;

      logger.debug('根据手机号查找用户', { phone });
      const [rows] = await db.execute(sql, [phone]);

      if (rows.length > 0) {
        const user = rows[0];
        // 添加默认密码（PersonId）
        user.password = user.person_id;
        logger.debug('找到用户', { userId: user.id, username: user.username });
        return user;
      }

      logger.debug('未找到用户', { phone });
      return null;
    } catch (error) {
      logger.error('根据手机号查找用户失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找用户（不包含密码）
   * @param {number} id - 用户ID（PersonId）
   * @returns {Promise<Object|null>} 用户信息
   */
  static async findById(id) {
    try {
      // 先尝试简单查询，避免JOIN可能的问题
      logger.debug('根据ID查找用户', { id, idType: typeof id });

      // 首先检查comPerson表，只查询数字类型的PersonId
      const personSql = `
        SELECT PersonId, BelongOrgId
        FROM dbo.comPerson
        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
          AND ISNUMERIC(PersonId) = 1
      `;
      const [personRows] = await db.execute(personSql, [id]);

      if (personRows.length === 0) {
        logger.debug('在comPerson表中未找到用户', { id });
        return null;
      }

      const person = personRows[0];
      logger.debug('在comPerson表中找到用户', person);

      // 然后查询comGroupPerson表获取用户名和电话
      const groupSql = `
        SELECT PersonName, Phone
        FROM dbo.comGroupPerson
        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
          AND ISNUMERIC(PersonId) = 1
      `;
      const [groupRows] = await db.execute(groupSql, [id]);

      if (groupRows.length === 0) {
        logger.debug('在comGroupPerson表中未找到用户详情', { id });
        return null;
      }

      const group = groupRows[0];
      logger.debug('在comGroupPerson表中找到用户详情', group);

      const user = {
        id: person.PersonId,
        username: group.PersonName,
        phone: group.Phone,
        company_id: person.BelongOrgId,
        person_id: person.PersonId
      };

      logger.debug('组装用户信息完成', user);
      return user;
    } catch (error) {
      logger.error('根据ID查找用户失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找用户（包含密码，用于密码验证）
   * @param {number} id - 用户ID（PersonId）
   * @returns {Promise<Object|null>} 用户信息（包含密码）
   */
  static async findByIdWithPassword(id) {
    try {
      const sql = `
        SELECT
          cp.PersonId as id,
          cgp.PersonName as username,
          cgp.Phone as phone,
          cp.BelongOrgId as company_id,
          cp.PersonId as person_id,
          cp.PersonId as password
        FROM dbo.comPerson cp
        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))
        WHERE CAST(cp.PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
          AND ISNUMERIC(cp.PersonId) = 1
          AND ISNUMERIC(cgp.PersonId) = 1
      `;

      logger.debug('根据ID查找用户（包含密码）', { id });
      const [rows] = await db.execute(sql, [id]);

      if (rows.length > 0) {
        const user = rows[0];
        logger.debug('找到用户（包含密码）', { userId: user.id, username: user.username });
        return user;
      }

      logger.debug('未找到用户', { id });
      return null;
    } catch (error) {
      logger.error('根据ID查找用户（包含密码）失败', error);
      throw error;
    }
  }

  /**
   * 验证密码
   * @param {string} plainPassword - 明文密码
   * @param {string} hashedPassword - 加密后的密码或PersonId
   * @returns {Promise<boolean>} 密码是否正确
   */
  static async validatePassword(plainPassword, hashedPassword) {
    try {
      // 如果hashedPassword是PersonId（纯数字），直接比较
      if (/^\d+$/.test(hashedPassword)) {
        logger.debug('使用PersonId验证密码', { plainPassword, hashedPassword });
        return plainPassword === hashedPassword;
      }

      // 否则使用bcrypt验证
      return await bcrypt.compare(plainPassword, hashedPassword);
    } catch (error) {
      logger.error('密码验证失败', error);
      return false;
    }
  }

  /**
   * 获取用户的公司列表（从comPerson和capOrganization表获取用户所属公司）
   * @param {number} userId - 用户ID（PersonId）
   * @returns {Promise<Array>} 公司列表
   */
  static async getUserCompanies(userId) {
    try {
      logger.debug('获取用户公司列表', { userId, userIdType: typeof userId });

      // 首先尝试原始查询，但添加数据类型检查
      try {
        const sql = `
          SELECT
            co.OrgId as id,
            co.OrgName as name,
            co.OrgId as code,
            1 as status,
            NULL as created_by,
            GETDATE() as created_at,
            GETDATE() as updated_at,
            GETDATE() as joined_at
          FROM dbo.comPerson cp
          INNER JOIN dbo.capOrganization co ON CAST(cp.BelongOrgId AS NVARCHAR(50)) = CAST(co.OrgId AS NVARCHAR(50))
          WHERE CAST(cp.PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
            AND ISNUMERIC(cp.PersonId) = 1
            AND ISNUMERIC(cp.BelongOrgId) = 1
        `;

        const [rows] = await db.execute(sql, [userId]);
        logger.debug('用户公司列表查询结果', { userId, rowCount: rows.length });

        if (rows.length > 0) {
          return rows;
        }
      } catch (originalError) {
        logger.warn('原始查询失败，尝试备用方案', { error: originalError.message });
      }

      // 备用方案：使用简化查询，避免JOIN问题
      try {
        const sql = `
          SELECT
            cp.BelongOrgId as id,
            'Default Company' as name,
            cp.BelongOrgId as code,
            1 as status,
            NULL as created_by,
            GETDATE() as created_at,
            GETDATE() as updated_at,
            GETDATE() as joined_at
          FROM dbo.comPerson cp
          WHERE CAST(cp.PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))
            AND ISNUMERIC(cp.PersonId) = 1
            AND ISNUMERIC(cp.BelongOrgId) = 1
        `;

        const [rows] = await db.execute(sql, [userId]);
        logger.debug('备用查询结果', { userId, rowCount: rows.length });
        return rows;
      } catch (backupError) {
        logger.error('备用查询也失败', backupError);

        // 最后的备用方案：返回默认公司
        logger.warn('使用默认公司作为最后备用方案', { userId });
        return [{
          id: '1001',
          name: 'Default Company',
          code: '1001',
          status: 1,
          created_by: null,
          created_at: new Date(),
          updated_at: new Date(),
          joined_at: new Date()
        }];
      }
    } catch (error) {
      logger.error('获取用户公司列表失败', error);
      throw error;
    }
  }

  /**
   * 更新用户信息
   * @param {number} id - 用户ID
   * @param {Object} userData - 要更新的用户数据
   * @returns {Promise<boolean>} 是否更新成功
   */
  static async updateUser(id, userData) {
    // 过滤掉不允许更新的字段
    const allowedFields = ['username', 'phone', 'status'];
    const filteredData = {};

    Object.keys(userData).forEach(key => {
      if (allowedFields.includes(key) && userData[key] !== undefined) {
        filteredData[key] = userData[key];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      logger.warn('没有有效的更新字段', { id, userData });
      return false;
    }

    logger.info('更新用户信息', { id, fields: Object.keys(filteredData) });
    // 注意：User类不继承BaseModel，这里需要实现具体的更新逻辑
    throw new Error('User.updateUser方法需要实现具体的数据库更新逻辑');
  }

  /**
   * 修改密码
   * @param {number} id - 用户ID
   * @param {string} newPassword - 新密码
   * @returns {Promise<boolean>} 是否修改成功
   */
  static async updatePassword(id, newPassword) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      logger.info('更新用户密码', { id });
      // 注意：User类不继承BaseModel，这里需要实现具体的更新逻辑
      throw new Error('User.updatePassword方法需要实现具体的数据库更新逻辑');
    } catch (error) {
      logger.error('更新密码失败', error);
      throw error;
    }
  }






}

module.exports = User;
module.exports = User;
