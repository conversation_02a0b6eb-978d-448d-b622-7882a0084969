/**
 * 地理编码代理接口
 * 解决天地图API权限问题的服务端代理
 */

const express = require('express');
const axios = require('axios');
const config = require('../config');
const router = express.Router();

// 获取天地图配置
const getTiandituConfig = () => config.get('tianditu');

/**
 * 天地图逆地理编码代理接口
 * POST /api/geocoding/tianditu
 */
router.post('/tianditu', async (req, res) => {
  try {
    const { latitude, longitude } = req.body;

    // 参数验证
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        message: '经纬度参数不能为空'
      });
    }

    // 构建天地图API请求参数
    const postStr = JSON.stringify({
      lon: longitude,
      lat: latitude,
      ver: 1
    });

    console.log(`天地图代理请求: 纬度=${latitude}, 经度=${longitude}`);

    // 获取天地图配置
    const tiandituConfig = getTiandituConfig();

    // 调用天地图API
    const response = await axios.get(tiandituConfig.geocoderUrl, {
      params: {
        postStr: postStr,
        type: 'geocode',
        tk: tiandituConfig.key
      },
      timeout: tiandituConfig.timeout
    });

    console.log('天地图API响应:', response.data);

    // 检查响应状态
    if (response.data.status === '0' && response.data.result) {
      // 成功
      res.json({
        success: true,
        data: response.data.result,
        message: '逆地理编码成功'
      });
    } else {
      // API返回错误
      const errorMsg = response.data.msg || '天地图API返回错误';
      console.error('天地图API错误:', response.data);
      
      res.status(400).json({
        success: false,
        message: `天地图API错误: ${errorMsg}`,
        code: response.data.status
      });
    }

  } catch (error) {
    console.error('天地图代理请求失败:', error);

    // 处理不同类型的错误
    if (error.code === 'ECONNABORTED') {
      res.status(408).json({
        success: false,
        message: '请求超时，请稍后重试'
      });
    } else if (error.response) {
      // HTTP错误响应
      res.status(error.response.status).json({
        success: false,
        message: `天地图API请求失败: ${error.response.status}`,
        details: error.response.data
      });
    } else if (error.request) {
      // 网络错误
      res.status(503).json({
        success: false,
        message: '网络连接失败，请检查网络设置'
      });
    } else {
      // 其他错误
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        details: error.message
      });
    }
  }
});

/**
 * 批量天地图逆地理编码代理接口
 * POST /api/geocoding/tianditu/batch
 */
router.post('/tianditu/batch', async (req, res) => {
  try {
    const { locations } = req.body;

    // 参数验证
    if (!Array.isArray(locations) || locations.length === 0) {
      return res.status(400).json({
        success: false,
        message: '位置列表不能为空'
      });
    }

    if (locations.length > 10) {
      return res.status(400).json({
        success: false,
        message: '批量请求最多支持10个位置'
      });
    }

    console.log(`天地图批量代理请求: ${locations.length}个位置`);

    const results = [];

    // 并发处理多个位置
    const promises = locations.map(async (location, index) => {
      try {
        const { latitude, longitude, name } = location;

        if (!latitude || !longitude) {
          return {
            index,
            name: name || `位置${index + 1}`,
            success: false,
            message: '经纬度参数不能为空'
          };
        }

        const postStr = JSON.stringify({
          lon: longitude,
          lat: latitude,
          ver: 1
        });

        // 获取天地图配置
        const tiandituConfig = getTiandituConfig();

        const response = await axios.get(tiandituConfig.geocoderUrl, {
          params: {
            postStr: postStr,
            type: 'geocode',
            tk: tiandituConfig.key
          },
          timeout: tiandituConfig.timeout
        });

        if (response.data.status === '0' && response.data.result) {
          return {
            index,
            name: name || `位置${index + 1}`,
            success: true,
            data: response.data.result
          };
        } else {
          return {
            index,
            name: name || `位置${index + 1}`,
            success: false,
            message: response.data.msg || '天地图API返回错误'
          };
        }

      } catch (error) {
        return {
          index,
          name: location.name || `位置${index + 1}`,
          success: false,
          message: error.message || '请求失败'
        };
      }
    });

    const results_array = await Promise.all(promises);

    // 按原始顺序排序
    results_array.sort((a, b) => a.index - b.index);

    res.json({
      success: true,
      data: results_array,
      message: `批量逆地理编码完成，共处理${locations.length}个位置`
    });

  } catch (error) {
    console.error('批量天地图代理请求失败:', error);
    
    res.status(500).json({
      success: false,
      message: '批量请求处理失败',
      details: error.message
    });
  }
});

/**
 * 天地图API状态检查接口
 * GET /api/geocoding/tianditu/status
 */
router.get('/tianditu/status', async (req, res) => {
  try {
    // 使用北京天安门坐标进行测试
    const testLat = 39.9042;
    const testLon = 116.4074;

    const postStr = JSON.stringify({
      lon: testLon,
      lat: testLat,
      ver: 1
    });

    console.log('检查天地图API状态...');

    // 获取天地图配置
    const tiandituConfig = getTiandituConfig();

    const response = await axios.get(tiandituConfig.geocoderUrl, {
      params: {
        postStr: postStr,
        type: 'geocode',
        tk: tiandituConfig.key
      },
      timeout: 5000 // 状态检查使用较短超时
    });

    if (response.data.status === '0') {
      res.json({
        success: true,
        available: true,
        message: '天地图API正常工作',
        testResult: response.data.result.formatted_address
      });
    } else {
      res.json({
        success: true,
        available: false,
        message: `天地图API异常: ${response.data.msg}`,
        code: response.data.status
      });
    }

  } catch (error) {
    console.error('天地图API状态检查失败:', error);
    
    res.json({
      success: true,
      available: false,
      message: '天地图API连接失败',
      error: error.message
    });
  }
});

module.exports = router;
