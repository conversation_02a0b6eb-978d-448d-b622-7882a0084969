/**
 * 图片处理工具类
 * 处理微信小程序中图片相关的环境差异和跨平台问题
 */

// 引入环境检测工具
const EnvironmentHelper = require('./environment-helper');

class ImageHelper {
  /**
   * 检查当前运行环境
   * @returns {Object} 环境信息
   */
  static getEnvironmentInfo() {
    return EnvironmentHelper.getDiagnosticInfo().environment;
  }

  /**
   * 构建图片URL
   * @param {string} url - 原始URL或路径
   * @param {string} baseUrl - 基础URL
   * @returns {string} 完整的图片URL
   */
  static buildImageUrl(url, baseUrl) {
    if (!url) return '';

    // 如果已经是完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // 确保URL以/开头
    const normalizedUrl = url.startsWith('/') ? url : '/' + url;

    const envInfo = this.getEnvironmentInfo();

    // 在真机环境下，尝试使用HTTPS协议以避免微信小程序的HTTP限制
    let fullUrl;
    if (envInfo.isRealDevice) {
      // 真机环境下的特殊处理
      if (normalizedUrl.startsWith('/api/files/')) {
        // 将 /api/files/ 路径转换为 /upload/ 路径
        const uploadPath = normalizedUrl.replace('/api/files/', '/upload/');
        fullUrl = `${baseUrl}${uploadPath}`;
      } else {
        fullUrl = `${baseUrl}${normalizedUrl}`;
      }

      // 如果baseUrl是HTTP协议，在真机环境下尝试转换为HTTPS
      if (fullUrl.startsWith('http://')) {
        const httpsUrl = fullUrl.replace('http://', 'https://');
        console.log(`[ImageHelper] 真机环境HTTP转HTTPS:`, {
          originalUrl: url,
          httpUrl: fullUrl,
          httpsUrl: httpsUrl,
          note: '尝试使用HTTPS避免微信小程序限制'
        });
        // 注意：这里暂时还是返回HTTP URL，因为服务器可能没有配置HTTPS
        // 如果需要强制使用HTTPS，取消下面这行的注释
        // fullUrl = httpsUrl;
      }

      console.log(`[ImageHelper] 真机环境构建URL:`, {
        originalUrl: url,
        normalizedUrl: normalizedUrl,
        fullUrl: fullUrl,
        isRealDevice: envInfo.isRealDevice
      });
    } else {
      // 开发工具环境，使用原始逻辑
      fullUrl = `${baseUrl}${normalizedUrl}`;
      console.log(`[ImageHelper] 开发工具环境构建URL:`, {
        originalUrl: url,
        normalizedUrl: normalizedUrl,
        fullUrl: fullUrl,
        baseUrl: baseUrl,
        isRealDevice: envInfo.isRealDevice
      });
    }

    return fullUrl;
  }

  /**
   * 验证图片URL格式
   * @param {string} url - 图片URL
   * @returns {boolean} 是否有效
   */
  static validateImageUrl(url) {
    if (!url) return false;

    const envInfo = this.getEnvironmentInfo();

    // 基本URL格式检查（不使用URL构造函数）
    const urlPattern = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
    if (!urlPattern.test(url)) {
      return false;
    }

    // 解析URL组件（手动解析，避免使用URL构造函数）
    const urlParts = this.parseUrl(url);

    // 在真机环境下检查域名配置
    if (envInfo.isRealDevice) {
      // 检查是否为本地地址（在真机环境下可能无法访问）
      if (urlParts.hostname === 'localhost' || urlParts.hostname === '127.0.0.1') {
        console.warn(`[ImageHelper] 真机环境下使用本地地址可能无法访问: ${url}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 手动解析URL（避免使用URL构造函数）
   * @param {string} url - 要解析的URL
   * @returns {Object} URL组件
   */
  static parseUrl(url) {
    const match = url.match(/^(https?):\/\/([^:\/\s]+)(?::(\d+))?(\/.*)?$/);

    if (!match) {
      return {
        protocol: '',
        hostname: '',
        port: '',
        pathname: ''
      };
    }

    return {
      protocol: match[1] + ':',
      hostname: match[2],
      port: match[3] || (match[1] === 'https' ? '443' : '80'),
      pathname: match[4] || '/'
    };
  }

  /**
   * 检查网络状态
   * @returns {Promise<Object>} 网络状态信息
   */
  static checkNetworkStatus() {
    return new Promise((resolve, reject) => {
      wx.getNetworkType({
        success: (res) => {
          const networkInfo = {
            networkType: res.networkType,
            isConnected: res.networkType !== 'none',
            isWifi: res.networkType === 'wifi',
            isMobile: ['2g', '3g', '4g', '5g'].includes(res.networkType),
            isSlowNetwork: ['2g', '3g'].includes(res.networkType)
          };

          console.log(`[ImageHelper] 网络状态:`, networkInfo);
          resolve(networkInfo);
        },
        fail: (error) => {
          console.error(`[ImageHelper] 获取网络状态失败:`, error);
          reject(error);
        }
      });
    });
  }

  /**
   * 显示图片加载错误提示
   * @param {string} errorType - 错误类型
   * @param {Object} context - 错误上下文
   */
  static showImageError(errorType, context = {}) {
    const envInfo = this.getEnvironmentInfo();
    let message = '图片加载失败';

    switch (errorType) {
      case 'network':
        message = envInfo.isRealDevice ? '网络连接异常，请检查网络后重试' : '网络请求失败';
        break;
      case 'url_format':
        message = envInfo.isRealDevice ? '图片地址无法访问，请检查网络或域名配置' : '图片地址格式错误';
        break;
      case 'not_found':
        message = '图片文件不存在';
        break;
      case 'permission':
        message = envInfo.isRealDevice ? '无法访问图片，请检查域名配置' : '图片访问权限不足';
        break;
      case 'http_not_supported':
        message = envInfo.isRealDevice ? '微信小程序不支持HTTP图片，请配置HTTPS服务器' : 'HTTP协议不支持';
        break;
      default:
        message = envInfo.isRealDevice ? '图片加载失败，请稍后重试' : '图片加载失败';
    }

    console.error(`[ImageHelper] 图片错误:`, {
      errorType: errorType,
      message: message,
      context: context,
      envInfo: envInfo
    });

    // 只在非频繁错误时显示toast，避免过多提示
    if (!this._lastErrorTime || Date.now() - this._lastErrorTime > 5000) {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
      this._lastErrorTime = Date.now();
    }
  }

  /**
   * 预加载图片
   * @param {string} url - 图片URL
   * @returns {Promise<Object>} 预加载结果
   */
  static preloadImage(url) {
    return new Promise((resolve, reject) => {
      if (!url) {
        reject(new Error('图片URL为空'));
        return;
      }

      wx.getImageInfo({
        src: url,
        success: (res) => {
          resolve(res);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }

  /**
   * 测试图片URL是否可访问（使用微信API）
   * @param {string} url - 图片URL
   * @returns {Promise<boolean>} 是否可访问
   */
  static testImageUrl(url) {
    return new Promise((resolve) => {
      if (!url) {
        resolve(false);
        return;
      }

      wx.getImageInfo({
        src: url,
        success: (res) => {
          resolve(true);
        },
        fail: (error) => {
          resolve(false);
        }
      });
    });
  }

  /**
   * 获取图片信息（兼容不同环境）
   * @param {string} src - 图片路径
   * @returns {Promise<Object>} 图片信息
   */
  static getImageInfo(src) {
    return new Promise((resolve, reject) => {
      const envInfo = this.getEnvironmentInfo();

      wx.getImageInfo({
        src: src,
        success: (res) => {
          const imageInfo = {
            ...res,
            isRealDevice: envInfo.isRealDevice,
            platform: envInfo.platform
          };

          console.log(`[ImageHelper] 获取图片信息成功:`, imageInfo);
          resolve(imageInfo);
        },
        fail: (error) => {
          console.error(`[ImageHelper] 获取图片信息失败:`, {
            src: src,
            error: error,
            isRealDevice: envInfo.isRealDevice
          });
          reject(error);
        }
      });
    });
  }

  /**
   * 处理图片加载错误的通用方法
   * @param {Object} event - 图片错误事件
   * @param {Object} options - 处理选项
   */
  static handleImageError(event, options = {}) {
    const { src, index, retryCallback } = options;
    const envInfo = this.getEnvironmentInfo();

    console.error(`[ImageHelper] 图片加载错误:`, {
      src: src,
      index: index,
      event: event,
      isRealDevice: envInfo.isRealDevice
    });

    // 标记图片为显示错误状态
    if (retryCallback && typeof retryCallback === 'function') {
      // 先设置显示错误状态，避免闪烁
      retryCallback(null, index, true); // 第三个参数表示设置错误状态
    }

    // 在真机环境下，检查是否是HTTP协议导致的问题
    if (envInfo.isRealDevice && src && src.startsWith('http://')) {
      console.log(`[ImageHelper] 检测到HTTP协议图片在真机环境，这可能导致微信小程序安全限制`);

      // 尝试HTTPS版本（注意：需要服务器支持HTTPS）
      const httpsUrl = src.replace('http://', 'https://');
      console.log(`[ImageHelper] 尝试HTTPS URL: ${httpsUrl}`);

      // 显示HTTP不支持的错误信息
      this.showImageError('http_not_supported', { src, index, httpsUrl });
      return;
    }

    // 在真机环境下，尝试使用备用URL格式
    if (envInfo.isRealDevice && src && src.includes('/api/files/')) {
      const alternativeUrl = src.replace('/api/files/', '/upload/');

      // 测试备用URL是否可用
      this.testImageUrl(alternativeUrl).then(isAccessible => {
        if (isAccessible && retryCallback && typeof retryCallback === 'function') {
          retryCallback(alternativeUrl, index, false); // 恢复正常状态并使用新URL
          return;
        }

        // 备用URL也不可用，显示错误信息
        this.showImageError('network', { src, index });
      }).catch(error => {
        this.showImageError('network', { src, index });
      });
    } else {
      // 根据环境显示不同的错误信息
      if (envInfo.isRealDevice) {
        this.showImageError('network', { src, index });
      } else {
        this.showImageError('url_format', { src, index });
      }
    }
  }

  /**
   * 获取备用图片URL
   * @param {string} originalUrl - 原始URL
   * @returns {string} 备用URL
   */
  static getAlternativeImageUrl(originalUrl) {
    if (!originalUrl) return '';

    // 如果是 /api/files/ 格式，尝试转换为 /upload/ 格式
    if (originalUrl.includes('/api/files/')) {
      return originalUrl.replace('/api/files/', '/upload/');
    }

    // 如果是 /upload/ 格式，尝试转换为 /api/files/ 格式
    if (originalUrl.includes('/upload/')) {
      return originalUrl.replace('/upload/', '/api/files/');
    }

    return originalUrl;
  }
}

module.exports = ImageHelper;
