/**
 * 统一响应工具类
 * 规范化API响应格式，提供统一的成功和错误响应方法
 */

class ResponseUtil {
  /**
   * 成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 响应数据
   * @param {string} message - 响应消息
   * @param {number} code - 响应代码
   */
  static success(res, data = null, message = '操作成功', code = 200) {
    const response = {
      success: true,
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    res.status(code).json(response);
  }

  /**
   * 错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   * @param {number} code - HTTP状态码
   * @param {*} error - 错误详情（开发环境下返回）
   */
  static error(res, message = '操作失败', code = 500, error = null) {
    const response = {
      success: false,
      code,
      message,
      timestamp: new Date().toISOString()
    };

    // 开发环境下返回错误详情
    if (process.env.NODE_ENV === 'development' && error) {
      response.error = error;
    }

    res.status(code).json(response);
  }

  /**
   * 参数错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   * @param {Array} errors - 验证错误详情
   */
  static badRequest(res, message = '请求参数错误', errors = null) {
    const response = {
      success: false,
      code: 400,
      message,
      timestamp: new Date().toISOString()
    };

    if (errors) {
      response.errors = errors;
    }

    res.status(400).json(response);
  }

  /**
   * 未授权响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static unauthorized(res, message = '未授权访问') {
    this.error(res, message, 401);
  }

  /**
   * 禁止访问响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static forbidden(res, message = '禁止访问') {
    this.error(res, message, 403);
  }

  /**
   * 资源不存在响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static notFound(res, message = '资源不存在') {
    this.error(res, message, 404);
  }

  /**
   * 服务器内部错误响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   * @param {*} error - 错误对象
   */
  static serverError(res, message = '服务器内部错误', error = null) {
    this.error(res, message, 500, error);
  }

  /**
   * 分页响应
   * @param {Object} res - Express响应对象
   * @param {Array} data - 数据列表
   * @param {Object} pagination - 分页信息
   * @param {number} pagination.page - 当前页码
   * @param {number} pagination.pageSize - 每页大小
   * @param {number} pagination.total - 总记录数
   * @param {string} message - 响应消息
   */
  static paginated(res, data, pagination, message = '获取成功') {
    const { page, pageSize, total } = pagination;
    const totalPages = Math.ceil(total / pageSize);

    const response = {
      success: true,
      code: 200,
      message,
      data: {
        list: data,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: parseInt(total),
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      },
      timestamp: new Date().toISOString()
    };

    res.status(200).json(response);
  }

  /**
   * 创建资源成功响应
   * @param {Object} res - Express响应对象
   * @param {*} data - 创建的资源数据
   * @param {string} message - 响应消息
   */
  static created(res, data = null, message = '创建成功') {
    this.success(res, data, message, 201);
  }

  /**
   * 无内容响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 响应消息
   */
  static noContent(res, message = '操作成功') {
    const response = {
      success: true,
      code: 204,
      message,
      timestamp: new Date().toISOString()
    };

    res.status(204).json(response);
  }

  /**
   * 验证错误响应
   * @param {Object} res - Express响应对象
   * @param {Array} errors - 验证错误列表
   */
  static validationError(res, errors) {
    const message = '数据验证失败';
    const response = {
      success: false,
      code: 422,
      message,
      errors,
      timestamp: new Date().toISOString()
    };

    res.status(422).json(response);
  }

  /**
   * 冲突响应（如资源已存在）
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static conflict(res, message = '资源冲突') {
    this.error(res, message, 409);
  }

  /**
   * 请求过于频繁响应
   * @param {Object} res - Express响应对象
   * @param {string} message - 错误消息
   */
  static tooManyRequests(res, message = '请求过于频繁') {
    this.error(res, message, 429);
  }

  /**
   * 包装异步路由处理器，自动捕获错误
   * @param {Function} handler - 路由处理函数
   * @returns {Function} 包装后的处理函数
   */
  static asyncHandler(handler) {
    return async (req, res, next) => {
      try {
        await handler(req, res, next);
      } catch (error) {
        this.serverError(res, '服务器内部错误', error);
      }
    };
  }

  /**
   * 创建标准化的错误对象
   * @param {string} message - 错误消息
   * @param {number} code - 错误代码
   * @param {string} field - 错误字段
   * @returns {Object} 错误对象
   */
  static createError(message, code = 500, field = null) {
    const error = new Error(message);
    error.statusCode = code;
    if (field) {
      error.field = field;
    }
    return error;
  }
}

module.exports = ResponseUtil;
