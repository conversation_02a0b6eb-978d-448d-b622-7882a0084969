[2025-07-24 00:49:47] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2167ms","memoryDiff":{"rss":11010048,"heapUsed":13245640,"heapTotal":11481088,"external":99311},"timestamp":"2025-07-24T00:49:47.811Z"}
[2025-07-24 01:03:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:03:22] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:03:26] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3264ms","memoryDiff":{"rss":3792896,"heapUsed":-386808,"heapTotal":3407872,"external":52356},"timestamp":"2025-07-24T01:03:26.129Z"}
[2025-07-24 01:03:26] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3126ms","memoryDiff":{"rss":581632,"heapUsed":2606968,"heapTotal":262144,"external":64958},"timestamp":"2025-07-24T01:03:26.179Z"}
[2025-07-24 01:03:29] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3075ms","memoryDiff":{"rss":237568,"heapUsed":1371624,"heapTotal":0,"external":42928},"timestamp":"2025-07-24T01:03:29.209Z"}
[2025-07-24 01:03:29] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3091ms","memoryDiff":{"rss":163840,"heapUsed":933344,"heapTotal":0,"external":32453},"timestamp":"2025-07-24T01:03:29.275Z"}
[2025-07-24 01:03:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3093ms","memoryDiff":{"rss":-14499840,"heapUsed":-3958424,"heapTotal":-14942208,"external":-89338},"timestamp":"2025-07-24T01:03:32.372Z"}
[2025-07-24 01:06:31] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:06:31] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:06:35] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3088ms","memoryDiff":{"rss":2748416,"heapUsed":-2198488,"heapTotal":2359296,"external":29595},"timestamp":"2025-07-24T01:06:35.142Z"}
[2025-07-24 01:06:38] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3077ms","memoryDiff":{"rss":-61440,"heapUsed":1100920,"heapTotal":0,"external":32584},"timestamp":"2025-07-24T01:06:38.222Z"}
[2025-07-24 01:06:38] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3071ms","memoryDiff":{"rss":172032,"heapUsed":935688,"heapTotal":0,"external":40645},"timestamp":"2025-07-24T01:06:38.238Z"}
[2025-07-24 01:06:41] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3084ms","memoryDiff":{"rss":-14503936,"heapUsed":-2961936,"heapTotal":-14942208,"external":-56835},"timestamp":"2025-07-24T01:06:41.330Z"}
[2025-07-24 01:07:53] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3110ms","memoryDiff":{"rss":1773568,"heapUsed":686888,"heapTotal":1310720,"external":31779},"timestamp":"2025-07-24T01:07:53.477Z"}
[2025-07-24 01:07:56] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3093ms","memoryDiff":{"rss":380928,"heapUsed":-225696,"heapTotal":-786432,"external":21528},"timestamp":"2025-07-24T01:07:56.574Z"}
[2025-07-24 01:07:56] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3091ms","memoryDiff":{"rss":360448,"heapUsed":312808,"heapTotal":262144,"external":-4313},"timestamp":"2025-07-24T01:07:56.592Z"}
[2025-07-24 01:07:59] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3082ms","memoryDiff":{"rss":12288,"heapUsed":528752,"heapTotal":0,"external":20926},"timestamp":"2025-07-24T01:07:59.687Z"}
[2025-07-24 01:08:06] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:08:06] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:08:06] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:08:06] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:08:09] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3146ms","memoryDiff":{"rss":3956736,"heapUsed":-543448,"heapTotal":3301376,"external":43588},"timestamp":"2025-07-24T01:08:09.532Z"}
[2025-07-24 01:08:12] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3090ms","memoryDiff":{"rss":106496,"heapUsed":1100536,"heapTotal":262144,"external":32584},"timestamp":"2025-07-24T01:08:12.627Z"}
[2025-07-24 01:08:12] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3087ms","memoryDiff":{"rss":77824,"heapUsed":935392,"heapTotal":0,"external":32453},"timestamp":"2025-07-24T01:08:12.675Z"}
[2025-07-24 01:08:18] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3099ms","memoryDiff":{"rss":-471040,"heapUsed":614480,"heapTotal":-524288,"external":20795},"timestamp":"2025-07-24T01:08:18.109Z"}
[2025-07-24 01:08:21] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3090ms","memoryDiff":{"rss":856064,"heapUsed":254232,"heapTotal":1048576,"external":21550},"timestamp":"2025-07-24T01:08:21.202Z"}
[2025-07-24 01:08:21] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3085ms","memoryDiff":{"rss":589824,"heapUsed":123856,"heapTotal":1048576,"external":29611},"timestamp":"2025-07-24T01:08:21.218Z"}
[2025-07-24 01:08:26] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3109ms","memoryDiff":{"rss":610304,"heapUsed":401984,"heapTotal":524288,"external":-32221},"timestamp":"2025-07-24T01:08:26.237Z"}
[2025-07-24 01:08:29] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":500,"duration":"3091ms","memoryDiff":{"rss":352256,"heapUsed":318832,"heapTotal":0,"external":29152},"timestamp":"2025-07-24T01:08:29.331Z"}
[2025-07-24 01:08:29] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3084ms","memoryDiff":{"rss":339968,"heapUsed":220088,"heapTotal":0,"external":20829},"timestamp":"2025-07-24T01:08:29.350Z"}
[2025-07-24 01:08:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"3065ms","memoryDiff":{"rss":286720,"heapUsed":-136736,"heapTotal":0,"external":-6468},"timestamp":"2025-07-24T01:08:32.439Z"}
[2025-07-24 01:09:42] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2470ms","memoryDiff":{"rss":23777280,"heapUsed":23532928,"heapTotal":20971520,"external":227357},"timestamp":"2025-07-24T01:09:42.480Z"}
[2025-07-24 01:09:50] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3207ms","memoryDiff":{"rss":1441792,"heapUsed":-8566616,"heapTotal":6029312,"external":95085},"timestamp":"2025-07-24T01:09:50.050Z"}
[2025-07-24 01:09:50] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3917ms","memoryDiff":{"rss":1699840,"heapUsed":-34224,"heapTotal":6029312,"external":225663},"timestamp":"2025-07-24T01:09:50.772Z"}
[2025-07-24 01:09:53] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2451ms","memoryDiff":{"rss":401408,"heapUsed":13091992,"heapTotal":0,"external":-203183},"timestamp":"2025-07-24T01:09:53.256Z"}
[2025-07-24 01:09:54] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3867ms","memoryDiff":{"rss":1277952,"heapUsed":32723768,"heapTotal":10694656,"external":358384},"timestamp":"2025-07-24T01:09:54.992Z"}
[2025-07-24 01:27:28] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2364ms","memoryDiff":{"rss":36720640,"heapUsed":20566152,"heapTotal":40894464,"external":424874},"timestamp":"2025-07-24T01:27:28.995Z"}
[2025-07-24 01:27:29] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2389ms","memoryDiff":{"rss":37285888,"heapUsed":20388592,"heapTotal":40316928,"external":509048},"timestamp":"2025-07-24T01:27:29.006Z"}
[2025-07-24 01:27:31] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2186ms","memoryDiff":{"rss":7454720,"heapUsed":-14127760,"heapTotal":2416640,"external":-319840},"timestamp":"2025-07-24T01:27:31.199Z"}
[2025-07-24 01:27:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2624ms","memoryDiff":{"rss":737280,"heapUsed":11403840,"heapTotal":-315392,"external":334692},"timestamp":"2025-07-24T01:27:32.895Z"}
[2025-07-24 01:27:35] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2213ms","memoryDiff":{"rss":16232448,"heapUsed":10148432,"heapTotal":15990784,"external":-201396},"timestamp":"2025-07-24T01:27:35.113Z"}
[2025-07-24 01:33:33] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:33:33] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:35:13] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:35:13] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:36:59] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:36:59] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:38:26] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:38:26] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:38:26] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 01:38:35] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2614ms","memoryDiff":{"rss":6684672,"heapUsed":-10311896,"heapTotal":-8912896,"external":-50118},"timestamp":"2025-07-24T01:38:35.221Z"}
[2025-07-24 01:38:37] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2659ms","memoryDiff":{"rss":10940416,"heapUsed":28544472,"heapTotal":22282240,"external":297566},"timestamp":"2025-07-24T01:38:37.886Z"}
[2025-07-24 01:41:57] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2726ms","memoryDiff":{"rss":40333312,"heapUsed":14823368,"heapTotal":43724800,"external":250728},"timestamp":"2025-07-24T01:41:57.286Z"}
[2025-07-24 01:41:59] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2447ms","memoryDiff":{"rss":8441856,"heapUsed":4047368,"heapTotal":3932160,"external":46725},"timestamp":"2025-07-24T01:41:59.740Z"}
[2025-07-24 01:42:01] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3873ms","memoryDiff":{"rss":26988544,"heapUsed":19465608,"heapTotal":22749184,"external":166875},"timestamp":"2025-07-24T01:42:01.617Z"}
[2025-07-24 01:42:04] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2411ms","memoryDiff":{"rss":25284608,"heapUsed":25376696,"heapTotal":24641536,"external":160917},"timestamp":"2025-07-24T01:42:04.033Z"}
[2025-07-24 01:42:08] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2350ms","memoryDiff":{"rss":27328512,"heapUsed":17081936,"heapTotal":27664384,"external":-235977},"timestamp":"2025-07-24T01:42:08.937Z"}
[2025-07-24 01:42:11] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2433ms","memoryDiff":{"rss":9281536,"heapUsed":-61294944,"heapTotal":-57147392,"external":-109455},"timestamp":"2025-07-24T01:42:11.375Z"}
[2025-07-24 01:42:12] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3428ms","memoryDiff":{"rss":8085504,"heapUsed":-55820632,"heapTotal":-45146112,"external":-438484},"timestamp":"2025-07-24T01:42:12.959Z"}
[2025-07-24 01:42:15] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2188ms","memoryDiff":{"rss":626688,"heapUsed":26836560,"heapTotal":23855104,"external":237975},"timestamp":"2025-07-24T01:42:15.151Z"}
[2025-07-24 01:42:56] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2131ms","memoryDiff":{"rss":41361408,"heapUsed":15159496,"heapTotal":43462656,"external":235524},"timestamp":"2025-07-24T01:42:56.808Z"}
[2025-07-24 01:42:58] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2011ms","memoryDiff":{"rss":9043968,"heapUsed":3860864,"heapTotal":4194304,"external":45505},"timestamp":"2025-07-24T01:42:58.823Z"}
[2025-07-24 01:43:00] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3185ms","memoryDiff":{"rss":9097216,"heapUsed":8535776,"heapTotal":5451776,"external":296457},"timestamp":"2025-07-24T01:43:00.293Z"}
[2025-07-24 01:43:44] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2233ms","memoryDiff":{"rss":44318720,"heapUsed":3168728,"heapTotal":43986944,"external":211699},"timestamp":"2025-07-24T01:43:44.693Z"}
[2025-07-24 01:43:46] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2033ms","memoryDiff":{"rss":3514368,"heapUsed":16747720,"heapTotal":2936832,"external":502652},"timestamp":"2025-07-24T01:43:46.730Z"}
[2025-07-24 01:43:48] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2441ms","memoryDiff":{"rss":27426816,"heapUsed":28778872,"heapTotal":27996160,"external":103838},"timestamp":"2025-07-24T01:43:48.252Z"}
[2025-07-24 01:43:50] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2127ms","memoryDiff":{"rss":24100864,"heapUsed":26823624,"heapTotal":23855104,"external":224128},"timestamp":"2025-07-24T01:43:50.383Z"}
[2025-07-24 01:44:57] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2124ms","memoryDiff":{"rss":39706624,"heapUsed":14249408,"heapTotal":42938368,"external":212166},"timestamp":"2025-07-24T01:44:57.917Z"}
[2025-07-24 01:45:00] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2180ms","memoryDiff":{"rss":7839744,"heapUsed":-155448,"heapTotal":2359296,"external":48573},"timestamp":"2025-07-24T01:45:00.102Z"}
[2025-07-24 01:45:01] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3440ms","memoryDiff":{"rss":12771328,"heapUsed":13879656,"heapTotal":11218944,"external":-113037},"timestamp":"2025-07-24T01:45:01.712Z"}
[2025-07-24 01:45:03] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2252ms","memoryDiff":{"rss":23982080,"heapUsed":26287856,"heapTotal":22544384,"external":225256},"timestamp":"2025-07-24T01:45:03.968Z"}
[2025-07-24 01:46:50] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3007ms","memoryDiff":{"rss":22147072,"heapUsed":37095776,"heapTotal":20918272,"external":425754},"timestamp":"2025-07-24T01:46:50.183Z"}
[2025-07-24 01:49:22] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2870ms","memoryDiff":{"rss":43053056,"heapUsed":5392448,"heapTotal":43724800,"external":296270},"timestamp":"2025-07-24T01:49:22.766Z"}
[2025-07-24 01:49:26] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4009ms","memoryDiff":{"rss":22319104,"heapUsed":35898184,"heapTotal":21495808,"external":264198},"timestamp":"2025-07-24T01:49:26.779Z"}
[2025-07-24 01:49:27] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4952ms","memoryDiff":{"rss":32423936,"heapUsed":41194808,"heapTotal":32768000,"external":86523},"timestamp":"2025-07-24T01:49:27.756Z"}
[2025-07-24 01:49:38] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2417ms","memoryDiff":{"rss":25292800,"heapUsed":26905392,"heapTotal":24379392,"external":220634},"timestamp":"2025-07-24T01:49:38.926Z"}
[2025-07-24 01:49:42] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3719ms","memoryDiff":{"rss":7454720,"heapUsed":-59685744,"heapTotal":-54525952,"external":-467162},"timestamp":"2025-07-24T01:49:42.651Z"}
[2025-07-24 01:49:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4542ms","memoryDiff":{"rss":7454720,"heapUsed":-45369640,"heapTotal":-46137344,"external":-213421},"timestamp":"2025-07-24T01:49:43.503Z"}
[2025-07-24 01:49:45] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2396ms","memoryDiff":{"rss":0,"heapUsed":24820400,"heapTotal":24903680,"external":142573},"timestamp":"2025-07-24T01:49:45.903Z"}
[2025-07-24 01:49:49] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2719ms","memoryDiff":{"rss":0,"heapUsed":-68640800,"heapTotal":-33292288,"external":-422599},"timestamp":"2025-07-24T01:49:49.407Z"}
[2025-07-24 01:49:53] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3889ms","memoryDiff":{"rss":-372736,"heapUsed":39515056,"heapTotal":6553600,"external":200833},"timestamp":"2025-07-24T01:49:53.301Z"}
[2025-07-24 01:49:54] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4727ms","memoryDiff":{"rss":-368640,"heapUsed":53708848,"heapTotal":15466496,"external":444041},"timestamp":"2025-07-24T01:49:54.170Z"}
[2025-07-24 01:49:57] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2848ms","memoryDiff":{"rss":131072,"heapUsed":24353808,"heapTotal":24379392,"external":121722},"timestamp":"2025-07-24T01:49:57.024Z"}
[2025-07-24 01:58:48] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2256ms","memoryDiff":{"rss":41349120,"heapUsed":25239944,"heapTotal":43462656,"external":626458},"timestamp":"2025-07-24T01:58:48.610Z"}
[2025-07-24 02:25:51] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2163ms","memoryDiff":{"rss":4096000,"heapUsed":10948864,"heapTotal":208896,"external":202732},"timestamp":"2025-07-24T02:25:51.239Z"}
[2025-07-24 02:25:54] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2853ms","memoryDiff":{"rss":34349056,"heapUsed":40031536,"heapTotal":38273024,"external":224191},"timestamp":"2025-07-24T02:25:54.096Z"}
[2025-07-24 02:25:54] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3548ms","memoryDiff":{"rss":42782720,"heapUsed":54458464,"heapTotal":46923776,"external":486371},"timestamp":"2025-07-24T02:25:54.815Z"}
[2025-07-24 02:30:04] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2071ms","memoryDiff":{"rss":42090496,"heapUsed":4930368,"heapTotal":41627648,"external":264953},"timestamp":"2025-07-24T02:30:04.989Z"}
[2025-07-24 02:30:07] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2643ms","memoryDiff":{"rss":5537792,"heapUsed":15120488,"heapTotal":6553600,"external":9450},"timestamp":"2025-07-24T02:30:07.637Z"}
[2025-07-24 02:30:08] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3260ms","memoryDiff":{"rss":13045760,"heapUsed":29341032,"heapTotal":14417920,"external":269739},"timestamp":"2025-07-24T02:30:08.275Z"}
[2025-07-24 02:30:29] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2252ms","memoryDiff":{"rss":38449152,"heapUsed":25530256,"heapTotal":44249088,"external":606609},"timestamp":"2025-07-24T02:30:29.456Z"}
[2025-07-24 02:30:32] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2027ms","memoryDiff":{"rss":2686976,"heapUsed":24548112,"heapTotal":3616768,"external":491193},"timestamp":"2025-07-24T02:30:32.108Z"}
[2025-07-24 02:34:04] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2406ms","memoryDiff":{"rss":41181184,"heapUsed":4815712,"heapTotal":42938368,"external":264193},"timestamp":"2025-07-24T02:34:04.874Z"}
[2025-07-24 02:34:08] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3236ms","memoryDiff":{"rss":14487552,"heapUsed":26712672,"heapTotal":14155776,"external":81150},"timestamp":"2025-07-24T02:34:08.114Z"}
[2025-07-24 02:34:08] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3887ms","memoryDiff":{"rss":23412736,"heapUsed":41015680,"heapTotal":23068672,"external":342029},"timestamp":"2025-07-24T02:34:08.792Z"}
[2025-07-24 02:34:19] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2064ms","memoryDiff":{"rss":1568768,"heapUsed":20301376,"heapTotal":-11534336,"external":281826},"timestamp":"2025-07-24T02:34:19.906Z"}
[2025-07-24 02:34:21] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3169ms","memoryDiff":{"rss":614400,"heapUsed":28185368,"heapTotal":12001280,"external":-16852},"timestamp":"2025-07-24T02:34:21.334Z"}
[2025-07-24 02:34:23] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2133ms","memoryDiff":{"rss":307200,"heapUsed":26695672,"heapTotal":24117248,"external":232164},"timestamp":"2025-07-24T02:34:23.471Z"}
[2025-07-24 02:34:32] [INFO] API响应较慢 | {"method":"GET","url":"/B05AJA220003","statusCode":304,"duration":"2278ms","memoryDiff":{"rss":8015872,"heapUsed":-75086256,"heapTotal":-50069504,"external":-267900},"timestamp":"2025-07-24T02:34:32.341Z"}
[2025-07-24 02:34:32] [INFO] API响应较慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"2232ms","memoryDiff":{"rss":7950336,"heapUsed":-75641208,"heapTotal":-50069504,"external":-281761},"timestamp":"2025-07-24T02:34:32.356Z"}
[2025-07-24 02:34:32] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4539ms","memoryDiff":{"rss":10977280,"heapUsed":-62259112,"heapTotal":-37486592,"external":-309665},"timestamp":"2025-07-24T02:34:32.372Z"}
[2025-07-24 03:03:13] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3168ms","memoryDiff":{"rss":25112576,"heapUsed":-2619432,"heapTotal":12582912,"external":14738},"timestamp":"2025-07-24T03:03:13.604Z"}
[2025-07-24 03:30:28] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2389ms","memoryDiff":{"rss":1093632,"heapUsed":7353576,"heapTotal":-471040,"external":284085},"timestamp":"2025-07-24T03:30:28.883Z"}
[2025-07-24 03:30:30] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"3231ms","memoryDiff":{"rss":7192576,"heapUsed":14458128,"heapTotal":8908800,"external":-295978},"timestamp":"2025-07-24T03:30:30.121Z"}
[2025-07-24 03:30:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2530ms","memoryDiff":{"rss":29212672,"heapUsed":-15218280,"heapTotal":11218944,"external":253333},"timestamp":"2025-07-24T03:30:32.655Z"}
[2025-07-24 03:30:34] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2220ms","memoryDiff":{"rss":2727936,"heapUsed":25735952,"heapTotal":0,"external":186502},"timestamp":"2025-07-24T03:30:34.879Z"}
[2025-07-24 03:32:41] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3171ms","memoryDiff":{"rss":14966784,"heapUsed":13420032,"heapTotal":15728640,"external":48496},"timestamp":"2025-07-24T03:32:41.615Z"}
[2025-07-24 03:34:52] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3929ms","memoryDiff":{"rss":36962304,"heapUsed":16961616,"heapTotal":43200512,"external":288702},"timestamp":"2025-07-24T03:34:52.179Z"}
[2025-07-24 05:34:23] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 05:34:23] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 05:34:53] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2245ms","memoryDiff":{"rss":36200448,"heapUsed":35885792,"heapTotal":34340864,"external":264476},"timestamp":"2025-07-24T05:34:53.491Z"}
[2025-07-24 06:09:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2293ms","memoryDiff":{"rss":28094464,"heapUsed":-17994584,"heapTotal":5767168,"external":-174787},"timestamp":"2025-07-24T06:09:32.485Z"}
[2025-07-24 06:26:27] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3423ms","memoryDiff":{"rss":4997120,"heapUsed":2569272,"heapTotal":2883584,"external":71371},"timestamp":"2025-07-24T06:26:27.190Z"}
[2025-07-24 06:26:29] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2128ms","memoryDiff":{"rss":29036544,"heapUsed":18417784,"heapTotal":26214400,"external":-131329},"timestamp":"2025-07-24T06:26:29.322Z"}
[2025-07-24 06:38:08] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2369ms","memoryDiff":{"rss":21536768,"heapUsed":20712224,"heapTotal":21495808,"external":-142027},"timestamp":"2025-07-24T06:38:08.721Z"}
[2025-07-24 06:53:30] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2540ms","memoryDiff":{"rss":14553088,"heapUsed":10773600,"heapTotal":12374016,"external":-78607},"timestamp":"2025-07-24T06:53:30.453Z"}
[2025-07-24 06:53:32] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2173ms","memoryDiff":{"rss":14385152,"heapUsed":-16378296,"heapTotal":-6553600,"external":-22751},"timestamp":"2025-07-24T06:53:32.822Z"}
[2025-07-24 06:53:34] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3464ms","memoryDiff":{"rss":14323712,"heapUsed":-5716352,"heapTotal":3350528,"external":-165587},"timestamp":"2025-07-24T06:53:34.418Z"}
[2025-07-24 06:53:36] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2135ms","memoryDiff":{"rss":11759616,"heapUsed":29351784,"heapTotal":22544384,"external":378782},"timestamp":"2025-07-24T06:53:36.557Z"}
[2025-07-24 06:53:41] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2064ms","memoryDiff":{"rss":27652096,"heapUsed":19521864,"heapTotal":25690112,"external":-84309},"timestamp":"2025-07-24T06:53:41.525Z"}
[2025-07-24 06:53:44] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3098ms","memoryDiff":{"rss":9056256,"heapUsed":-48875728,"heapTotal":-52690944,"external":-175598},"timestamp":"2025-07-24T06:53:44.626Z"}
[2025-07-24 06:53:45] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3755ms","memoryDiff":{"rss":9064448,"heapUsed":-42239976,"heapTotal":-41943040,"external":-262644},"timestamp":"2025-07-24T06:53:45.306Z"}
[2025-07-24 06:53:47] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2208ms","memoryDiff":{"rss":589824,"heapUsed":29541152,"heapTotal":22806528,"external":369330},"timestamp":"2025-07-24T06:53:47.519Z"}
[2025-07-24 06:54:57] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3373ms","memoryDiff":{"rss":16625664,"heapUsed":4723328,"heapTotal":13684736,"external":204837},"timestamp":"2025-07-24T06:54:57.950Z"}
[2025-07-24 06:55:00] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2199ms","memoryDiff":{"rss":17076224,"heapUsed":16759176,"heapTotal":16723968,"external":-335204},"timestamp":"2025-07-24T06:55:00.153Z"}
[2025-07-24 06:55:05] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2050ms","memoryDiff":{"rss":22450176,"heapUsed":29054576,"heapTotal":22544384,"external":331001},"timestamp":"2025-07-24T06:55:05.736Z"}
[2025-07-24 06:55:08] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3135ms","memoryDiff":{"rss":31637504,"heapUsed":-47783152,"heapTotal":-20447232,"external":-125915},"timestamp":"2025-07-24T06:55:08.874Z"}
[2025-07-24 06:55:37] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3888ms","memoryDiff":{"rss":53014528,"heapUsed":49535304,"heapTotal":49020928,"external":284398},"timestamp":"2025-07-24T06:55:37.580Z"}
[2025-07-24 06:55:38] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"4309ms","memoryDiff":{"rss":52854784,"heapUsed":48080960,"heapTotal":48758784,"external":198366},"timestamp":"2025-07-24T06:55:38.710Z"}
[2025-07-24 06:55:41] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2991ms","memoryDiff":{"rss":26636288,"heapUsed":31202240,"heapTotal":26476544,"external":307403},"timestamp":"2025-07-24T06:55:41.710Z"}
[2025-07-24 06:55:43] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4612ms","memoryDiff":{"rss":32657408,"heapUsed":-62804352,"heapTotal":-48816128,"external":-126159},"timestamp":"2025-07-24T06:55:43.937Z"}
[2025-07-24 06:55:47] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2494ms","memoryDiff":{"rss":1163264,"heapUsed":18384296,"heapTotal":11010048,"external":-125564},"timestamp":"2025-07-24T06:55:47.325Z"}
[2025-07-24 06:55:55] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2370ms","memoryDiff":{"rss":925696,"heapUsed":21250488,"heapTotal":25952256,"external":-7775},"timestamp":"2025-07-24T06:55:55.032Z"}
[2025-07-24 06:57:52] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2860ms","memoryDiff":{"rss":29728768,"heapUsed":-14901064,"heapTotal":839680,"external":-434745},"timestamp":"2025-07-24T06:57:52.956Z"}
[2025-07-24 06:57:55] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"2688ms","memoryDiff":{"rss":5279744,"heapUsed":-23161480,"heapTotal":-14102528,"external":-406277},"timestamp":"2025-07-24T06:57:55.311Z"}
[2025-07-24 06:57:57] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"4793ms","memoryDiff":{"rss":19283968,"heapUsed":60202968,"heapTotal":44040192,"external":420865},"timestamp":"2025-07-24T06:57:57.761Z"}
[2025-07-24 06:59:16] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3156ms","memoryDiff":{"rss":8351744,"heapUsed":2058784,"heapTotal":4194304,"external":29525},"timestamp":"2025-07-24T06:59:16.258Z"}
[2025-07-24 07:06:59] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:06:59] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:06:59] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:08:09] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:08:09] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:08:09] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:09:55] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:09:55] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:15:53] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4499ms","memoryDiff":{"rss":41332736,"heapUsed":991912,"heapTotal":42938368,"external":173095},"timestamp":"2025-07-24T07:15:53.790Z"}
[2025-07-24 07:15:56] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2260ms","memoryDiff":{"rss":5775360,"heapUsed":13948144,"heapTotal":786432,"external":58975},"timestamp":"2025-07-24T07:15:56.055Z"}
[2025-07-24 07:15:57] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3436ms","memoryDiff":{"rss":16912384,"heapUsed":19839248,"heapTotal":13049856,"external":12705},"timestamp":"2025-07-24T07:15:57.522Z"}
[2025-07-24 07:18:14] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2427ms","memoryDiff":{"rss":37478400,"heapUsed":16486280,"heapTotal":42151936,"external":355852},"timestamp":"2025-07-24T07:18:14.880Z"}
[2025-07-24 07:18:21] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2323ms","memoryDiff":{"rss":7368704,"heapUsed":-2266296,"heapTotal":2883584,"external":199846},"timestamp":"2025-07-24T07:18:21.136Z"}
[2025-07-24 07:18:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2226ms","memoryDiff":{"rss":598016,"heapUsed":17460552,"heapTotal":-53248,"external":508770},"timestamp":"2025-07-24T07:18:25.214Z"}
[2025-07-24 07:19:47] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2272ms","memoryDiff":{"rss":36302848,"heapUsed":21648392,"heapTotal":40841216,"external":534401},"timestamp":"2025-07-24T07:19:47.895Z"}
[2025-07-24 07:19:50] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2967ms","memoryDiff":{"rss":11272192,"heapUsed":-774320,"heapTotal":7602176,"external":-279959},"timestamp":"2025-07-24T07:19:50.866Z"}
[2025-07-24 07:19:51] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3607ms","memoryDiff":{"rss":16277504,"heapUsed":8977240,"heapTotal":12582912,"external":-122433},"timestamp":"2025-07-24T07:19:51.529Z"}
[2025-07-24 07:19:53] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2048ms","memoryDiff":{"rss":17358848,"heapUsed":14464936,"heapTotal":16252928,"external":-58067},"timestamp":"2025-07-24T07:19:53.581Z"}
[2025-07-24 07:22:23] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:22:23] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 07:22:26] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2953ms","memoryDiff":{"rss":43298816,"heapUsed":2475920,"heapTotal":35860480,"external":163734},"timestamp":"2025-07-24T07:22:26.315Z"}
[2025-07-24 07:22:26] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2935ms","memoryDiff":{"rss":42856448,"heapUsed":1441960,"heapTotal":35860480,"external":163132},"timestamp":"2025-07-24T07:22:26.320Z"}
[2025-07-24 07:22:29] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2870ms","memoryDiff":{"rss":5156864,"heapUsed":22356512,"heapTotal":1572864,"external":266623},"timestamp":"2025-07-24T07:22:29.197Z"}
[2025-07-24 07:22:31] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4412ms","memoryDiff":{"rss":9261056,"heapUsed":4004552,"heapTotal":12263424,"external":-69762},"timestamp":"2025-07-24T07:22:31.150Z"}
[2025-07-24 07:22:33] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2316ms","memoryDiff":{"rss":7761920,"heapUsed":27878720,"heapTotal":8126464,"external":282075},"timestamp":"2025-07-24T07:22:33.471Z"}
[2025-07-24 08:13:37] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":1010,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-24 08:13:46] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":1011,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-24 08:14:09] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3147ms","memoryDiff":{"rss":26210304,"heapUsed":22704560,"heapTotal":23539712,"external":268335},"timestamp":"2025-07-24T08:14:09.657Z"}
[2025-07-24 08:16:08] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":1012,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-24 08:16:13] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2225ms","memoryDiff":{"rss":39374848,"heapUsed":4923040,"heapTotal":43724800,"external":263651},"timestamp":"2025-07-24T08:16:13.543Z"}
[2025-07-24 08:23:18] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 08:23:18] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 08:24:37] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2566ms","memoryDiff":{"rss":42033152,"heapUsed":6950864,"heapTotal":44773376,"external":332927},"timestamp":"2025-07-24T08:24:37.385Z"}
[2025-07-24 08:24:39] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2501ms","memoryDiff":{"rss":5484544,"heapUsed":22814024,"heapTotal":3670016,"external":306215},"timestamp":"2025-07-24T08:24:39.891Z"}
[2025-07-24 08:24:41] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3956ms","memoryDiff":{"rss":28336128,"heapUsed":31915936,"heapTotal":26943488,"external":78216},"timestamp":"2025-07-24T08:24:41.668Z"}
[2025-07-24 08:24:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2296ms","memoryDiff":{"rss":24465408,"heapUsed":26103584,"heapTotal":24379392,"external":202709},"timestamp":"2025-07-24T08:24:43.970Z"}
[2025-07-24 08:52:42] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2019ms","memoryDiff":{"rss":38309888,"heapUsed":17414168,"heapTotal":43200512,"external":286374},"timestamp":"2025-07-24T08:52:42.248Z"}
[2025-07-24 08:52:44] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2692ms","memoryDiff":{"rss":18870272,"heapUsed":16146304,"heapTotal":14155776,"external":130665},"timestamp":"2025-07-24T08:52:44.944Z"}
[2025-07-24 08:52:45] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3309ms","memoryDiff":{"rss":28553216,"heapUsed":21885152,"heapTotal":25952256,"external":-20644},"timestamp":"2025-07-24T08:52:45.577Z"}
[2025-07-24 08:52:47] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2121ms","memoryDiff":{"rss":24866816,"heapUsed":28053824,"heapTotal":23592960,"external":277902},"timestamp":"2025-07-24T08:52:47.701Z"}
[2025-07-24 08:54:19] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":1013,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-24 08:54:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2306ms","memoryDiff":{"rss":38744064,"heapUsed":19491456,"heapTotal":43986944,"external":402552},"timestamp":"2025-07-24T08:54:25.919Z"}
[2025-07-24 09:06:14] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-24 09:06:14] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
