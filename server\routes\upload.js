// 引入express框架，用于创建路由和处理HTTP请求
const express = require("express");
const path = require("path");
const router = express.Router();
const { requireAuth } = require("../middleware/auth");
const UploadManager = require("../utils/upload-manager");

// 创建上传实例
const upload = UploadManager.createUploader();

// 单文件上传
router.post("/single", requireAuth, (req, res) => {
  // 使用multer中间件处理上传
  upload.single("file")(req, res, (err) => {
    if (err) {
      const errorResponse = UploadManager.handleUploadError(err);
      return res.status(errorResponse.status).json(errorResponse);
    }

    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "没有上传文件",
        });
      }

      const fileInfo = UploadManager.formatFileInfo(req.file);



      res.json({
        success: true,
        message: "文件上传成功",
        data: fileInfo,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "文件处理失败",
      });
    }
  });
});

// 多文件上传
router.post("/multiple", requireAuth, upload.array("files", 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: "没有上传文件",
      });
    }

    const filesInfo = UploadManager.formatFilesInfo(req.files);

    res.json({
      success: true,
      message: "文件上传成功",
      data: filesInfo,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "文件上传失败",
    });
  }
});

// Base64图片上传
router.post("/base64", requireAuth, (req, res) => {
  try {
    const { imageData, filename } = req.body;

    // 验证base64图片数据
    const validation = UploadManager.validateBase64Image(imageData);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: validation.message,
      });
    }

    // 保存base64图片
    const fileInfo = UploadManager.saveBase64Image(
      validation.mimeType,
      validation.base64Data,
      filename
    );

    res.json({
      success: true,
      message: "图片上传成功",
      data: fileInfo,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "图片上传失败",
    });
  }
});



// 错误处理中间件
router.use((error, req, res, next) => {
  const errorResponse = UploadManager.handleUploadError(error);
  res.status(errorResponse.status).json(errorResponse);
});

module.exports = router;
