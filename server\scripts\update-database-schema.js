require('dotenv').config();
const sql = require('mssql');

async function updateDatabaseSchema() {
  try {
    console.log('开始更新数据库表结构...');

    // 数据库配置
    const dbConfig = {
      server: process.env.SQLSERVER_HOST || 'localhost',
      port: parseInt(process.env.SQLSERVER_PORT) || 1433,
      user: process.env.SQLSERVER_USER || 'sa',
      password: process.env.SQLSERVER_PASSWORD || '123456',
      database: process.env.SQLSERVER_DB || 'NBSTEST',
      encrypt: process.env.SQLSERVER_ENCRYPT === 'true' || false,
      trustServerCertificate: process.env.SQLSERVER_TRUST_CERT === 'true' || true,
      requestTimeout: 30000,
      connectionTimeout: 30000,
      pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
      }
    };

    console.log('数据库配置:', {
      server: dbConfig.server,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.user
    });

    // 连接数据库
    const pool = await sql.connect(dbConfig);
    
    // 1. 删除现有表（如果存在）
    console.log('删除现有表...');
    
    try {
      await pool.request().query(`
        IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedback_media]') AND type in (N'U'))
        DROP TABLE [dbo].[CU_feedback_media];
      `);
      console.log('✅ CU_feedback_media表已删除');
    } catch (error) {
      console.log('⚠️ CU_feedback_media表删除失败或不存在:', error.message);
    }
    
    try {
      await pool.request().query(`
        IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CU_feedbacks]') AND type in (N'U'))
        DROP TABLE [dbo].[CU_feedbacks];
      `);
      console.log('✅ CU_feedbacks表已删除');
    } catch (error) {
      console.log('⚠️ CU_feedbacks表删除失败或不存在:', error.message);
    }
    
    // 2. 创建新的表结构
    console.log('创建新的表结构...');
    
    // 创建CU_feedbacks表
    await pool.request().query(`
      CREATE TABLE [dbo].[CU_feedbacks] (
          [Id] INT IDENTITY(1,1) PRIMARY KEY,
          [TaskNumber] NVARCHAR(100) NOT NULL,
          [FeedbackUserId] NVARCHAR(50) NOT NULL,
          [FeedbackTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
          [Notes] NTEXT,
          [Category] NVARCHAR(100),
          [Status] INT NOT NULL DEFAULT 1,
          [CreatedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),
          [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETDATE()
      );
    `);
    console.log('✅ CU_feedbacks表创建成功');
    
    // 创建CU_feedbacks表索引
    await pool.request().query(`
      CREATE INDEX IX_CU_feedbacks_TaskNumber ON [dbo].[CU_feedbacks] ([TaskNumber]);
      CREATE INDEX IX_CU_feedbacks_FeedbackUserId ON [dbo].[CU_feedbacks] ([FeedbackUserId]);
      CREATE INDEX IX_CU_feedbacks_Status ON [dbo].[CU_feedbacks] ([Status]);
      CREATE INDEX IX_CU_feedbacks_FeedbackTime ON [dbo].[CU_feedbacks] ([FeedbackTime]);
    `);
    console.log('✅ CU_feedbacks表索引创建成功');
    
    // 创建CU_feedback_media表
    await pool.request().query(`
      CREATE TABLE [dbo].[CU_feedback_media] (
          [Id] INT IDENTITY(1,1) PRIMARY KEY,
          [FeedbackId] INT NOT NULL,
          [MediaType] NVARCHAR(20) NOT NULL,
          [FileName] NVARCHAR(255) NOT NULL,
          [FilePath] NVARCHAR(500) NOT NULL,
          [FileSize] BIGINT,
          [Duration] FLOAT,
          [UploadTime] DATETIME2 NOT NULL DEFAULT GETDATE(),
          [Status] INT NOT NULL DEFAULT 1,
          [CreatedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),
          [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETDATE(),
          
          -- 外键约束
          CONSTRAINT FK_CU_feedback_media_FeedbackId 
              FOREIGN KEY ([FeedbackId]) REFERENCES [dbo].[CU_feedbacks]([Id]) 
              ON DELETE CASCADE
      );
    `);
    console.log('✅ CU_feedback_media表创建成功');
    
    // 创建CU_feedback_media表索引
    await pool.request().query(`
      CREATE INDEX IX_CU_feedback_media_FeedbackId ON [dbo].[CU_feedback_media] ([FeedbackId]);
      CREATE INDEX IX_CU_feedback_media_MediaType ON [dbo].[CU_feedback_media] ([MediaType]);
      CREATE INDEX IX_CU_feedback_media_Status ON [dbo].[CU_feedback_media] ([Status]);
      CREATE INDEX IX_CU_feedback_media_UploadTime ON [dbo].[CU_feedback_media] ([UploadTime]);
    `);
    console.log('✅ CU_feedback_media表索引创建成功');
    
    console.log('🎉 数据库表结构更新完成！');
    
    // 关闭连接
    await pool.close();
    
  } catch (error) {
    console.error('❌ 数据库表结构更新失败:', error);
    process.exit(1);
  }
}

// 运行更新脚本
updateDatabaseSchema();
