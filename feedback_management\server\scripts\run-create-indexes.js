/**
 * 运行数据库索引创建脚本
 */

const fs = require('fs');
const path = require('path');
const sql = require('mssql');
const config = require('../config');

// 数据库配置
const dbConfig = {
  server: config.get('sqlserver.server'),
  port: config.get('sqlserver.port'),
  user: config.get('sqlserver.user'),
  password: config.get('sqlserver.password'),
  database: config.get('sqlserver.database'),
  options: {
    encrypt: config.get('sqlserver.encrypt', false),
    trustServerCertificate: config.get('sqlserver.trustServerCertificate', true),
    enableArithAbort: true,
    requestTimeout: 120000, // 2分钟超时
    connectionTimeout: 30000
  }
};

async function runIndexCreation() {
  console.log('🔧 开始创建数据库索引...\n');
  
  try {
    // 读取SQL脚本
    const sqlFilePath = path.join(__dirname, 'create-indexes.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📖 已读取索引创建脚本');
    
    // 连接数据库
    const pool = await sql.connect(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 执行脚本
    console.log('⚙️  正在执行索引创建脚本...\n');
    
    // 分割脚本为多个语句（按GO分割）
    const statements = sqlScript.split(/\nGO\s*\n/i).filter(stmt => stmt.trim());
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          console.log(`执行语句 ${i + 1}/${statements.length}...`);
          const result = await pool.request().query(statement);
          
          // 显示PRINT消息
          if (result.output) {
            console.log(`   ${result.output}`);
          }
          
          // 显示查询结果
          if (result.recordset && result.recordset.length > 0) {
            console.log('   查询结果:');
            result.recordset.forEach(row => {
              console.log(`   - ${JSON.stringify(row)}`);
            });
          }
          
        } catch (error) {
          console.error(`   ❌ 语句执行失败: ${error.message}`);
          // 继续执行其他语句
        }
      }
    }
    
    console.log('\n✅ 索引创建脚本执行完成');
    
    // 验证索引是否创建成功
    console.log('\n🔍 验证索引创建结果...');
    const indexCheckQuery = `
      SELECT 
        t.name AS TableName,
        i.name AS IndexName,
        i.type_desc AS IndexType
      FROM sys.indexes i
      INNER JOIN sys.tables t ON i.object_id = t.object_id
      WHERE t.name IN ('CU_feedbacks', 'CU_feedback_media')
        AND i.type > 0
      ORDER BY t.name, i.name
    `;
    
    const indexResult = await pool.request().query(indexCheckQuery);
    
    console.log('当前索引状态:');
    indexResult.recordset.forEach(row => {
      console.log(`   ${row.TableName}.${row.IndexName} (${row.IndexType})`);
    });
    
    await pool.close();
    console.log('\n🎉 索引创建任务完成！');
    
  } catch (error) {
    console.error('❌ 索引创建失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runIndexCreation().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runIndexCreation };
