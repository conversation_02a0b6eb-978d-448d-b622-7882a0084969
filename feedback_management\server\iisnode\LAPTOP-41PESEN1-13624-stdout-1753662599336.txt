创建目录: upload/others
上传目录结构初始化完成
[2025-07-28 00:30:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:30:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:30:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:54] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:30:54] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:30:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:30:55] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:30:55] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:30:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:30:57] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:30:57] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:30:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:30:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:30:59] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:30:59] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:31:00] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:31:00] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:31:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:31:02] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:31:02] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:31:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 5
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 5
[2025-07-28 00:31:06] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 00:31:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:31:53] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:32:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:32:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:32:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:32:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:33:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:33:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:33:30] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:33:30] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:33:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:33:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:33:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 00:33:33] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:33:33] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:33:34] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:33:34] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 5
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 5
[2025-07-28 00:33:34] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 00:33:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:33:36] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:33:36] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:34:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:34:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:34:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:34:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:34:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:34:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:34:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:37:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:37:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:37:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:37:40] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:37:40] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:37:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:37:41] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:37:41] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:37:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:37:43] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:37:43] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:37:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:37:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:38:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 00:38:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 5
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 5
[2025-07-28 00:38:36] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 5
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 5
[2025-07-28 00:38:36] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 00:38:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:38:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:39:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:01] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:39:01] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:39:01] [DEBUG] 工程统计信息查询成功 | {"total_tasks":0,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":0}
[2025-07-28 00:39:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:39:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:04] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:39:04] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:39:04] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":5}
[2025-07-28 00:39:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 00:39:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:39:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 00:39:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
天地图代理请求: 纬度=24.530972493489582, 经度=118.1821609157986
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区金山街道泗水道617-101',
    location: { lon: 118.182161, lat: 24.530972 },
    addressComponent: {
      address: '泗水道617-101',
      town: '金山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '东北',
      county: '湖里区',
      city_code: '*********',
      address_position: '东北',
      poi: '美宜佳F389',
      province_code: '*********',
      town_code: '*********005',
      province: '福建省',
      road: '五缘湾道',
      road_distance: 132,
      address_distance: 25,
      poi_distance: 25
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-07-28 00:39:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:40:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:40:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:40:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:40:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:40:01] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2010,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 00:40:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 00:40:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:40:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:40:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 00:40:06] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:40:06] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 6
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 6
[2025-07-28 00:40:06] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 00:40:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:45:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:45:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:46:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:48:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:48:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:49:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:49:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:50:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:50:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:50:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:51:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:51:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:52:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:52:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:54:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:54:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 6
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 6
[2025-07-28 00:55:02] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 00:55:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:55:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:18] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:55:18] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:55:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:55:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:55:19] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:55:19] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:55:21] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:55:21] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:55:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:55:22] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:55:22] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:55:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:55:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:55:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 00:56:00] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:56:00] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:56:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:56:01] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:56:01] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:56:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 00:56:03] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:56:03] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:56:04] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 00:56:04] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 00:56:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:56:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:56:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 00:59:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 6
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 6
[2025-07-28 00:59:42] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 00:59:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:00:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:00:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:01:23] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:02:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:03:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 6
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 6
[2025-07-28 01:03:31] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:03:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:03:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:03:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:03:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:03:42] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:03:42] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:03:42] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:03:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:03:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:03:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:03:46] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:03:46] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:03:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:03:48] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:03:48] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:04:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:04:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:04:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:04:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:04:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:04:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:04:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:04:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:04:10] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:04:10] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:04:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:04:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:04:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:04:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 6
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 6
[2025-07-28 01:05:34] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:05:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:05:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:05:54] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:05:54] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:05:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:05:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:05:56] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:05:56] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:05:57] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:05:57] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:05:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:06:00] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:06:00] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:06:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:06:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:06:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:06:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:06:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:06:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:06:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:06:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:06:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:06:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:06:12] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:06:12] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:06:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:06:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:07:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:29] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:07:29] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:07:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:30] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":6}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:07:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 01:07:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:07:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:07:56] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2011,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:07:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:07:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:07:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:08:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 01:08:01] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:08:01] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 7
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 7
[2025-07-28 01:08:01] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:08:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:08:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:08:53] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:09:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:09:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:09:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:09:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:10:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:10:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:10:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:10:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:10:45] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:10:45] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:10:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:10:46] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:10:46] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:10:48] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:10:48] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:10:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:48] [DEBUG] 工程统计信息查询成功 | {"total_tasks":2,"supplying_tasks":0,"completed_tasks":1,"total_feedbacks":0}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200189
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
任务 2: B05-2200190
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200189
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
任务 2: B05-2200190
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:10:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:10:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 01:10:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 01:10:52] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:10:52] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 7
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 7
[2025-07-28 01:10:52] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 7
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 7
[2025-07-28 01:10:52] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:10:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:10:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:11:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:01] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:11:01] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:11:01] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":7}
[2025-07-28 01:11:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:11:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 01:11:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:11:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:26] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2012,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:11:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:11:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:11:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 01:11:30] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:11:30] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-28 01:11:30] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:11:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:11:50] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:11:50] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:11:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:56] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":8}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:11:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:11:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 01:11:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 01:12:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:12:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:13:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:27] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:13:27] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:13:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:13:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:13:28] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:13:28] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:13:29] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:13:29] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:13:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:13:31] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:13:31] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:13:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-28 01:13:36] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:13:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:13:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:14:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:14:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:14:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:14:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:14:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:14:27] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:14:27] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:14:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:14:27] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:14:27] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:14:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:14:29] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:14:29] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:14:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:14:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:14:31] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:14:31] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:14:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:15:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:15:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:15:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:15:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:15:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:18:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 8
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 8
[2025-07-28 01:18:24] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:18:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:18:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:18:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:18:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:18:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:19:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:19:58] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:19:58] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:20:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:01] [DEBUG] 工程统计信息查询成功 | {"total_tasks":3,"supplying_tasks":0,"completed_tasks":1,"total_feedbacks":0}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-28 01:20:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-28 01:20:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 01:20:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
天地图代理请求: 纬度=24.5118, 经度=118.14577
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城',
    location: { lon: 118.14577, lat: 24.5118 },
    addressComponent: {
      address: '金湖三里30-46禹洲·香槟城',
      town: '禾山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '西北',
      county: '湖里区',
      city_code: '*********',
      address_position: '西北',
      poi: '禹洲·香槟城北门',
      province_code: '*********',
      town_code: '*********003',
      province: '福建省',
      road: '金湖路',
      road_distance: 67,
      address_distance: 31,
      poi_distance: 31
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-07-28 01:20:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:32] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2013,"task_number":"B05-2200219","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:20:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200219
  scheduled_time: Thu Jan 13 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1642032000000
任务 2: B05-2200236
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
任务 3: B05-2200218
  scheduled_time: Mon Jan 10 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641772800000
[2025-07-28 01:20:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:20:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 01:20:37] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:20:37] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 9
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 9
[2025-07-28 01:20:37] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:20:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:20:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:21:01] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:21:01] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:21:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:21:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:24] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:21:24] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:21:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:21:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:21:27] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:21:27] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:21:28] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:21:28] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:21:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:21:29] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:21:29] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:21:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:57] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:21:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:24:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:29:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:29:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:29:29] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:29:29] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:29:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:29:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:29:33] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:29:33] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:29:33] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:29:33] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:29:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:29:36] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:29:36] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:30:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:30:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:30:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:30:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:30:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:30:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:30:12] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:30:12] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:30:14] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:30:14] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:30:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:30:16] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:30:16] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:32:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:32:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:32:48] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:32:48] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:32:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:32:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:32:50] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:32:50] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:32:51] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:32:51] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:32:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:32:53] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:32:53] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:32:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 9
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 9
[2025-07-28 01:32:55] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:32:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:32:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:33:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:36:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 9
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 9
[2025-07-28 01:36:51] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 01:36:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:37:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 01:37:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:39] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:37:39] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:37:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:37:40] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:37:40] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:37:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 01:37:42] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:37:42] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:37:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 01:37:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 01:37:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 01:37:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
