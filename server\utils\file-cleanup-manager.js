/**
 * 文件清理管理器
 * 负责清理孤儿文件、临时文件和过期文件
 */

const fs = require('fs');
const path = require('path');
const db = require('../config/database');

class FileCleanupManager {
  constructor() {
    this.uploadDir = path.join(__dirname, '..', 'upload');
    this.tempDir = path.join(this.uploadDir, 'temp');
    this.cleanupInterval = 24 * 60 * 60 * 1000; // 24小时
    this.tempFileMaxAge = 2 * 60 * 60 * 1000; // 2小时
    this.orphanFileMaxAge = 7 * 24 * 60 * 60 * 1000; // 7天
  }

  /**
   * 启动定期清理任务
   */
  startCleanupSchedule() {
    console.log('启动文件清理调度器');
    
    // 立即执行一次清理
    this.performCleanup();
    
    // 设置定期清理
    setInterval(() => {
      this.performCleanup();
    }, this.cleanupInterval);
  }

  /**
   * 执行清理任务
   */
  async performCleanup() {
    try {
      console.log('开始执行文件清理任务');
      
      // 清理临时文件
      await this.cleanupTempFiles();
      
      // 清理孤儿文件
      await this.cleanupOrphanFiles();
      
      // 清理空目录
      await this.cleanupEmptyDirectories();
      
      console.log('文件清理任务完成');
    } catch (error) {
      console.error('文件清理任务失败:', error);
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles() {
    try {
      if (!fs.existsSync(this.tempDir)) {
        return;
      }

      const files = fs.readdirSync(this.tempDir);
      let cleanedCount = 0;

      for (const file of files) {
        const filePath = path.join(this.tempDir, file);
        const stats = fs.statSync(filePath);
        
        // 检查文件年龄
        const fileAge = Date.now() - stats.mtime.getTime();
        
        if (fileAge > this.tempFileMaxAge) {
          try {
            fs.unlinkSync(filePath);
            cleanedCount++;
            
            // 记录清理日志
            await this.logCleanup(filePath, stats.size, 'temp_file_expired');
          } catch (error) {
            console.error(`删除临时文件失败: ${filePath}`, error);
          }
        }
      }

      if (cleanedCount > 0) {
        console.log(`清理了 ${cleanedCount} 个过期临时文件`);
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  }

  /**
   * 清理孤儿文件
   */
  async cleanupOrphanFiles() {
    try {
      // 获取数据库中所有的文件路径
      const dbFiles = await this.getDbFilePaths();
      const dbFileSet = new Set(dbFiles);

      // 扫描上传目录
      const uploadFiles = await this.scanUploadDirectory();
      let cleanedCount = 0;

      for (const fileInfo of uploadFiles) {
        // 检查文件是否在数据库中
        const relativePath = path.relative(this.uploadDir, fileInfo.fullPath);
        const normalizedPath = relativePath.replace(/\\/g, '/');
        
        if (!dbFileSet.has(normalizedPath) && !dbFileSet.has('/' + normalizedPath)) {
          // 检查文件年龄
          const fileAge = Date.now() - fileInfo.mtime.getTime();
          
          if (fileAge > this.orphanFileMaxAge) {
            try {
              fs.unlinkSync(fileInfo.fullPath);
              cleanedCount++;
              
              // 记录清理日志
              await this.logCleanup(fileInfo.fullPath, fileInfo.size, 'orphan_file');
            } catch (error) {
              console.error(`删除孤儿文件失败: ${fileInfo.fullPath}`, error);
            }
          }
        }
      }

      if (cleanedCount > 0) {
        console.log(`清理了 ${cleanedCount} 个孤儿文件`);
      }
    } catch (error) {
      console.error('清理孤儿文件失败:', error);
    }
  }

  /**
   * 获取数据库中所有文件路径
   */
  async getDbFilePaths() {
    try {
      const result = await db.execute(
        'SELECT FilePath FROM CU_feedback_media WHERE Status = 1'
      );
      
      return result[0].map(row => row.FilePath);
    } catch (error) {
      console.error('获取数据库文件路径失败:', error);
      return [];
    }
  }

  /**
   * 扫描上传目录
   */
  async scanUploadDirectory() {
    const files = [];
    
    const scanDir = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stats = fs.statSync(fullPath);
          
          if (stats.isDirectory()) {
            // 跳过temp目录
            if (item !== 'temp') {
              scanDir(fullPath);
            }
          } else if (stats.isFile()) {
            files.push({
              fullPath,
              size: stats.size,
              mtime: stats.mtime
            });
          }
        }
      } catch (error) {
        console.error(`扫描目录失败: ${dir}`, error);
      }
    };

    scanDir(this.uploadDir);
    return files;
  }

  /**
   * 清理空目录
   */
  async cleanupEmptyDirectories() {
    try {
      const cleanupDir = (dir) => {
        try {
          const items = fs.readdirSync(dir);
          
          // 递归清理子目录
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stats = fs.statSync(fullPath);
            
            if (stats.isDirectory()) {
              cleanupDir(fullPath);
            }
          }
          
          // 检查目录是否为空
          const remainingItems = fs.readdirSync(dir);
          if (remainingItems.length === 0 && dir !== this.uploadDir) {
            fs.rmdirSync(dir);
            console.log(`删除空目录: ${dir}`);
          }
        } catch (error) {
          // 忽略目录删除错误
        }
      };

      cleanupDir(this.uploadDir);
    } catch (error) {
      console.error('清理空目录失败:', error);
    }
  }

  /**
   * 记录清理日志
   */
  async logCleanup(filePath, fileSize, reason) {
    try {
      await db.execute(
        `INSERT INTO CU_file_cleanup_log (FilePath, FileSize, CleanupReason, CleanupTime)
         VALUES (?, ?, ?, GETDATE())`,
        [filePath, fileSize, reason]
      );
    } catch (error) {
      console.error('记录清理日志失败:', error);
    }
  }

  /**
   * 手动清理指定文件
   */
  async cleanupFile(filePath, reason = 'manual') {
    try {
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        fs.unlinkSync(filePath);
        
        await this.logCleanup(filePath, stats.size, reason);
        console.log(`手动清理文件: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`手动清理文件失败: ${filePath}`, error);
      return false;
    }
  }

  /**
   * 获取清理统计信息
   */
  async getCleanupStats(days = 7) {
    try {
      const result = await db.execute(
        `SELECT 
           CleanupReason,
           COUNT(*) as FileCount,
           SUM(FileSize) as TotalSize
         FROM CU_file_cleanup_log 
         WHERE CleanupTime >= DATEADD(day, -?, GETDATE())
         GROUP BY CleanupReason`,
        [days]
      );
      
      return result[0];
    } catch (error) {
      console.error('获取清理统计失败:', error);
      return [];
    }
  }

  /**
   * 停止清理调度器
   */
  stopCleanupSchedule() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
      console.log('文件清理调度器已停止');
    }
  }
}

module.exports = FileCleanupManager;
