const formatDate = date => {
  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  if(month<10)
  {
      month='0'+month.toString();
  }
  if(day<10)
  {
      day='0'+day.toString();
  }
//   const hour = date.getHours()
//   const minute = date.getMinutes()
//   const second = date.getSeconds()

  return year.toString()+month.toString()+day.toString()
}

const formatNumber = n => {
  n = n.toString()  
  return n[1] ? n : `0${n}`
}


/**
 * 以prommise存储本地缓存数据
 * @param {string} key 
 * @param {any} data 
 * @returns {object} 成功状态
 */
const setStorage=(key,data)=>{
    return new Promise((resolve,reject)=>{
        wx.setStorage({
            key,
            data,
            encrypt:true,
            success:(res)=>{
                resolve(res);
            },
            fail:(err)=>{
                wx.showToast({
                  title: '应用数据存储失败，请稍后再试！',
                })
            }
        })
    })
}


/**
 * 以promise获取缓存数据
 * @param {string} key 
 * @returns {object}promise对象
 */
const getStorage=(key)=>{
    return new Promise((resolve,reject)=>{
        wx.getStorage({
            key,
            encrypt:true,
            success:(res)=>{
                resolve(res.data);
            },
            fail:(err)=>{
                reject(err);
            }
        })
    })
}

/**
 * 以promise获取用户头像
 * @returns {object}promise对象
 */
const getUserProfile=()=>{
    return new Promise((resolve,reject)=>{
        wx.getUserProfile({
            desc: '获取用户信息进行登陆授权',
            success:(res)=>{
                resolve(res.userInfo.avatarUrl);
            },
            fail:(err)=>{
                reject(err);
            }
          })
    })
}


const wxLogin=()=>{
    return new Promise((resolve,reject)=>{
        wx.login({
            success:(res)=>{
                console.log('rr',res.code);
                resolve(res.code);
            },
            fail:(err)=>{
                wx.showLoading({
                  title: '应用数据读取失败，请稍后',
                })
            }
        })
    })
}


module.exports = {
    setStorage,
    getStorage,
    wxLogin,
    getUserProfile,
    formatDate
}
