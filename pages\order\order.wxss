/* wxss文件 */
page{
    width: 100%;
    height: 100vh;
    background-color:rgb(223, 223, 223);
}
.navigaterbar
{
    display: flex;
    justify-content:space-around;
    height: 200rpx;
    width: 94%;
    margin-left: 3%;
    margin-right: 3%;
    flex-wrap: wrap;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    padding-top: 20rpx;
    /* background-color: rgb(223, 223, 223); */
    border: 2px double gray; 
    background-color: white;
    border-radius: 10rpx;
}
.currentbar
{
    text-align: center;
    margin-bottom: 20rpx;
    width: 150rpx;
    line-height: 80rpx;
    height: 80rpx;
    margin-left: 16rpx;
    margin-right: 16rpx;
    border:1px solid rgb(18, 150, 219);
    border-radius: 10rpx;
}
.OrderList{
    
    margin-bottom: 20rpx;
    width: 94%;
    margin-left: 3%;
    margin-right: 3%;
    background-color: rgb(223, 223, 223);
    border-radius: 10rpx;
}
.inTitle{
    margin-top: 10rpx;
    margin-left: 3%;
    font-size: large;
}