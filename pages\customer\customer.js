// pages/customer/customer.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        motto:"hello world",
        userinfo:{
            id:0,
            word:"hello world"
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        console.log("页面正在下拉");
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        console.log("页面触底了");
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },
    pagePullDownReferch(){
        wx.startPullDownRefresh();
        setTimeout(()=>{
            wx.stopPullDownRefresh();
        },1000)
    }
})