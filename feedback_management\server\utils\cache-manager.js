/**
 * 内存缓存管理器
 * 用于缓存频繁查询的数据，减少数据库压力
 */

const logger = require('./logger');

class CacheManager {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map(); // Time To Live
    this.defaultTTL = 5 * 60 * 1000; // 默认5分钟过期
    
    // 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60 * 1000); // 每分钟清理一次
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间（毫秒），默认使用defaultTTL
   */
  set(key, value, ttl = this.defaultTTL) {
    try {
      this.cache.set(key, value);
      this.ttl.set(key, Date.now() + ttl);
      
      logger.debug('缓存设置成功', { key, ttl });
    } catch (error) {
      logger.error('设置缓存失败', { key, error });
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存值或null
   */
  get(key) {
    try {
      // 检查是否过期
      const expireTime = this.ttl.get(key);
      if (!expireTime || Date.now() > expireTime) {
        this.delete(key);
        return null;
      }

      const value = this.cache.get(key);
      logger.debug('缓存命中', { key });
      return value;
    } catch (error) {
      logger.error('获取缓存失败', { key, error });
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    this.ttl.clear();
    logger.info('所有缓存已清空');
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, expireTime] of this.ttl.entries()) {
      if (now > expireTime) {
        this.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('清理过期缓存', { cleanedCount });
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      totalKeys: this.cache.size,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  /**
   * 生成用户反馈缓存键
   * @param {string} userId - 用户ID
   * @param {string} companyId - 公司ID
   * @param {number} limit - 限制数量
   * @param {number} offset - 偏移量
   */
  getUserFeedbackKey(userId, companyId, limit = 50, offset = 0) {
    return `user_feedback:${userId}:${companyId}:${limit}:${offset}`;
  }

  /**
   * 生成分组反馈缓存键
   * @param {string} userId - 用户ID
   * @param {string} companyId - 公司ID
   */
  getGroupedFeedbackKey(userId, companyId) {
    return `grouped_feedback:${userId}:${companyId}`;
  }

  /**
   * 生成任务反馈缓存键
   * @param {string} taskNumber - 任务编号
   */
  getTaskFeedbackKey(taskNumber) {
    return `task_feedback:${taskNumber}`;
  }

  /**
   * 生成媒体文件缓存键
   * @param {string} feedbackId - 反馈ID
   */
  getMediaKey(feedbackId) {
    return `media:${feedbackId}`;
  }

  /**
   * 生成项目列表缓存键
   * @param {string} userId - 用户ID
   * @param {string} companyId - 公司ID
   */
  getProjectListKey(userId, companyId) {
    return `project_list:${userId}:${companyId}`;
  }

  /**
   * 使缓存失效（当数据更新时调用）
   * @param {string} pattern - 缓存键模式
   */
  invalidatePattern(pattern) {
    let invalidatedCount = 0;
    
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.delete(key);
        invalidatedCount++;
      }
    }

    if (invalidatedCount > 0) {
      logger.info('缓存失效', { pattern, invalidatedCount });
    }
  }

  /**
   * 使用户相关缓存失效
   * @param {string} userId - 用户ID
   */
  invalidateUserCache(userId) {
    this.invalidatePattern(`user_feedback:${userId}`);
    this.invalidatePattern(`grouped_feedback:${userId}`);
    this.invalidatePattern(`project_list:${userId}`);
  }

  /**
   * 使任务相关缓存失效
   * @param {string} taskNumber - 任务编号
   */
  invalidateTaskCache(taskNumber) {
    this.invalidatePattern(`task_feedback:${taskNumber}`);
  }

  /**
   * 使反馈相关缓存失效
   * @param {string} feedbackId - 反馈ID
   */
  invalidateFeedbackCache(feedbackId) {
    this.invalidatePattern(`media:${feedbackId}`);
  }

  /**
   * 获取或设置缓存（如果不存在则执行函数并缓存结果）
   * @param {string} key - 缓存键
   * @param {Function} fn - 获取数据的函数
   * @param {number} ttl - 过期时间
   */
  async getOrSet(key, fn, ttl = this.defaultTTL) {
    try {
      // 先尝试从缓存获取
      let value = this.get(key);
      if (value !== null) {
        return value;
      }

      // 缓存未命中，执行函数获取数据
      logger.debug('缓存未命中，执行查询', { key });
      value = await fn();
      
      // 将结果存入缓存
      if (value !== null && value !== undefined) {
        this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      logger.error('getOrSet执行失败', { key, error });
      throw error;
    }
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
    logger.info('缓存管理器已销毁');
  }
}

// 创建全局缓存实例
const cacheManager = new CacheManager();

// 监听进程退出事件，清理资源
process.on('SIGINT', () => cacheManager.destroy());
process.on('SIGTERM', () => cacheManager.destroy());
process.on('exit', () => cacheManager.destroy());

module.exports = cacheManager;
