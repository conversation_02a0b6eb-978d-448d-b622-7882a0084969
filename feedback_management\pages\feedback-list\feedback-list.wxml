<!-- pages/feedback-list/feedback-list.wxml -->
<view class="container">
  <!-- 任务单信息头部 -->
  <view class="header" wx:if="{{taskInfo}}">
    <view class="task-info">
      <view class="task-title">
        <text class="task-number">任务单号：{{taskInfo.task_number}}</text>
        <text class="part-name">部位名称：{{taskInfo.part_name}}</text>
      </view>
      <view class="task-details">
        <text class="strength-grade">强度等级：{{taskInfo.strength_grade}}</text>
        <text class="scheduled-time">计划时间：{{taskInfo.scheduled_time}}</text>
      </view>
      <view class="project-details">
        <text class="project-name">工程名称：{{taskInfo.project_name}}</text>
        <text class="construction-unit">施工单位：{{taskInfo.construction_unit}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索框 -->
  <view class="search-bar" wx:if="{{!taskId}}">
    <input class="search-input" placeholder="搜索工程、任务单、反馈内容..." value="{{searchKeyword}}" bindinput="onSearchInput" />
  </view>



  <!-- 调试信息 -->
  <view class="debug-info" wx:if="{{!loading && !taskId}}" style="font-size: 24rpx;">
    <text>工程数量: {{filteredGroupedFeedbacks.length}}，现场信息反馈数量: {{totalFeedbackCount}}</text>
  </view>

  <!-- 分类反馈单列表 -->
  <view class="grouped-feedback-list" wx:if="{{!loading && !taskId && filteredGroupedFeedbacks.length > 0}}">
    <view class="project-group" wx:for="{{filteredGroupedFeedbacks}}" wx:key="id" wx:for-item="project">
      <!-- 工程标题 -->
      <view class="project-header" bindtap="onToggleProject" data-project-id="{{project.id}}">
        <view class="project-info">
          <text class="project-name">工程名称：{{project.name}}</text>
          <text class="project-unit" wx:if="{{project.construction_unit}}">施工单位：{{project.construction_unit}}</text>
        </view>
        <view class="expand-icon {{expandedProjects[project.id] ? 'expanded' : ''}}">◀</view>
      </view>

      <!-- 任务单列表 -->
      <view class="task-list {{expandedProjects[project.id] ? 'expanded' : 'collapsed'}}">
        <view class="task-group" wx:for="{{project.tasks}}" wx:key="id" wx:for-item="task">
          <!-- 任务单标题 -->
          <view class="task-header" bindtap="onToggleTask" data-task-id="{{task.id || task.task_number}}">
            <view class="task-info">
              <text class="task-number">{{task.task_number}}</text>
              <text class="part-name" wx:if="{{task.part_name}}">{{task.part_name}}</text>
              <text class="feedback-count">({{task.feedbacks.length}}条反馈)</text>
            </view>
            <view class="expand-icon {{expandedTasks[task.id || task.task_number] ? 'expanded' : ''}}">◀</view>
          </view>

          <!-- 反馈单列表 -->
          <view class="feedback-list-container {{expandedTasks[task.id || task.task_number] ? 'expanded' : 'collapsed'}}">
            <view class="feedback-list">
            <!-- 有反馈单时显示反馈单列表 -->
            <view wx:if="{{task.feedbacks.length > 0}}">
              <view class="feedback-item" wx:for="{{task.feedbacks}}" wx:key="id" wx:for-item="feedback">
                <view class="feedback-info" bindtap="onFeedbackTap" data-feedback="{{feedback}}">
                  <view class="feedback-content">
                    <view class="category-info" wx:if="{{feedback.category}}">
                      <text class="category-label">类别：</text>
                      <text class="category-name">{{feedback.category}}</text>
                    </view>
                    <view class="feedback-user-info">
                      <text class="feedback-label">反馈员：</text>
                      <text class="feedback-name">{{feedback.feedback_user_name}}</text>
                    </view>
                    <view class="notes" wx:if="{{feedback.notes}}">
                      <text class="notes-text">{{feedback.notes}}</text>
                    </view>
                    <view class="media-info" wx:if="{{feedback.media_count > 0}}">
                      <text class="media-count">📎 {{feedback.media_count}} 个附件</text>
                    </view>
                    <view class="feedback-time">
                      <text class="time-text">{{feedback.feedback_time_text}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 没有反馈单时显示提示和创建按钮 -->
            <view wx:else class="no-feedback-tip">
              <text class="tip-text">该任务单还没有反馈记录</text>
              <button class="btn btn-primary btn-small" bindtap="onCreateFeedbackForTask" data-task-id="{{task.id}}">
                创建反馈
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    </view>
  </view>

  <!-- 原有的平铺列表（用于特定任务单的反馈记录） -->
  <view class="feedback-list" wx:if="{{!loading && taskId && feedbacks.length > 0}}">
    <view class="feedback-item" wx:for="{{feedbacks}}" wx:key="id">
      <view class="feedback-info" bindtap="onFeedbackTap" data-feedback="{{item}}">
        <view class="feedback-content">
          <!-- 工程和施工单位信息 -->
          <view class="project-info" wx:if="{{taskInfo}}">
            <text class="project-name">工程名称：{{taskInfo.project_name}}</text>
            <text class="project-unit" wx:if="{{taskInfo.construction_unit}}">施工单位：{{taskInfo.construction_unit}}</text>
          </view>
          <view class="category-info" wx:if="{{item.category}}">
            <text class="category-label">类别：</text>
            <text class="category-name">{{item.category}}</text>
          </view>
          <view class="feedback-user-info">
            <text class="feedback-label">反馈员：</text>
            <text class="feedback-name">{{item.feedback_user_name}}</text>
          </view>
          <view class="notes" wx:if="{{item.notes}}">
            <text class="notes-text">{{item.notes}}</text>
          </view>
          <view class="media-info" wx:if="{{item.media_count > 0}}">
            <text class="media-count">📎 {{item.media_count}} 个附件</text>
          </view>
          <view class="feedback-time">
            <text class="time-text">{{item.feedback_time_text}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && ((taskId && feedbacks.length === 0) || (!taskId && filteredGroupedFeedbacks.length === 0))}}">
    <view class="empty-icon">📋</view>
    <text class="empty-text">暂无现场信息反馈记录</text>
    <text class="empty-desc">{{taskId ? '该任务单还没有现场信息反馈记录' : (searchKeyword ? '没有找到匹配的反馈记录' : '您还没有创建任何现场信息反馈记录')}}</text>
  </view>
  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>
