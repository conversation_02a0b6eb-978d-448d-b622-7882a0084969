/* pages/task-list/task-list.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.project-title {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.project-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.project-code {
  font-size: 28rpx;
  color: #999;
}

.project-company {
  font-size: 28rpx;
  color: #666;
}

.construction-unit {
  font-size: 28rpx;
  color: #999;
}

/* 项目统计信息 */
.project-stats {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007bff;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 操作按钮 */
.action-bar {
  margin-bottom: 20rpx;
}

.btn {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #1296DB, #42a5f5);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #ee4343, #f8783d);
  color: white;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 筛选器样式 */
.filter-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.filter-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 0;
  display: block;
}

/* 筛选按钮组 */
.filter-buttons {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.filter-button {
  flex: 1;
  min-width: 0;
  padding: 16rpx 8rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  text-align: center;
  transition: all 0.3s ease;
}

.filter-button:active {
  transform: scale(0.95);
}

.filter-button.active {
  background: #e3f2fd;
  border-color: #1976d2;
}

.button-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-button.active .button-text {
  color: #1976d2;
  font-weight: bold;
}



/* 任务单列表 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.task-item {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
}

.task-info {
  flex: 1;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.task-info:active {
  transform: scale(0.98);
}





/* 任务单头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.task-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.supply-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.supply-status.pending {
  background-color: #fff3e0;
  color: #f57c00;
}

.supply-status.supplying {
  background-color: #e3f2fd;
  color: #1976d2;
}

.supply-status.completed {
  background-color: #e8f5e8;
  color: #4caf50;
}

.supply-status.cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

/* 任务单内容 */
.task-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.task-part,
.task-concrete,
.task-volume,
.task-feedback-user,
.task-time {
  display: flex;
  align-items: flex-start;
  font-size: 28rpx;
}

.label {
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 任务单底部 */
.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.feedback-count {
  font-size: 26rpx;
  color: #1976d2;
}

.arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  color: #666;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 12rpx;
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 30rpx;
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.modal-warning {
  font-size: 24rpx;
  color: #f44336;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-footer .btn {
  flex: 1;
  border-radius: 0;
  border-right: 1rpx solid #eee;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
}

.modal-footer .btn:last-child {
  border-right: none;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}


