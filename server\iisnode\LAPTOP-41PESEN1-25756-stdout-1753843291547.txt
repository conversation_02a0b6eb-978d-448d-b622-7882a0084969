上传目录结构初始化完成
[2025-07-30 02:41:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:41:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:41:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:41:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-30 02:41:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-30 02:41:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-30 02:41:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-30 02:41:32] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-30 02:41:32] [DEBUG] 缓存设置成功 | {"key":"user_feedback:2015493:1007:20:0","ttl":180000}
[2025-07-30 02:41:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:41:35] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:41:35] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:41:36] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:41:36] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:41:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:41:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:41:39] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:41:39] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:41:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:41:40] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-30 02:41:41] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:41:41] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:41:43] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:41:43] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:41:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-30 02:41:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:41:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:43:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-30 02:43:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:43:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
工程查询结果行数: 2
任务单查询结果行数: 4
用户反馈查询结果行数: 10
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 2
总任务单数量: 4
总反馈数量: 10
[2025-07-30 02:43:05] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-30 02:43:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:43:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:43:06] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:43:06] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:43:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:43:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:43:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:43:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:43:28] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:43:28] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:44:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:08] [DEBUG] 工程统计信息查询成功 | {"total_tasks":25,"supplying_tasks":0,"completed_tasks":12,"total_feedbacks":0}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2106617
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 2: B05-2106636
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 3: B05-2106630
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
[2025-07-30 02:44:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2106617
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 2: B05-2106636
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 3: B05-2106630
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
[2025-07-30 02:44:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-30 02:44:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2106617
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 2: B05-2106636
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 3: B05-2106630
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
天地图代理请求: 纬度=24.5118, 经度=118.14577
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城',
    location: { lon: 118.14577, lat: 24.5118 },
    addressComponent: {
      address: '金湖三里30-46禹洲·香槟城',
      town: '禾山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '西北',
      county: '湖里区',
      city_code: '*********',
      address_position: '西北',
      poi: '禹洲·香槟城北门',
      province_code: '*********',
      town_code: '*********003',
      province: '福建省',
      road: '金湖路',
      road_distance: 67,
      address_distance: 31,
      poi_distance: 31
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-07-30 02:44:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2106617
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 2: B05-2106636
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 3: B05-2106630
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
[2025-07-30 02:44:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-30 02:44:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2106617
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 2: B05-2106636
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 3: B05-2106630
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
天地图代理请求: 纬度=24.5118, 经度=118.14577
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城',
    location: { lon: 118.14577, lat: 24.5118 },
    addressComponent: {
      address: '金湖三里30-46禹洲·香槟城',
      town: '禾山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '西北',
      county: '湖里区',
      city_code: '*********',
      address_position: '西北',
      poi: '禹洲·香槟城北门',
      province_code: '*********',
      town_code: '*********003',
      province: '福建省',
      road: '金湖路',
      road_distance: 67,
      address_distance: 31,
      poi_distance: 31
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-07-30 02:44:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2106617
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 2: B05-2106636
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
任务 3: B05-2106630
  scheduled_time: Thu Jul 15 2021 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1626307200000
[2025-07-30 02:44:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:44:48] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:44:48] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-30 02:44:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:44:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:46:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-30 02:46:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-30 02:46:30] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-30 02:46:30] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
