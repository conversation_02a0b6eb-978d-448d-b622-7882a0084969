/**
 * SQL Server数据库连接配置
 * 反馈管理系统的主数据库配置文件
 */

const sql = require('mssql');
const config = require('./index');
const logger = require('../utils/logger');

// 获取SQL Server数据库配置
const dbConfig = {
  server: config.get('sqlserver.server'),
  port: config.get('sqlserver.port'),
  user: config.get('sqlserver.user'),
  password: config.get('sqlserver.password'),
  database: config.get('sqlserver.database'),
  options: {
    encrypt: config.get('sqlserver.encrypt', false), // 如果使用Azure SQL，设置为true
    trustServerCertificate: config.get('sqlserver.trustServerCertificate', true), // 本地开发时设置为true
    enableArithAbort: true,
    requestTimeout: config.get('sqlserver.requestTimeout', 30000),
    connectionTimeout: config.get('sqlserver.connectionTimeout', 30000)
  },
  pool: {
    max: config.get('sqlserver.poolMax', 10),
    min: config.get('sqlserver.poolMin', 0),
    idleTimeoutMillis: config.get('sqlserver.idleTimeout', 30000),
    acquireTimeoutMillis: config.get('sqlserver.acquireTimeout', 60000)
  }
};

// 创建连接池
let pool = null;

/**
 * 获取数据库连接池
 */
async function getPool() {
  if (!pool) {
    try {
      pool = await sql.connect(dbConfig);
      logger.success('SQL Server连接池创建成功', {
        server: dbConfig.server,
        database: dbConfig.database
      });
    } catch (error) {
      logger.error('SQL Server连接池创建失败', error);
      throw error;
    }
  }
  return pool;
}

/**
 * 执行SQL查询（纯SQL Server版本，无MySQL转换）
 * @param {string} query - SQL查询语句
 * @param {Array} params - 查询参数
 * @returns {Promise} 查询结果
 */
async function executeRaw(query, params = []) {
  try {
    const pool = await getPool();
    const request = pool.request();

    logger.debug('执行SQL查询', { query, params });

    // 添加参数
    if (params && params.length > 0) {
      params.forEach((param, index) => {
        request.input(`param${index}`, param);
      });

      // 替换查询中的?为@param0, @param1等
      let paramIndex = 0;
      query = query.replace(/\?/g, () => `@param${paramIndex++}`);
    }

    // 检查是否是INSERT语句，如果是，添加SCOPE_IDENTITY()来获取插入的ID
    const isInsert = /^\s*INSERT\s+INTO/i.test(query.trim());
    if (isInsert) {
      // 如果查询不以分号结尾，添加分号
      if (!query.trim().endsWith(';')) {
        query += ';';
      }
      query += ' SELECT SCOPE_IDENTITY() as insertId;';
    }

    logger.debug('最终执行的SQL', { finalQuery: query });
    const result = await request.query(query);
    logger.debug('SQL执行结果', { result });
    return result;
  } catch (error) {
    logger.error('SQL Server查询执行失败', { query, params, error });
    throw error;
  }
}



/**
 * 获取数据库连接（用于事务操作）
 * @returns {Promise<Object>} 数据库连接对象
 */
async function getConnection() {
  const pool = await getPool();
  const transaction = new sql.Transaction(pool);

  // 创建一个兼容MySQL风格的连接对象
  const connection = {
    // 开始事务
    beginTransaction: async () => {
      await transaction.begin();
    },

    // 提交事务
    commit: async () => {
      await transaction.commit();
    },

    // 回滚事务
    rollback: async () => {
      await transaction.rollback();
    },

    // 执行查询（纯SQL Server版本）
    execute: async (query, params = []) => {
      const request = transaction.request();

      logger.debug('事务中执行SQL查询', { query, params });

      // 添加参数
      if (params && params.length > 0) {
        params.forEach((param, index) => {
          request.input(`param${index}`, param);
        });

        // 替换查询中的?为@param0, @param1等
        let paramIndex = 0;
        query = query.replace(/\?/g, () => `@param${paramIndex++}`);
      }

      // 检查是否是INSERT语句
      const isInsert = /^\s*INSERT\s+INTO/i.test(query.trim());
      if (isInsert) {
        if (!query.trim().endsWith(';')) {
          query += ';';
        }
        query += ' SELECT SCOPE_IDENTITY() as insertId;';
      }

      logger.debug('事务中最终执行的SQL', { finalQuery: query });
      const result = await request.query(query);
      logger.debug('事务中SQL执行结果', { result });
      return formatResult(result);
    },

    // 释放连接
    release: () => {
      // SQL Server的连接池会自动管理连接，这里不需要特殊处理
    }
  };

  return connection;
}

/**
 * 执行带事务的SQL操作
 * @param {Function} operations - 事务操作函数
 * @returns {Promise} 操作结果
 */
async function executeTransaction(operations) {
  const pool = await getPool();
  const transaction = new sql.Transaction(pool);

  try {
    await transaction.begin();
    const result = await operations(transaction);
    await transaction.commit();
    return result;
  } catch (error) {
    await transaction.rollback();
    logger.error('SQL Server事务执行失败', error);
    throw error;
  }
}

/**
 * 测试数据库连接
 */
async function testConnection() {
  try {
    const pool = await getPool();
    await pool.request().query('SELECT 1 as test');
    logger.success('SQL Server数据库连接测试成功', {
      server: dbConfig.server,
      database: dbConfig.database
    });
    return true;
  } catch (error) {
    logger.error('SQL Server数据库连接测试失败', error);
    return false;
  }
}

/**
 * 优雅关闭数据库连接池
 */
async function closePool() {
  try {
    if (pool) {
      await pool.close();
      pool = null;
      logger.info('SQL Server连接池已关闭');
    }
  } catch (error) {
    logger.error('关闭SQL Server连接池失败', error);
  }
}

/**
 * 格式化查询结果为统一格式
 * @param {Object} result - SQL Server查询结果
 * @returns {Array} 格式化后的结果
 */
function formatResult(result) {
  let insertId = null;
  let affectedRows = 0;
  let recordset = [];

  // 处理SQL Server的结果结构
  if (result.recordsets && result.recordsets.length > 0) {
    // 如果有多个结果集（INSERT + SELECT SCOPE_IDENTITY()的情况）
    if (result.recordsets.length > 1) {
      // 第一个结果集是主查询结果
      recordset = result.recordsets[0] || [];
      // 第二个结果集包含insertId
      const insertIdResult = result.recordsets[1];
      if (insertIdResult && insertIdResult.length > 0 && insertIdResult[0].insertId) {
        insertId = insertIdResult[0].insertId;
      }
    } else {
      // 只有一个结果集
      recordset = result.recordsets[0] || [];
      // 检查是否包含insertId字段
      if (recordset.length > 0 && recordset[0].insertId !== undefined) {
        insertId = recordset[0].insertId;
        recordset = []; // INSERT操作不返回数据行
      }
    }
  } else if (result.recordset) {
    // 兼容旧的recordset格式
    recordset = result.recordset;
    if (recordset.length > 0 && recordset[0].insertId !== undefined) {
      insertId = recordset[0].insertId;
      recordset = []; // INSERT操作不返回数据行
    }
  }

  // 获取影响的行数
  if (result.rowsAffected && result.rowsAffected.length > 0) {
    // 对于UPDATE/DELETE操作，取第一个值
    // 对于INSERT操作，可能有两个值（INSERT + SELECT SCOPE_IDENTITY）
    affectedRows = result.rowsAffected[0] || 0;
  }

  return [recordset, { insertId, affectedRows }];
}

// 监听进程退出事件，优雅关闭连接池
process.on('SIGINT', closePool);
process.on('SIGTERM', closePool);
process.on('exit', closePool);

// 导出数据库接口
module.exports = {
  // 数据库连接池管理
  getPool,
  getConnection,
  executeTransaction,
  testConnection,
  closePool,

  // 主要查询接口（返回统一格式）
  execute: async (query, params) => {
    const result = await executeRaw(query, params);
    return formatResult(result);
  },

  // 原始查询接口（返回SQL Server原生结果）
  executeRaw,

  // SQL Server特有的类型
  sql
};

// 初始化时测试连接
testConnection().catch(error => {
  logger.error('SQL Server初始化连接测试失败', error);
});
