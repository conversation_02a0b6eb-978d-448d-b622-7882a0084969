// pages/profile/profile.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    companies: [],
    currentCompany: null,
    showChangePassword: false,
    passwordForm: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    passwordMatch: {
      show: false,
      isMatch: false,
      message: "",
    },
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserProfile();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserProfile();
  },

  // 加载用户信息
  loadUserProfile() {
    const userInfo = wx.getStorageSync("userInfo");
    const companies = wx.getStorageSync("companies") || [];
    const currentCompany = wx.getStorageSync("currentCompany");

    this.setData({
      userInfo,
      companies,
      currentCompany,
    });

    // 从服务器获取最新的用户信息
    this.fetchUserProfile();
  },

  // 从服务器获取用户信息
  fetchUserProfile() {
    const token = wx.getStorageSync("authToken");
    if (!token) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    const app = getApp();
    wx.request({
      url: `${app.globalData.baseUrl}/api/auth/profile`,
      method: "GET",
      header: {
        authorization: token,
        "content-type": "application/json",
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data && res.data.success) {
          const data = res.data.data;
          const user = data.user;
          const companies = data.companies;
          const currentCompany = data.currentCompany;

          // 更新本地存储
          wx.setStorageSync("userInfo", user);
          wx.setStorageSync("companies", companies);
          if (currentCompany) {
            wx.setStorageSync("currentCompany", currentCompany);
          }

          this.setData({
            userInfo: user,
            companies: companies,
            currentCompany: currentCompany || null,
          });
        } else if (res.statusCode === 401) {
          // 未授权，跳转到登录页
          wx.redirectTo({
            url: "/pages/login/login",
          });
        }
      },
      fail: (error) => {
        console.error("获取用户信息失败:", error);
        wx.showToast({
          title: "获取用户信息失败",
          icon: "none",
        });
      },
    });
  },

  // 显示修改密码弹窗
  showChangePasswordModal() {
    this.setData({
      showChangePassword: true,
      passwordForm: {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
    });
  },

  // 隐藏修改密码弹窗
  hideChangePasswordModal() {
    this.setData({
      showChangePassword: false,
      passwordForm: {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
      passwordMatch: {
        show: false,
        isMatch: false,
        message: "",
      },
    });
  },

  // 输入旧密码
  onOldPasswordInput(e) {
    this.setData({
      "passwordForm.oldPassword": e.detail.value,
    });
  },

  // 输入新密码
  onNewPasswordInput(e) {
    const newPassword = e.detail.value;
    this.setData({
      "passwordForm.newPassword": newPassword,
    });
  },

  // 输入确认密码
  onConfirmPasswordInput(e) {
    const confirmPassword = e.detail.value;
    const { newPassword } = this.data.passwordForm;

    this.setData({
      "passwordForm.confirmPassword": confirmPassword,
    });

    // 检查密码匹配
    if (confirmPassword.length > 0) {
      const isMatch = newPassword === confirmPassword;
      this.setData({
        passwordMatch: {
          show: true,
          isMatch: isMatch,
          message: isMatch ? "密码匹配" : "密码不匹配",
        },
      });
    } else {
      this.setData({
        passwordMatch: {
          show: false,
          isMatch: false,
          message: "",
        },
      });
    }
  },

  // 提交修改密码
  submitChangePassword() {
    const { passwordForm } = this.data;

    // 基础验证
    if (
      !passwordForm.oldPassword ||
      !passwordForm.newPassword ||
      !passwordForm.confirmPassword
    ) {
      wx.showToast({
        title: "请填写完整信息",
        icon: "none",
      });
      return;
    }

    // 检查新密码是否与旧密码相同
    if (passwordForm.oldPassword === passwordForm.newPassword) {
      wx.showToast({
        title: "新密码不能与旧密码相同",
        icon: "none",
      });
      return;
    }

    // 确认密码验证
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      wx.showToast({
        title: "两次输入的新密码不一致",
        icon: "none",
      });
      return;
    }

    this.setData({ loading: true });

    const token = wx.getStorageSync("authToken");
    const app = getApp();
    wx.request({
      url: `${app.globalData.baseUrl}/api/auth/change-password`,
      method: "POST",
      header: {
        authorization: token,
        "content-type": "application/json",
      },
      data: {
        oldPassword: passwordForm.oldPassword,
        newPassword: passwordForm.newPassword,
      },
      success: (res) => {
        this.setData({ loading: false });

        if (res.statusCode === 200 && res.data && res.data.success) {
          wx.showToast({
            title: "密码修改成功",
            icon: "success",
            duration: 2000,
          });

          // 延迟关闭弹窗，让用户看到成功提示
          setTimeout(() => {
            this.hideChangePasswordModal();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data?.message || "密码修改失败",
            icon: "none",
            duration: 3000,
          });
        }
      },
      fail: (error) => {
        this.setData({ loading: false });
        console.error("修改密码失败:", error);
        wx.showToast({
          title: "网络错误，请检查网络连接",
          icon: "none",
          duration: 3000,
        });
      },
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: "确认退出",
      content: "确定要退出登录吗？",
      success: (res) => {
        if (res.confirm) {
          this.performLogout();
        }
      },
    });
  },

  // 执行退出登录
  performLogout() {
    const token = wx.getStorageSync("authToken");
    const app = getApp();

    // 调用服务器登出接口
    wx.request({
      url: `${app.globalData.baseUrl}/api/auth/logout`,
      method: "POST",
      header: {
        authorization: token,
        "content-type": "application/json",
      },
      complete: () => {
        // 无论服务器响应如何，都清除本地存储
        wx.removeStorageSync("authToken");
        wx.removeStorageSync("userInfo");
        wx.removeStorageSync("companies");
        wx.removeStorageSync("currentCompany");

        // 跳转到登录页
        wx.redirectTo({
          url: "/pages/login/login",
        });
      },
    });
  },
});
