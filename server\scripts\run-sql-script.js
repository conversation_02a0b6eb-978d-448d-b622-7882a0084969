require('dotenv').config();
const sql = require('mssql');
const fs = require('fs');
const path = require('path');

async function runSqlScript(scriptPath) {
  try {
    console.log(`开始执行SQL脚本: ${scriptPath}`);

    // 数据库配置
    const dbConfig = {
      server: process.env.SQLSERVER_HOST || 'localhost',
      port: parseInt(process.env.SQLSERVER_PORT) || 1433,
      user: process.env.SQLSERVER_USER || 'sa',
      password: process.env.SQLSERVER_PASSWORD || '123456',
      database: process.env.SQLSERVER_DB || 'NBSTEST',
      encrypt: process.env.SQLSERVER_ENCRYPT === 'true' || false,
      trustServerCertificate: process.env.SQLSERVER_TRUST_CERT === 'true' || true,
      requestTimeout: 30000,
      connectionTimeout: 30000,
      pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
      }
    };

    console.log('数据库配置:', {
      server: dbConfig.server,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.user
    });

    // 读取SQL脚本文件
    const fullScriptPath = path.resolve(scriptPath);
    if (!fs.existsSync(fullScriptPath)) {
      throw new Error(`SQL脚本文件不存在: ${fullScriptPath}`);
    }

    const sqlScript = fs.readFileSync(fullScriptPath, 'utf8');
    console.log(`SQL脚本文件读取成功，长度: ${sqlScript.length} 字符`);

    // 连接数据库
    const pool = await sql.connect(dbConfig);
    console.log('数据库连接成功');

    // 执行SQL脚本
    const result = await pool.request().query(sqlScript);
    console.log('SQL脚本执行成功');
    
    if (result.recordset && result.recordset.length > 0) {
      console.log('查询结果:');
      console.table(result.recordset);
    }

    // 关闭连接
    await pool.close();
    console.log('🎉 SQL脚本执行完成！');
    
  } catch (error) {
    console.error('❌ SQL脚本执行失败:', error);
    process.exit(1);
  }
}

// 获取命令行参数
const scriptPath = process.argv[2];
if (!scriptPath) {
  console.error('请提供SQL脚本文件路径');
  console.log('用法: node run-sql-script.js <script-path>');
  process.exit(1);
}

// 运行脚本
runSqlScript(scriptPath);
