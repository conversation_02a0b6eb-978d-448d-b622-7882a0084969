/**
 * 简化的性能监控中间件
 * 只监控响应时间，减少性能开销
 */

const logger = require('../utils/logger');

/**
 * 简化的性能监控中间件
 */
function performanceMonitor(req, res, next) {
  const startTime = Date.now();

  // 监听响应结束事件
  res.on('finish', () => {
    const duration = Date.now() - startTime;

    // 只记录慢请求
    if (duration > 2000) {
      logger.warn('慢请求检测', {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration: `${duration}ms`
      });
    }
  });

  next();
}

/**
 * 慢查询检测中间件
 */
function slowQueryDetector(threshold = 2000) {
  return (req, res, next) => {
    const startTime = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - startTime;

      if (duration > threshold) {
        logger.warn('检测到慢请求', {
          method: req.method,
          url: req.url,
          duration: `${duration}ms`,
          threshold: `${threshold}ms`
        });
      }
    });

    next();
  };
}

/**
 * 内存使用监控
 */
function memoryMonitor(threshold = 500 * 1024 * 1024) { // 默认500MB阈值
  return (req, res, next) => {
    const memoryUsage = process.memoryUsage();

    if (memoryUsage.heapUsed > threshold) {
      logger.warn('内存使用过高', {
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        threshold: `${Math.round(threshold / 1024 / 1024)}MB`,
        url: req.url
      });
    }

    next();
  };
}

module.exports = {
  performanceMonitor,
  slowQueryDetector,
  memoryMonitor
};
