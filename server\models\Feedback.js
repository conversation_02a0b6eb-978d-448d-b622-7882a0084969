const db = require("../config/database");
const logger = require('../utils/logger');
const cacheManager = require('../utils/cache-manager');

class Feedback {
  // 创建现场信息反馈记录
  static async create(feedbackData) {
    const {
      task_number,
      feedback_user_id,
      feedback_time,
      notes,
      category,
      longitude,
      latitude,
      location_desc,
      location_status
    } = feedbackData;

    try {
      // 使用新的CU_feedbacks表，注意列名匹配，包含位置信息
      const sql = `INSERT INTO dbo.CU_feedbacks
                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
      const params = [
        task_number,
        feedback_user_id,
        feedback_time,
        notes,
        category,
        longitude || null,
        latitude || null,
        location_desc || null,
        location_status || 'unavailable'
      ];

      const result = await db.execute(sql, params);
      const feedbackId = result[1].insertId || result[0].insertId;

      logger.info('现场信息反馈记录创建成功', {
        feedbackId,
        task_number,
        feedback_user_id,
        location_status
      });

      // 清除相关缓存
      cacheManager.invalidateUserCache(feedback_user_id);
      cacheManager.invalidateTaskCache(task_number);

      return feedbackId;
    } catch (error) {
      logger.error('创建现场信息反馈记录失败', {
        error: error.message,
        feedbackData
      });
      throw error;
    }
  }

  // 根据ID查找现场信息反馈记录
  static async findById(id) {
    try {
      // 首先获取基本的反馈记录信息，包含位置信息
      const result = await db.execute(
        `
        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,
               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,
               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,
               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,
               i.LocationDesc as location_desc, i.LocationStatus as location_status,
               -- 计算最近操作时间（取UpdatedAt和CreatedAt的最大值）
               CASE
                 WHEN i.UpdatedAt > i.CreatedAt THEN i.UpdatedAt
                 ELSE i.CreatedAt
               END as last_operation_time
        FROM dbo.CU_feedbacks i
        WHERE i.Id = ? AND i.Status = 1
      `,
        [id]
      );

      if (!result[0] || result[0].length === 0) {
        return null;
      }

      const feedback = result[0][0];

      // 尝试获取任务和项目信息（如果失败则使用默认值）
      try {
        const taskResult = await db.execute(
          `
          SELECT xpo.BillNo as task_number,
                 xpo.X_JZPart as part_name,
                 CONCAT(
                   ISNULL(xpo.MaterialId, ''),
                   CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,
                   ISNULL(xpo.X_ImperviousId, ''),
                   CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,
                   ISNULL(xpo.X_FolderId, '')
                 ) as strength_grade,
                 CASE
                   WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0
                   THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)
                   ELSE NULL
                 END as scheduled_time,
                 cp.ProjectName as project_name,
                 ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit
          FROM dbo.X_ppProduceOrder xpo
          LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
          WHERE xpo.BillNo = ?
        `,
          [feedback.task_number]
        );

        if (taskResult[0] && taskResult[0].length > 0) {
          Object.assign(feedback, taskResult[0][0]);
        } else {
          // 如果找不到任务信息，使用默认值
          feedback.part_name = '未知部位';
          feedback.strength_grade = '';
          feedback.scheduled_time = null;
          feedback.project_name = '未知项目';
          feedback.construction_unit = 'Unknown';
        }
      } catch (taskError) {
        console.warn('获取任务信息失败，使用默认值:', taskError.message);
        feedback.part_name = '未知部位';
        feedback.strength_grade = '';
        feedback.scheduled_time = null;
        feedback.project_name = '未知项目';
        feedback.construction_unit = 'Unknown';
      }

      // 尝试获取用户名信息
      try {
        const userResult = await db.execute(
          `
          SELECT cgp.PersonName as feedback_user_name
          FROM dbo.comPerson cper
          LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId
          WHERE cper.PersonId = ?
        `,
          [feedback.feedback_user_id]
        );

        if (userResult[0] && userResult[0].length > 0) {
          feedback.feedback_user_name = userResult[0][0].feedback_user_name;
        } else {
          feedback.feedback_user_name = '未知用户';
        }
      } catch (userError) {
        console.warn('获取用户信息失败，使用默认值:', userError.message);
        feedback.feedback_user_name = '未知用户';
      }

      feedback.supply_status = 'pending';
      return feedback;

    } catch (error) {
      console.error('查找反馈记录失败:', error);

      // 检查是否是数据库文件问题
      if (error.message && error.message.includes('设备未就绪')) {
        const dbError = new Error('数据库服务暂时不可用，请稍后重试');
        dbError.code = 'DATABASE_UNAVAILABLE';
        dbError.originalError = error;
        throw dbError;
      }

      throw error;
    }
  }

  // 获取任务单的现场信息反馈记录列表（使用task_number，优化版本，带缓存）
  static async getByTaskNumber(taskNumber) {
    const cacheKey = cacheManager.getTaskFeedbackKey(taskNumber);

    return await cacheManager.getOrSet(cacheKey, async () => {
      const result = await db.execute(
        `
        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,
               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,
               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,
               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,
               i.LocationDesc as location_desc, i.LocationStatus as location_status,
               cgp.PersonName as feedback_user_name,
               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count,
               -- 计算最近操作时间（取UpdatedAt和CreatedAt的最大值）
               CASE
                 WHEN i.UpdatedAt > i.CreatedAt THEN i.UpdatedAt
                 ELSE i.CreatedAt
               END as last_operation_time
        FROM dbo.CU_feedbacks i
        LEFT JOIN dbo.comPerson cp ON i.FeedbackUserId = cp.PersonId
        LEFT JOIN dbo.comGroupPerson cgp ON cp.PersonId = cgp.PersonId
        WHERE i.TaskNumber = ? AND i.Status = 1
        ORDER BY last_operation_time DESC
      `,
        [taskNumber]
      );
      return result[0];
    }, 5 * 60 * 1000); // 缓存5分钟
  }

  // 保持向后兼容的方法
  static async getByTaskId(taskId) {
    // 这个方法现在需要先通过taskId找到task_number，然后调用getByTaskNumber
    // 但在新的数据库结构中，我们主要使用task_number
    return this.getByTaskNumber(taskId);
  }

  // 获取用户的所有现场信息反馈记录列表（优化版本，带缓存）
  static async getByUserId(userId, companyId, limit = 50, offset = 0) {
    const cacheKey = cacheManager.getUserFeedbackKey(userId, companyId, limit, offset);

    return await cacheManager.getOrSet(cacheKey, async () => {
      const result = await db.execute(
        `
        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,
               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,
               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,
               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,
               i.LocationDesc as location_desc, i.LocationStatus as location_status,
               cgp.PersonName as feedback_user_name,
               xpo.X_JZPart as part_name,
               cp.ProjectName as project_name,
               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,
               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count,
               -- 计算最近操作时间（取UpdatedAt和CreatedAt的最大值）
               CASE
                 WHEN i.UpdatedAt > i.CreatedAt THEN i.UpdatedAt
                 ELSE i.CreatedAt
               END as last_operation_time
        FROM CU_feedbacks i
        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId
        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId
        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo
        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
        WHERE i.FeedbackUserId = ? AND i.Status = 1 AND cp.X_OrgId = ?
        ORDER BY last_operation_time DESC
        OFFSET ? ROWS FETCH NEXT ? ROWS ONLY
      `,
        [userId, companyId, offset, limit]
      );
      return result[0];
    }, 3 * 60 * 1000); // 缓存3分钟
  }

  // 获取用户的分类反馈单数据（按工程和任务单分类，优化版本，带缓存）
  static async getGroupedByUserId(userId, companyId) {
    const cacheKey = cacheManager.getGroupedFeedbackKey(userId, companyId);

    return await cacheManager.getOrSet(cacheKey, async () => {
      try {
        console.log("开始获取分类反馈单数据，用户ID:", userId, "公司ID:", companyId);

      // 第一步：查询用户有反馈记录的工程（优化查询）
      const projectResult = await db.execute(
        `
        SELECT DISTINCT
          cp.ProjectId as project_id,
          cp.ProjectName as project_name,
          cp.ProjectId as project_code,
          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit
        FROM dbo.comProject cp WITH (NOLOCK)
        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId
        INNER JOIN dbo.CU_feedbacks f
          ON xpo.BillNo = f.TaskNumber
        WHERE cp.X_OrgId = ? AND f.FeedbackUserId = ? AND f.Status = 1
        ORDER BY cp.ProjectName
        `,
        [companyId, userId]
      );

      // 第二步：查询这些工程下的所有任务单（优化查询，限制数量）
      const projectIds = projectResult[0].map(p => p.project_id);
      let taskResult = [[]];

      if (projectIds.length > 0) {
        const placeholders = projectIds.map(() => '?').join(',');
        taskResult = await db.execute(
          `
          SELECT TOP 200
            xpo.BillNo as task_number,
            xpo.X_JZPart as part_name,
            CONCAT(
              ISNULL(xpo.MaterialId, ''),
              CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,
              ISNULL(xpo.X_ImperviousId, ''),
              CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,
              ISNULL(xpo.X_FolderId, '')
            ) as strength_grade,
            CASE
              WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0
              THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)
              ELSE NULL
            END as scheduled_time,
            ISNULL(xpo.X_SupplyState, 0) as supply_status,
            xpo.ProjectId as task_project_id
          FROM dbo.X_ppProduceOrder xpo WITH (NOLOCK)
          WHERE xpo.ProjectId IN (${placeholders})
          ORDER BY xpo.ProjectId, xpo.DemandBeginDate DESC, xpo.BillNo DESC
          `,
          projectIds
        );
      }

      // 第三步：查询用户在这些工程下的所有反馈记录（优化查询）
      let feedbackResult = [[]];
      if (projectIds.length > 0) {
        const placeholders = projectIds.map(() => '?').join(',');
        feedbackResult = await db.execute(
          `
          SELECT
            f.Id as feedback_id,
            f.TaskNumber as task_number,
            FORMAT(f.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,
            CAST(f.Notes AS NVARCHAR(MAX)) as feedback_notes,
            f.Category as feedback_category,
            cgp.PersonName as feedback_user_name,
            (SELECT COUNT(*) FROM dbo.CU_feedback_media fm WHERE fm.FeedbackId = f.Id AND fm.Status = 1) as media_count,
            -- 计算最近操作时间（取UpdatedAt和CreatedAt的最大值）
            CASE
              WHEN f.UpdatedAt > f.CreatedAt THEN f.UpdatedAt
              ELSE f.CreatedAt
            END as last_operation_time
          FROM dbo.CU_feedbacks f
          LEFT JOIN dbo.comPerson cper ON f.FeedbackUserId = cper.PersonId
          LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId
          INNER JOIN dbo.X_ppProduceOrder xpo ON f.TaskNumber = xpo.BillNo
          WHERE f.FeedbackUserId = ? AND f.Status = 1
            AND xpo.ProjectId IN (${placeholders})
          ORDER BY f.FeedbackTime DESC
          `,
          [userId, ...projectIds]
        );
      }

      const projectRows = projectResult[0];
      const taskRows = taskResult[0];
      const feedbackRows = feedbackResult[0];

      console.log("工程查询结果行数:", projectRows.length);
      console.log("任务单查询结果行数:", taskRows.length);
      console.log("用户反馈查询结果行数:", feedbackRows.length);

      // 创建反馈映射表，按任务单号分组
      const feedbackMap = new Map();
      feedbackRows.forEach(feedback => {
        if (!feedbackMap.has(feedback.task_number)) {
          feedbackMap.set(feedback.task_number, []);
        }
        feedbackMap.get(feedback.task_number).push({
          id: feedback.feedback_id,
          feedback_time: feedback.feedback_time,
          notes: feedback.feedback_notes,
          category: feedback.feedback_category,
          feedback_user_name: feedback.feedback_user_name,
          media_count: feedback.media_count,
          last_operation_time: feedback.last_operation_time
        });
      });

      // 创建工程映射表
      const projectMap = new Map();

      // 先添加所有工程
      projectRows.forEach(project => {
        projectMap.set(project.project_id, {
          id: project.project_id,
          name: project.project_name,
          code: project.project_code,
          construction_unit: project.construction_unit,
          tasks: new Map()
        });
      });

      // 添加任务单到对应工程（如果工程存在）或创建"其他"工程
      taskRows.forEach(task => {
        let targetProject = null;

        // 尝试找到匹配的工程
        if (task.task_project_id) {
          targetProject = projectMap.get(task.task_project_id);
        }

        // 如果没有找到匹配的工程，添加到第一个工程或创建"其他"分类
        if (!targetProject && projectMap.size > 0) {
          // 添加到"其他"分类
          const otherId = 'OTHER_TASKS';
          if (!projectMap.has(otherId)) {
            projectMap.set(otherId, {
              id: otherId,
              name: '其他任务单',
              code: otherId,
              construction_unit: 'Unknown',
              tasks: new Map()
            });
          }
          targetProject = projectMap.get(otherId);
        }

        if (targetProject) {
          const taskFeedbacks = feedbackMap.get(task.task_number) || [];

          targetProject.tasks.set(task.task_number, {
            id: task.task_number, // 添加id字段，使用task_number作为唯一标识
            task_number: task.task_number,
            part_name: task.part_name,
            strength_grade: task.strength_grade,
            scheduled_time: task.scheduled_time,
            supply_status: task.supply_status,
            feedbacks: taskFeedbacks
          });
        }
      });

      // 转换为数组格式并排序
      const result_array = Array.from(projectMap.values()).map(project => {
        // 对任务单按最新时间排序（有反馈的优先，然后按计划时间排序）
        const sortedTasks = Array.from(project.tasks.values()).map(task => {
          // 确保每个任务都有id字段
          if (!task.id) {
            task.id = task.task_number; // 使用task_number作为id
          }
          return task;
        }).sort((a, b) => {
          // 获取任务单的最新操作时间
          const aLatestOperation = a.feedbacks.length > 0 ?
            Math.max(...a.feedbacks.map(f => new Date(f.last_operation_time).getTime())) : 0;
          const bLatestOperation = b.feedbacks.length > 0 ?
            Math.max(...b.feedbacks.map(f => new Date(f.last_operation_time).getTime())) : 0;

          // 如果都有反馈，按最新操作时间排序
          if (aLatestOperation > 0 && bLatestOperation > 0) {
            return bLatestOperation - aLatestOperation;
          }

          // 有反馈的优先显示
          if (aLatestOperation > 0 && bLatestOperation === 0) return -1;
          if (aLatestOperation === 0 && bLatestOperation > 0) return 1;

          // 都没有反馈时，按计划时间排序（最新的在前）
          const aScheduledTime = a.scheduled_time ? new Date(a.scheduled_time).getTime() : 0;
          const bScheduledTime = b.scheduled_time ? new Date(b.scheduled_time).getTime() : 0;
          return bScheduledTime - aScheduledTime;
        });

        // 对每个任务单的反馈按最近操作时间倒序排序
        sortedTasks.forEach(task => {
          task.feedbacks.sort((a, b) => new Date(b.last_operation_time) - new Date(a.last_operation_time));
        });

        return {
          ...project,
          tasks: sortedTasks
        };
      });

      // 过滤掉没有任务单的工程
      const filteredResult = result_array.filter(project => project.tasks.length > 0);

      // 对工程按最新活动时间排序
      filteredResult.sort((a, b) => {
        // 获取工程的最新活动时间（最近操作时间或任务计划时间）
        const getLatestActivityTime = (project) => {
          let latestTime = 0;

          project.tasks.forEach(task => {
            // 检查最近操作时间
            task.feedbacks.forEach(feedback => {
              const operationTime = new Date(feedback.last_operation_time).getTime();
              if (operationTime > latestTime) latestTime = operationTime;
            });

            // 检查任务计划时间
            if (task.scheduled_time) {
              const scheduledTime = new Date(task.scheduled_time).getTime();
              if (scheduledTime > latestTime) latestTime = scheduledTime;
            }
          });

          return latestTime;
        };

        const aLatestTime = getLatestActivityTime(a);
        const bLatestTime = getLatestActivityTime(b);

        // 按最新活动时间倒序排序
        return bLatestTime - aLatestTime;
      });

      console.log("=== Feedback.getGroupedByUserId 查询结果 ===");
      console.log("工程数量:", filteredResult.length);
      console.log("总任务单数量:", filteredResult.reduce((total, project) => total + project.tasks.length, 0));
      console.log("总反馈数量:", filteredResult.reduce((total, project) =>
        total + project.tasks.reduce((taskTotal, task) => taskTotal + task.feedbacks.length, 0), 0));

        return filteredResult;
      } catch (error) {
        console.error("获取分类反馈单数据失败:", error.message);
        logger.error('获取分类反馈单数据失败', error);
        throw error;
      }
    }, 2 * 60 * 1000); // 缓存2分钟
  }

  // 更新现场信息反馈记录
  static async update(id, feedbackData) {
    const fields = [];
    const values = [];

    Object.keys(feedbackData).forEach((key) => {
      if (feedbackData[key] !== undefined && key !== "id") {
        fields.push(`${key} = ?`);
        // 处理temperature和humidity的空值
        let value = feedbackData[key];
        if ((key === "temperature" || key === "humidity") && value === "") {
          value = null;
        }
        values.push(value);
      }
    });

    // 自动添加UpdatedAt字段
    fields.push("UpdatedAt = ?");
    values.push(new Date());

    if (fields.length === 1) return false; // 只有UpdatedAt字段，说明没有实际更新内容

    values.push(id);
    const result = await db.execute(
      `UPDATE CU_feedbacks SET ${fields.join(", ")} WHERE Id = ?`,
      values
    );

    const success = result[1].affectedRows > 0;

    // 如果更新成功，清除相关缓存
    if (success) {
      // 获取反馈记录信息以清除相关缓存
      try {
        const feedbackResult = await db.execute(
          `SELECT FeedbackUserId, TaskNumber FROM CU_feedbacks WHERE Id = ?`,
          [id]
        );
        if (feedbackResult[0] && feedbackResult[0].length > 0) {
          const feedback = feedbackResult[0][0];
          cacheManager.invalidateUserCache(feedback.FeedbackUserId);
          cacheManager.invalidateTaskCache(feedback.TaskNumber);
        }
      } catch (cacheError) {
        logger.warn('清除缓存失败', { id, error: cacheError.message });
      }
    }

    return success;
  }





  // 添加多媒体文件
  static async addMedia(feedbackId, mediaData) {
    const { file_type, file_name, file_path, file_size } = mediaData;

    // 使用首字母大写的字段名
    const result = await db.execute(
      `INSERT INTO CU_feedback_media
       (FeedbackId, MediaType, FileName, FilePath, FileSize)
       VALUES (?, ?, ?, ?, ?)`,
      [feedbackId, file_type, file_name, file_path, file_size]
    );

    return result[1].insertId;
  }

  // 获取现场信息反馈记录的多媒体文件（优化版本）
  static async getMedia(feedbackId) {
    try {


      // 简化查询，移除可能有问题的索引提示
      const result = await db.execute(
        `SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt
         FROM CU_feedback_media
         WHERE FeedbackId = ? AND Status = 1
         ORDER BY CreatedAt ASC`,
        [feedbackId]
      );



      if (result[0] && result[0].length > 0) {
        // 手动映射字段名，确保兼容性
        const mappedData = result[0].map(item => {
          let filePath = item.FilePath;

          // 确保文件路径格式正确
          if (filePath && !filePath.startsWith('http')) {
            // 标准化路径分隔符
            filePath = filePath.replace(/\\/g, '/');

            // 如果路径已经是/api/files/格式，保持不变
            if (filePath.startsWith('/api/files/')) {
              // 保持原样
            }
            // 如果路径是/upload/格式，转换为/api/files/格式
            else if (filePath.startsWith('/upload/')) {
              filePath = filePath.replace('/upload/', '/api/files/');
            }
            // 如果路径是upload/格式（相对路径），转换为/api/files/格式
            else if (filePath.startsWith('upload/')) {
              filePath = '/api/files/' + filePath.substring(7); // 去掉'upload/'
            }
            // 如果路径不是以/开头，假设是相对于upload目录的路径
            else if (!filePath.startsWith('/')) {
              filePath = '/api/files/' + filePath;
            }
            // 其他情况，添加/api/files/前缀
            else {
              filePath = '/api/files' + filePath;
            }
          }

          return {
            id: item.Id,
            feedback_id: item.FeedbackId,
            file_type: item.MediaType,
            file_name: item.FileName,
            file_path: filePath,
            file_size: item.FileSize,
            duration: item.Duration,
            upload_time: item.UploadTime,
            status: item.Status,
            created_at: item.CreatedAt,
            updated_at: item.UpdatedAt
          };
        });



        return mappedData;
      }

      return [];
    } catch (error) {
      console.error(`查询媒体文件失败:`, error);
      return [];
    }
  }



  // 获取现场信息反馈统计信息（从新数据库获取）
  static async getStats(filters = {}) {
    try {
      logger.debug('获取反馈统计信息', { filters });

      const { company_id, project_id, date_from, date_to } = filters;

      // 构建基础查询条件
      let whereConditions = ['f.Status = 1'];
      let params = [];

      // 添加公司过滤条件
      if (company_id) {
        whereConditions.push('cp.X_OrgId = ?');
        params.push(company_id);
      }

      // 添加项目过滤条件
      if (project_id) {
        whereConditions.push('cp.ProjectId = ?');
        params.push(project_id);
      }

      // 添加日期过滤条件
      if (date_from) {
        whereConditions.push('f.FeedbackTime >= ?');
        params.push(date_from);
      }

      if (date_to) {
        whereConditions.push('f.FeedbackTime <= ?');
        params.push(date_to);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 查询统计信息
      const statsQuery = `
        SELECT
          COUNT(DISTINCT f.Id) as total_feedbacks,
          COUNT(DISTINCT f.TaskNumber) as feedback_tasks,
          COUNT(DISTINCT cp.ProjectId) as feedback_projects
        FROM dbo.CU_feedbacks f
        LEFT JOIN dbo.X_ppProduceOrder xpo ON f.TaskNumber = xpo.BillNo
        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId
        ${whereClause}
      `;

      const result = await db.execute(statsQuery, params);

      const stats = {
        total_feedbacks: result[0][0]?.total_feedbacks || 0,
        feedback_tasks: result[0][0]?.feedback_tasks || 0,
        feedback_projects: result[0][0]?.feedback_projects || 0
      };

      logger.debug('统计查询结果', stats);
      return stats;
    } catch (error) {
      logger.error('获取反馈统计信息失败', error);
      throw error;
    }
  }
}

module.exports = Feedback;
