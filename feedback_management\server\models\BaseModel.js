/**
 * 数据库操作基类
 * 提供统一的数据库操作方法，包括CRUD、事务处理、分页查询等
 */

const db = require('../config/database');
const logger = require('../utils/logger');

class BaseModel {
  /**
   * 构造函数
   * @param {string} tableName - 表名
   * @param {string} primaryKey - 主键字段名，默认为'id'
   */
  constructor(tableName, primaryKey = 'id') {
    this.tableName = tableName;
    this.primaryKey = primaryKey;
  }

  /**
   * 执行SQL查询
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数数组
   * @returns {Promise<Array>} 查询结果
   */
  async query(sql, params = []) {
    try {
      logger.database('QUERY', this.tableName, { sql: sql.substring(0, 100) + '...', paramsCount: params.length });
      const [rows] = await db.execute(sql, params);
      return rows;
    } catch (error) {
      logger.error(`数据库查询失败 - 表: ${this.tableName}`, error);
      throw error;
    }
  }

  /**
   * 根据ID查找单条记录
   * @param {number} id - 记录ID
   * @param {Array} fields - 要查询的字段，默认查询所有字段
   * @returns {Promise<Object|null>} 查询结果
   */
  async findById(id, fields = ['*']) {
    const fieldStr = Array.isArray(fields) ? fields.join(', ') : fields;
    const sql = `SELECT ${fieldStr} FROM ${this.tableName} WHERE ${this.primaryKey} = ? AND status = 1`;
    const rows = await this.query(sql, [id]);
    return rows[0] || null;
  }

  /**
   * 根据条件查找单条记录
   * @param {Object} conditions - 查询条件
   * @param {Array} fields - 要查询的字段
   * @returns {Promise<Object|null>} 查询结果
   */
  async findOne(conditions = {}, fields = ['*']) {
    const { whereClause, params } = this.buildWhereClause(conditions);
    const fieldStr = Array.isArray(fields) ? fields.join(', ') : fields;
    let sql = `SELECT TOP 1 ${fieldStr} FROM ${this.tableName} ${whereClause}`;

    const rows = await this.query(sql, params);
    return rows[0] || null;
  }

  /**
   * 根据条件查找多条记录
   * @param {Object} conditions - 查询条件
   * @param {Object} options - 查询选项
   * @param {Array} options.fields - 要查询的字段
   * @param {string} options.orderBy - 排序字段
   * @param {string} options.order - 排序方向 ASC|DESC
   * @param {number} options.limit - 限制数量
   * @param {number} options.offset - 偏移量
   * @returns {Promise<Array>} 查询结果
   */
  async findMany(conditions = {}, options = {}) {
    const {
      fields = ['*'],
      orderBy = this.primaryKey,
      order = 'DESC',
      limit,
      offset
    } = options;

    const { whereClause, params } = this.buildWhereClause(conditions);
    const fieldStr = Array.isArray(fields) ? fields.join(', ') : fields;

    let sql = `SELECT ${fieldStr} FROM ${this.tableName} ${whereClause}`;

    if (orderBy) {
      sql += ` ORDER BY ${orderBy} ${order}`;
    }

    // 使用SQL Server的OFFSET/FETCH语法处理分页
    if (limit) {
      const offsetValue = offset || 0;
      sql += ` ORDER BY ${this.primaryKey} OFFSET ${offsetValue} ROWS FETCH NEXT ${limit} ROWS ONLY`;
    }

    return await this.query(sql, params);
  }

  /**
   * 分页查询
   * @param {Object} conditions - 查询条件
   * @param {number} page - 页码（从1开始）
   * @param {number} pageSize - 每页大小
   * @param {Object} options - 其他选项
   * @returns {Promise<Object>} 分页结果 {data: Array, total: number, page: number, pageSize: number}
   */
  async paginate(conditions = {}, page = 1, pageSize = 10, options = {}) {
    const offset = (page - 1) * pageSize;
    
    // 查询总数
    const total = await this.count(conditions);
    
    // 查询数据
    const data = await this.findMany(conditions, {
      ...options,
      limit: pageSize,
      offset
    });

    return {
      data,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil(total / pageSize)
    };
  }

  /**
   * 统计记录数量
   * @param {Object} conditions - 查询条件
   * @returns {Promise<number>} 记录数量
   */
  async count(conditions = {}) {
    const { whereClause, params } = this.buildWhereClause(conditions);
    const sql = `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`;
    const rows = await this.query(sql, params);
    return rows[0].count;
  }

  /**
   * 创建记录
   * @param {Object} data - 要插入的数据
   * @returns {Promise<number>} 插入的记录ID
   */
  async create(data) {
    const { fields, placeholders, values } = this.buildInsertClause(data);
    const sql = `INSERT INTO ${this.tableName} (${fields}) VALUES (${placeholders})`;

    logger.database('CREATE', this.tableName, { fieldsCount: Object.keys(data).length });

    const result = await db.execute(sql, values);
    return result[1].insertId;
  }

  /**
   * 批量创建记录
   * @param {Array} dataArray - 要插入的数据数组
   * @returns {Promise<Array>} 插入的记录ID数组
   */
  async createMany(dataArray) {
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      return [];
    }

    const insertIds = [];

    try {
      for (const data of dataArray) {
        const { fields, placeholders, values } = this.buildInsertClause(data);
        const sql = `INSERT INTO ${this.tableName} (${fields}) VALUES (${placeholders})`;
        const result = await db.execute(sql, values);
        insertIds.push(result[1].insertId);
      }

      logger.database('CREATE_MANY', this.tableName, { count: dataArray.length });
      return insertIds;
    } catch (error) {
      logger.error(`批量创建失败 - 表: ${this.tableName}`, error);
      throw error;
    }
  }

  /**
   * 更新记录
   * @param {number} id - 记录ID
   * @param {Object} data - 要更新的数据
   * @returns {Promise<boolean>} 是否更新成功
   */
  async update(id, data) {
    const { setClause, values } = this.buildUpdateClause(data);
    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`;
    
    logger.database('UPDATE', this.tableName, { id, fieldsCount: Object.keys(data).length });
    
    const [result] = await db.execute(sql, [...values, id]);
    return result.affectedRows > 0;
  }

  /**
   * 根据条件更新记录
   * @param {Object} conditions - 更新条件
   * @param {Object} data - 要更新的数据
   * @returns {Promise<number>} 受影响的行数
   */
  async updateMany(conditions, data) {
    const { setClause, values: updateValues } = this.buildUpdateClause(data);
    const { whereClause, params: whereParams } = this.buildWhereClause(conditions);
    const sql = `UPDATE ${this.tableName} SET ${setClause} ${whereClause}`;
    
    logger.database('UPDATE_MANY', this.tableName, { 
      conditions: Object.keys(conditions).length,
      fieldsCount: Object.keys(data).length 
    });
    
    const [result] = await db.execute(sql, [...updateValues, ...whereParams]);
    return result.affectedRows;
  }



  /**
   * 检查记录是否存在
   * @param {Object} conditions - 查询条件
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(conditions) {
    const count = await this.count(conditions);
    return count > 0;
  }

  /**
   * 构建WHERE子句
   * @param {Object} conditions - 查询条件
   * @returns {Object} {whereClause: string, params: Array}
   */
  buildWhereClause(conditions) {
    const keys = Object.keys(conditions);
    
    if (keys.length === 0) {
      return { whereClause: 'WHERE status = 1', params: [] };
    }

    const whereParts = [];
    const params = [];

    // 默认添加status条件
    if (!conditions.hasOwnProperty('status')) {
      whereParts.push('status = 1');
    }

    keys.forEach(key => {
      const value = conditions[key];
      
      if (value === null) {
        whereParts.push(`${key} IS NULL`);
      } else if (Array.isArray(value)) {
        const placeholders = value.map(() => '?').join(', ');
        whereParts.push(`${key} IN (${placeholders})`);
        params.push(...value);
      } else if (typeof value === 'object' && value.operator) {
        // 支持操作符，如 {operator: '>', value: 10}
        whereParts.push(`${key} ${value.operator} ?`);
        params.push(value.value);
      } else {
        whereParts.push(`${key} = ?`);
        params.push(value);
      }
    });

    const whereClause = whereParts.length > 0 ? `WHERE ${whereParts.join(' AND ')}` : '';
    return { whereClause, params };
  }

  /**
   * 构建INSERT子句
   * @param {Object} data - 插入数据
   * @returns {Object} {fields: string, placeholders: string, values: Array}
   */
  buildInsertClause(data) {
    // 添加默认字段
    const insertData = {
      ...data
    };

    // 如果没有status字段，默认设置为1
    if (!insertData.hasOwnProperty('status')) {
      insertData.status = 1;
    }

    const keys = Object.keys(insertData);
    const fields = keys.join(', ');
    const placeholders = keys.map(() => '?').join(', ');
    const values = keys.map(key => insertData[key]);

    return { fields, placeholders, values };
  }

  /**
   * 构建UPDATE子句
   * @param {Object} data - 更新数据
   * @returns {Object} {setClause: string, values: Array}
   */
  buildUpdateClause(data) {
    // 构建更新数据
    const updateData = {
      ...data
    };

    const keys = Object.keys(updateData);
    const setClause = keys.map(key => `${key} = ?`).join(', ');
    const values = keys.map(key => updateData[key]);

    return { setClause, values };
  }

  /**
   * 执行事务
   * @param {Function} callback - 事务回调函数
   * @returns {Promise<*>} 事务结果
   */
  async transaction(callback) {
    try {
      logger.database('TRANSACTION_START', this.tableName);

      const result = await db.executeTransaction(callback);
      logger.database('TRANSACTION_COMMIT', this.tableName);
      return result;
    } catch (error) {
      logger.error(`事务回滚 - 表: ${this.tableName}`, error);
      throw error;
    }
  }
}

module.exports = BaseModel;
