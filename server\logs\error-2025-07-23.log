[2025-07-23 03:17:17] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":401,"duration":"6ms","memoryDiff":{"rss":0,"heapUsed":70504,"heapTotal":0,"external":24},"timestamp":"2025-07-23T03:17:17.099Z"}
[2025-07-23 03:17:17] [ERROR] API请求失败 | {"method":"GET","url":"/","statusCode":401,"duration":"5ms","memoryDiff":{"rss":0,"heapUsed":62944,"heapTotal":0,"external":24},"timestamp":"2025-07-23T03:17:17.114Z"}
[2025-07-23 03:20:35] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:35] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 03:20:35] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:35] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:35] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 03:20:35] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:35] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"24ms","memoryDiff":{"rss":479232,"heapUsed":-429424,"heapTotal":1048576,"external":9738},"timestamp":"2025-07-23T03:20:35.806Z"}
[2025-07-23 03:20:42] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:42] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:42] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:42] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 03:20:42] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"21ms","memoryDiff":{"rss":253952,"heapUsed":257688,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T03:20:42.219Z"}
[2025-07-23 06:22:52] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"6ms","memoryDiff":{"rss":135168,"heapUsed":155200,"heapTotal":0,"external":74},"timestamp":"2025-07-23T06:22:52.800Z"}
[2025-07-23 06:28:47] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"2ms","memoryDiff":{"rss":118784,"heapUsed":61000,"heapTotal":0,"external":74},"timestamp":"2025-07-23T06:28:47.264Z"}
[2025-07-23 06:28:48] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"1ms","memoryDiff":{"rss":90112,"heapUsed":67800,"heapTotal":0,"external":74},"timestamp":"2025-07-23T06:28:48.219Z"}
[2025-07-23 06:28:48] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"3ms","memoryDiff":{"rss":86016,"heapUsed":76392,"heapTotal":0,"external":74},"timestamp":"2025-07-23T06:28:48.408Z"}
[2025-07-23 06:28:48] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"1ms","memoryDiff":{"rss":40960,"heapUsed":73672,"heapTotal":0,"external":74},"timestamp":"2025-07-23T06:28:48.529Z"}
[2025-07-23 06:28:48] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"2ms","memoryDiff":{"rss":114688,"heapUsed":79240,"heapTotal":262144,"external":74},"timestamp":"2025-07-23T06:28:48.691Z"}
[2025-07-23 06:28:48] [ERROR] API请求失败 | {"method":"POST","url":"/login","statusCode":400,"duration":"1ms","memoryDiff":{"rss":102400,"heapUsed":94576,"heapTotal":0,"external":74},"timestamp":"2025-07-23T06:28:48.792Z"}
[2025-07-23 07:10:44] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:44] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:10:44] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:44] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"637ms","memoryDiff":{"rss":3457024,"heapUsed":-10989536,"heapTotal":786432,"external":62553},"timestamp":"2025-07-23T07:10:44.264Z"}
[2025-07-23 07:10:44] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:44] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:10:44] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:44] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"286ms","memoryDiff":{"rss":2465792,"heapUsed":-5907616,"heapTotal":-208896,"external":-471057},"timestamp":"2025-07-23T07:10:44.576Z"}
[2025-07-23 07:10:45] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:45] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:45] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"1119ms","memoryDiff":{"rss":819200,"heapUsed":19043216,"heapTotal":0,"external":386198},"timestamp":"2025-07-23T07:10:45.696Z"}
[2025-07-23 07:10:46] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:46] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:10:46] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"256ms","memoryDiff":{"rss":102400,"heapUsed":-3726152,"heapTotal":1888256,"external":-362946},"timestamp":"2025-07-23T07:10:46.305Z"}
[2025-07-23 07:11:01] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:01] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:11:01] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:01] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"676ms","memoryDiff":{"rss":6234112,"heapUsed":-560216,"heapTotal":7811072,"external":-47548},"timestamp":"2025-07-23T07:11:01.374Z"}
[2025-07-23 07:11:01] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:01] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:11:01] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:01] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"48ms","memoryDiff":{"rss":32768,"heapUsed":2200936,"heapTotal":0,"external":98188},"timestamp":"2025-07-23T07:11:01.424Z"}
[2025-07-23 07:11:02] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:02] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:02] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"799ms","memoryDiff":{"rss":5931008,"heapUsed":-72887344,"heapTotal":-35655680,"external":-294233},"timestamp":"2025-07-23T07:11:02.434Z"}
[2025-07-23 07:11:03] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:03] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:11:03] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:03] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:03] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:03] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"50ms","memoryDiff":{"rss":8192,"heapUsed":704416,"heapTotal":0,"external":45680},"timestamp":"2025-07-23T07:11:03.210Z"}
[2025-07-23 07:11:03] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"68ms","memoryDiff":{"rss":8192,"heapUsed":946496,"heapTotal":0,"external":46804},"timestamp":"2025-07-23T07:11:03.220Z"}
[2025-07-23 07:11:06] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:06] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:11:06] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"638ms","memoryDiff":{"rss":864256,"heapUsed":-2298768,"heapTotal":6815744,"external":-346905},"timestamp":"2025-07-23T07:11:06.608Z"}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA17027"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA17026"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA17013"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA16096"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA210026"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA16098"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:11:45] [ERROR] SQL Server查询执行失败 | {"query":"SELECT COUNT(*) as task_count FROM dbo.X_ppProduceOrder WHERE ProjectId = @param0","params":["B05AJA210029"],"error":{"code":"EREQUEST","originalError":{"info":{"code":"ESOCKET"}},"name":"RequestError"}}
[2025-07-23 07:27:33] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:27:33] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"17ms","memoryDiff":{"rss":0,"heapUsed":263192,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:27:33.303Z"}
[2025-07-23 07:27:33] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:27:33] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"43ms","memoryDiff":{"rss":65536,"heapUsed":370240,"heapTotal":0,"external":18492},"timestamp":"2025-07-23T07:27:33.350Z"}
[2025-07-23 07:27:33] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"631ms","memoryDiff":{"rss":69632,"heapUsed":255760,"heapTotal":0,"external":9770},"timestamp":"2025-07-23T07:27:33.973Z"}
[2025-07-23 07:27:33] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:33] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"15ms","memoryDiff":{"rss":0,"heapUsed":246648,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:27:33.992Z"}
[2025-07-23 07:27:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:49] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:27:49] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:49] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"906ms","memoryDiff":{"rss":15609856,"heapUsed":14295216,"heapTotal":21495808,"external":352095},"timestamp":"2025-07-23T07:27:49.479Z"}
[2025-07-23 07:27:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:49] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:27:49] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"421ms","memoryDiff":{"rss":7716864,"heapUsed":-5584240,"heapTotal":208896,"external":-306219},"timestamp":"2025-07-23T07:27:49.926Z"}
[2025-07-23 07:28:18] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:18] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:18] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:18] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"20ms","memoryDiff":{"rss":897024,"heapUsed":272952,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:28:18.789Z"}
[2025-07-23 07:28:18] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:18] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:18] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:18] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"24ms","memoryDiff":{"rss":0,"heapUsed":252224,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:28:18.817Z"}
[2025-07-23 07:28:19] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:19] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:19] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"16ms","memoryDiff":{"rss":360448,"heapUsed":278912,"heapTotal":0,"external":9770},"timestamp":"2025-07-23T07:28:19.131Z"}
[2025-07-23 07:28:19] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:19] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:19] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"21ms","memoryDiff":{"rss":0,"heapUsed":259576,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:28:19.155Z"}
[2025-07-23 07:28:38] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:38] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:38] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:38] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"23ms","memoryDiff":{"rss":0,"heapUsed":257008,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:28:38.892Z"}
[2025-07-23 07:28:38] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:38] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:38] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:38] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"28ms","memoryDiff":{"rss":0,"heapUsed":254216,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:28:38.925Z"}
[2025-07-23 07:28:39] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:39] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:39] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"15ms","memoryDiff":{"rss":4096,"heapUsed":252688,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:28:39.275Z"}
[2025-07-23 07:28:39] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:39] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:39] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"20ms","memoryDiff":{"rss":0,"heapUsed":247848,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:28:39.300Z"}
[2025-07-23 07:28:47] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:47] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:47] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:47] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"18ms","memoryDiff":{"rss":0,"heapUsed":254888,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:28:47.517Z"}
[2025-07-23 07:28:47] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:47] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:47] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:47] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"25ms","memoryDiff":{"rss":0,"heapUsed":249624,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:28:47.545Z"}
[2025-07-23 07:28:47] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:47] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:47] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"18ms","memoryDiff":{"rss":0,"heapUsed":246544,"heapTotal":0,"external":9770},"timestamp":"2025-07-23T07:28:47.959Z"}
[2025-07-23 07:28:48] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:48] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:48] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"37ms","memoryDiff":{"rss":0,"heapUsed":247120,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:28:48.004Z"}
[2025-07-23 07:28:58] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:58] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"19ms","memoryDiff":{"rss":0,"heapUsed":250664,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:28:58.806Z"}
[2025-07-23 07:28:58] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:28:58] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"23ms","memoryDiff":{"rss":0,"heapUsed":249448,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:28:58.834Z"}
[2025-07-23 07:28:58] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"18ms","memoryDiff":{"rss":0,"heapUsed":246408,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:28:58.924Z"}
[2025-07-23 07:28:58] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:28:58] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"19ms","memoryDiff":{"rss":0,"heapUsed":245888,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:28:58.947Z"}
[2025-07-23 07:29:00] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:00] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:00] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:00] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"16ms","memoryDiff":{"rss":40960,"heapUsed":350640,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:29:00.563Z"}
[2025-07-23 07:29:00] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:00] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:00] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"13ms","memoryDiff":{"rss":4096,"heapUsed":253600,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:00.594Z"}
[2025-07-23 07:29:04] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:04] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"19ms","memoryDiff":{"rss":0,"heapUsed":250784,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:29:04.576Z"}
[2025-07-23 07:29:04] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:04] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"25ms","memoryDiff":{"rss":0,"heapUsed":249520,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:29:04.605Z"}
[2025-07-23 07:29:04] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"25ms","memoryDiff":{"rss":0,"heapUsed":246848,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:04.696Z"}
[2025-07-23 07:29:04] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:04] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"26ms","memoryDiff":{"rss":0,"heapUsed":246056,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:04.725Z"}
[2025-07-23 07:29:05] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:05] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:05] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:05] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"13ms","memoryDiff":{"rss":0,"heapUsed":250432,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:29:05.804Z"}
[2025-07-23 07:29:05] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:05] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:05] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"12ms","memoryDiff":{"rss":0,"heapUsed":246064,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:05.837Z"}
[2025-07-23 07:29:14] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:14] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:14] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"18ms","memoryDiff":{"rss":0,"heapUsed":409952,"heapTotal":0,"external":18914},"timestamp":"2025-07-23T07:29:14.345Z"}
[2025-07-23 07:29:14] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"25ms","memoryDiff":{"rss":0,"heapUsed":531760,"heapTotal":0,"external":19476},"timestamp":"2025-07-23T07:29:14.346Z"}
[2025-07-23 07:29:14] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:14] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"21ms","memoryDiff":{"rss":4096,"heapUsed":504432,"heapTotal":0,"external":35924},"timestamp":"2025-07-23T07:29:14.404Z"}
[2025-07-23 07:29:14] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"17ms","memoryDiff":{"rss":0,"heapUsed":415984,"heapTotal":0,"external":35900},"timestamp":"2025-07-23T07:29:14.405Z"}
[2025-07-23 07:29:23] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:23] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"60ms","memoryDiff":{"rss":0,"heapUsed":252288,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:29:23.398Z"}
[2025-07-23 07:29:23] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:23] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"29ms","memoryDiff":{"rss":98304,"heapUsed":249528,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:29:23.436Z"}
[2025-07-23 07:29:23] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"17ms","memoryDiff":{"rss":8192,"heapUsed":246320,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:23.788Z"}
[2025-07-23 07:29:23] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:23] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"23ms","memoryDiff":{"rss":0,"heapUsed":247544,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:23.815Z"}
[2025-07-23 07:29:28] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:28] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:28] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:28] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"17ms","memoryDiff":{"rss":0,"heapUsed":249560,"heapTotal":0,"external":9738},"timestamp":"2025-07-23T07:29:28.920Z"}
[2025-07-23 07:29:28] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:28] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 07:29:28] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:28] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"21ms","memoryDiff":{"rss":0,"heapUsed":249712,"heapTotal":0,"external":17930},"timestamp":"2025-07-23T07:29:28.945Z"}
[2025-07-23 07:29:29] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:29] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:29] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"15ms","memoryDiff":{"rss":0,"heapUsed":247632,"heapTotal":0,"external":9770},"timestamp":"2025-07-23T07:29:29.023Z"}
[2025-07-23 07:29:29] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:29] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 07:29:29] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"18ms","memoryDiff":{"rss":0,"heapUsed":246800,"heapTotal":0,"external":17962},"timestamp":"2025-07-23T07:29:29.045Z"}
[2025-07-23 08:12:42] [ERROR] API请求失败 | {"method":"POST","url":"/","statusCode":404,"duration":"2ms","memoryDiff":{"rss":86016,"heapUsed":91464,"heapTotal":0,"external":75},"timestamp":"2025-07-23T08:12:42.663Z"}
[2025-07-23 08:48:28] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:28] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 08:48:28] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:29] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:29] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-23 08:48:29] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:29] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"862ms","memoryDiff":{"rss":24387584,"heapUsed":13072000,"heapTotal":28049408,"external":330803},"timestamp":"2025-07-23T08:48:29.706Z"}
[2025-07-23 08:48:30] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:30] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:30] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"1077ms","memoryDiff":{"rss":24883200,"heapUsed":-7070112,"heapTotal":23277568,"external":37104},"timestamp":"2025-07-23T08:48:30.116Z"}
[2025-07-23 08:48:30] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:30] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-23 08:48:30] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"29ms","memoryDiff":{"rss":-294912,"heapUsed":366496,"heapTotal":0,"external":18524},"timestamp":"2025-07-23T08:48:30.149Z"}
