<view class="container">
  <view class="debug-section">
    <text class="title">用户信息调试</text>
    
    <view class="info-item">
      <text class="label">存储的userInfo:</text>
      <text class="value">{{userInfoStr}}</text>
    </view>
    
    <view class="info-item">
      <text class="label">PersonId:</text>
      <text class="value">{{userInfo.PersonId || '无'}}</text>
    </view>
    
    <view class="info-item">
      <text class="label">PersonName:</text>
      <text class="value">{{userInfo.PersonName || '无'}}</text>
    </view>
    
    <view class="info-item">
      <text class="label">Phone:</text>
      <text class="value">{{userInfo.Phone || '无'}}</text>
    </view>
    
    <view class="info-item">
      <text class="label">Company数量:</text>
      <text class="value">{{companyCount}}</text>
    </view>
    
    <button type="primary" bindtap="refreshInfo">刷新信息</button>
    <button type="default" bindtap="goBack">返回</button>
  </view>
</view>
