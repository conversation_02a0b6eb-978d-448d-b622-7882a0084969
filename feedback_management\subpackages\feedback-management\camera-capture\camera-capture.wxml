<!-- 相机拍摄页面 -->
<view class="camera-container">
  <!-- 相机预览区域 -->
  <view class="camera-preview">
    <camera
      device-position="{{devicePosition}}"
      binderror="onCameraError"
      bindstop="onCameraStop"
      bindinitdone="onCameraReady"
      class="camera-view"
      wx:if="{{!capturedMedia}}"
    >
      <!-- 拍摄控制区域 -->
      <view class="capture-controls">
        <!-- 占位元素，保持布局平衡 -->
        <view class="control-placeholder"></view>

        <!-- 拍摄按钮 -->
        <view class="capture-button-container">
          <view class="capture-button {{captureMode}}" bindtap="onCapture" bindtouchstart="onCaptureStart" bindtouchend="onCaptureEnd">
            <view class="capture-inner {{recording ? 'recording' : ''}}"></view>
          </view>
        </view>

        <!-- 切换摄像头 -->
        <view class="control-item" bindtap="onSwitchCamera">
          <text class="control-icon">🔄</text>
        </view>
      </view>

      <!-- 录像时长显示 -->
      <view class="recording-info" wx:if="{{recording}}">
        <text class="recording-time">{{recordingTime}}s / {{maxVideoDuration}}s</text>
        <view class="recording-indicator"></view>
      </view>

      <!-- 视频时长设置提示 -->
      <view class="duration-hint" wx:if="{{captureMode === 'video' && videoDurationSettings}}">
        <text class="hint-text">录制时长：{{videoDurationSettings.min_duration}}-{{videoDurationSettings.max_duration}}秒</text>
      </view>

      <!-- 调试信息显示（仅在开发模式下显示） -->
      <view class="debug-info" wx:if="{{showDebugInfo}}">
        <text class="debug-text">摄像头：{{devicePosition === 'back' ? '后置' : '前置'}}</text>
        <text class="debug-text">模式：{{captureMode === 'photo' ? '拍照' : '录像'}}</text>
      </view>
    </camera>

    <!-- 拍摄结果预览 -->
    <view class="media-preview" wx:if="{{capturedMedia}}">
      <image 
        wx:if="{{capturedMedia.type === 'photo'}}"
        src="{{capturedMedia.tempFilePath}}" 
        mode="aspectFit"
        class="preview-image"
      />
      <video
        wx:if="{{capturedMedia.type === 'video'}}"
        src="{{capturedMedia.tempFilePath}}"
        controls
        class="preview-video"
        object-fit="contain"
        direction="0"
        show-fullscreen-btn="{{false}}"
        show-play-btn="{{true}}"
        show-center-play-btn="{{true}}"
      />
      
      <!-- 预览控制按钮 -->
      <view class="preview-controls">
        <button class="btn btn-secondary" bindtap="onRetake">重拍</button>
        <button class="btn btn-primary" bindtap="onConfirm" disabled="{{uploading}}">
          {{uploading ? '上传中...' : '确认使用'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 移除自定义上传进度条，使用wx.showLoading代替 -->
</view>
