// project-list.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    currentCompany: null,
    projectList: [],
    searchKeyword: "",
    loading: false,
    showDeleteModal: false,
    selectedProject: null,


  },

  onLoad() {
    this.checkLogin();
    this.loadData();
  },

  onShow() {
    this.loadData();
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync("userInfo");
    const currentCompany = wx.getStorageSync("currentCompany");

    if (!userInfo) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    if (!currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      userInfo,
      currentCompany,
    });
  },

  // 加载数据
  async loadData() {
    if (!this.data.currentCompany) {
      return;
    }

    this.setData({
      loading: true,
      projectList: []
    });

    try {
      await this.loadProjectList();
    } catch (error) {
      console.error("加载数据失败:", error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载工程列表
  async loadProjectList() {
    try {
      const { searchKeyword } = this.data;

      const res = await app.request({
        url: "/api/projects",
        method: "GET",
        data: {
          keyword: searchKeyword,
        },
      });

      if (res.data && res.data.success) {
        const projects = res.data.data || [];

        // 为每个工程添加格式化的显示信息
        const formattedProjects = projects.map((project) => ({
          ...project,
          // 格式化任务数量显示
          taskCountText:
            project.task_count > 0
              ? `${project.task_count}个任务单`
              : "暂无任务",
          // 格式化创建时间显示
          createdTimeText: this.formatDate(project.created_at),
        }));

        this.setData({
          projectList: formattedProjects
        });
      } else {
        console.error("获取工程列表失败:", res.data);
        this.setData({ projectList: [] });

        if (res.data && res.data.message) {
          wx.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000,
          });
        }
      }
    } catch (error) {
      console.error("加载工程列表网络错误:", error);
      this.setData({ projectList: [] });

      wx.showToast({
        title: "网络连接失败",
        icon: "none",
        duration: 2000,
      });
    }
  },



  // 格式化日期显示
  formatDate(dateString) {
    if (!dateString) return "";

    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value,
    });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      projectList: []
    });
    this.loadProjectList();
  },

  // 点击工程项
  onProjectTap(e) {
    const project = e.currentTarget.dataset.project;
    wx.navigateTo({
      url: `/subpackages/task-management/task-list/task-list?projectId=${project.id}`,
    });
  },



  // 下拉刷新
  async onPullDownRefresh() {
    try {
      // 重置搜索关键词
      this.setData({
        searchKeyword: "",
        projectList: []
      });

      // 重新加载工程列表
      await this.loadProjectList();

      wx.showToast({
        title: "刷新成功",
        icon: "success",
        duration: 1500,
      });
    } catch (error) {
      console.error("下拉刷新失败:", error);
      wx.showToast({
        title: "刷新失败",
        icon: "none",
        duration: 2000,
      });
    } finally {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    }
  },




});
