/**
 * 日志工具类
 * 提供统一的日志记录功能，支持不同级别的日志输出
 */

const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = path.join(__dirname, '../logs');
    this.ensureLogDir();
  }

  /**确保日志目录存在
   */
  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**获取当前时间戳
   * @returns {string} 格式化的时间戳
   */
  getTimestamp() {
    const now = new Date();
    return now.toISOString().replace('T', ' ').substring(0, 19);
  }

  /**
   * 格式化日志消息
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {Object} meta - 额外信息
   * @returns {string} 格式化后的日志
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = this.getTimestamp();
    const metaStr = Object.keys(meta).length > 0 ? ` | ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  /**
   * 写入日志文件
   * @param {string} filename - 文件名
   * @param {string} content - 日志内容
   */
  writeToFile(filename, content) {
    try {
      const filePath = path.join(this.logDir, filename);
      fs.appendFileSync(filePath, content + '\n');
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 获取日志文件名
   * @param {string} type - 日志类型
   * @returns {string} 文件名
   */
  getLogFileName(type = 'app') {
    const date = new Date().toISOString().substring(0, 10);
    return `${type}-${date}.log`;
  }

  /**
   * 判断是否为关键信息
   * @param {string} message - 日志消息
   * @returns {boolean} 是否为关键信息
   */
  isEssentialMessage(message) {
    const essentialKeywords = [
      '启动', '关闭', '错误', '失败', '成功', '连接',
      '登录', '注册', '创建', '删除', '更新',
      'start', 'stop', 'error', 'fail', 'success', 'connect',
      'login', 'register', 'create', 'delete', 'update'
    ];

    return essentialKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 信息日志 - 仅记录关键信息
   * @param {string} message - 日志消息
   * @param {Object} meta - 额外信息
   */
  info(message, meta = {}) {
    const logMessage = this.formatMessage('info', message, meta);
    // 根据用户偏好，只在控制台输出关键信息
    if (this.isEssentialMessage(message)) {
      console.log(`${logMessage}`);
    }
    this.writeToFile(this.getLogFileName('info'), logMessage);
  }

  /**
   * 警告日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 额外信息
   */
  warn(message, meta = {}) {
    const logMessage = this.formatMessage('warn', message, meta);
    console.warn(`${logMessage}`);
    this.writeToFile(this.getLogFileName('warn'), logMessage);
  }

  /**
   * 错误日志
   * @param {string} message - 日志消息
   * @param {Error|Object} error - 错误对象或额外信息
   */
  error(message, error = {}) {
    const meta = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : error;
    
    const logMessage = this.formatMessage('error', message, meta);
    console.error(`${logMessage}`);
    this.writeToFile(this.getLogFileName('error'), logMessage);
  }

  /**
   * 调试日志 - 仅在开发环境且为关键信息时输出
   * @param {string} message - 日志消息
   * @param {Object} meta - 额外信息
   */
  debug(message, meta = {}) {
    if (process.env.NODE_ENV === 'development') {
      const logMessage = this.formatMessage('debug', message, meta);
      // 只输出关键的调试信息
      if (this.isEssentialMessage(message)) {
        console.debug(`${logMessage}`);
      }
      this.writeToFile(this.getLogFileName('debug'), logMessage);
    }
  }

  /**
   * 成功日志
   * @param {string} message - 日志消息
   * @param {Object} meta - 额外信息
   */
  success(message, meta = {}) {
    const logMessage = this.formatMessage('success', message, meta);
    console.log(`${logMessage}`);
    this.writeToFile(this.getLogFileName('info'), logMessage);
  }

  /**
   * HTTP请求日志
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {number} duration - 请求耗时（毫秒）
   */
  http(req, res, duration) {
    const { method, url, ip } = req;
    const { statusCode } = res;
    const userAgent = req.get('User-Agent') || '';
    
    const message = `${method} ${url} ${statusCode} ${duration}ms`;
    const meta = {
      ip,
      userAgent,
      userId: req.session?.user?.id || null
    };

    const logMessage = this.formatMessage('http', message, meta);
    
    // 根据状态码选择日志级别
    if (statusCode >= 500) {
      console.error(`${logMessage}`);
      this.writeToFile(this.getLogFileName('error'), logMessage);
    } else if (statusCode >= 400) {
      console.warn(`${logMessage}`);
      this.writeToFile(this.getLogFileName('warn'), logMessage);
    } else {
      console.log(`${logMessage}`);
      this.writeToFile(this.getLogFileName('access'), logMessage);
    }
  }

  /**
   * 数据库操作日志
   * @param {string} operation - 操作类型
   * @param {string} table - 表名
   * @param {Object} meta - 额外信息
   */
  database(operation, table, meta = {}) {
    const message = `Database ${operation} on ${table}`;
    const logMessage = this.formatMessage('database', message, meta);
    console.log(`${logMessage}`);
    this.writeToFile(this.getLogFileName('database'), logMessage);
  }

  /**
   * 安全相关日志
   * @param {string} event - 安全事件
   * @param {Object} meta - 额外信息
   */
  security(event, meta = {}) {
      const logMessage = this.formatMessage('security', event, meta);
    console.warn(`${logMessage}`);
    this.writeToFile(this.getLogFileName('security'), logMessage);
  }

  /**
   * 清理过期日志文件
   * @param {number} days - 保留天数，默认30天
   */
  cleanOldLogs(days = 30) {
    try {
      const files = fs.readdirSync(this.logDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          this.info(`清理过期日志文件: ${file}`);
        }
      });
    } catch (error) {
      this.error('清理日志文件失败', error);
    }
  }

  /**
   * 获取日志统计信息
   * @returns {Object} 统计信息
   */
  getLogStats() {
    try {
      const files = fs.readdirSync(this.logDir);
      const stats = {
        totalFiles: files.length,
        totalSize: 0,
        filesByType: {}
      };

      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const fileStats = fs.statSync(filePath);
        stats.totalSize += fileStats.size;

        const type = file.split('-')[0];
        if (!stats.filesByType[type]) {
          stats.filesByType[type] = { count: 0, size: 0 };
        }
        stats.filesByType[type].count++;
        stats.filesByType[type].size += fileStats.size;
      });

      return stats;
    } catch (error) {
      this.error('获取日志统计失败', error);
      return null;
    }
  }
}

// 创建单例实例
const logger = new Logger();

module.exports = logger;
