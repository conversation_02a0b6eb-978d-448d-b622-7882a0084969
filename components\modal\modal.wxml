<!-- 模态框组件模板 -->
<view class="modal-container" wx:if="{{show}}">
  <!-- 遮罩层 -->
  <view class="modal-mask" bindtap="onMaskTap"></view>
  
  <!-- 模态框内容 -->
  <view 
    class="modal-content" 
    style="width: {{width}}; max-height: {{maxHeight}}"
    catchtap="stopPropagation">
    
    <!-- 头部 -->
    <view class="modal-header" wx:if="{{title}}">
      <text class="modal-title">{{title}}</text>
      <view class="modal-close" bindtap="onClose">
        <text class="close-icon">×</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="modal-body">
      <!-- 文本内容 -->
      <text class="modal-text" wx:if="{{content}}">{{content}}</text>
      
      <!-- 自定义内容插槽 -->
      <slot name="content"></slot>
      
      <!-- 默认插槽 -->
      <slot wx:if="{{!content}}"></slot>
    </view>
    
    <!-- 底部按钮 -->
    <view class="modal-footer">
      <!-- 取消按钮 -->
      <view 
        class="modal-button cancel-button" 
        wx:if="{{showCancel}}"
        style="color: {{cancelColor}}"
        bindtap="onCancel">
        {{cancelText}}
      </view>
      
      <!-- 确认按钮 -->
      <view 
        class="modal-button confirm-button" 
        style="color: {{confirmColor}}"
        bindtap="onConfirm">
        {{confirmText}}
      </view>
    </view>
  </view>
</view>
