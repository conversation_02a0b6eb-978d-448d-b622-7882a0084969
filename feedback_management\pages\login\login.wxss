.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1296DB 0%, #42a5f5 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-placeholder {
  margin-bottom: 30rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.login-form {
  width: 100%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.login-form .form-group {
  margin-bottom: 40rpx !important;
  width: 100% !important;
}

.login-form .form-label {
  font-size: 28rpx !important;
  color: #333 !important;
  margin-bottom: 15rpx !important;
  display: block !important;
}

.login-form .form-input {
  width: 100% !important;
  height: 80rpx !important;
  padding: 0 25rpx !important;
  border: 2rpx solid #e0e0e0 !important;
  border-radius: 12rpx !important;
  font-size: 28rpx !important;
  box-sizing: border-box !important;
  background-color: #fff !important;
  line-height: 80rpx !important;
  display: block !important;
  color: #333 !important;
  margin: 0 !important;
}

.login-form .form-input::placeholder {
  color: #999 !important;
  font-size: 28rpx !important;
}

.login-form .form-input:focus {
  border-color: #1296DB !important;
  outline: none !important;
}

.login-btn {
  width: 100%;
  margin-top: 40rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}






