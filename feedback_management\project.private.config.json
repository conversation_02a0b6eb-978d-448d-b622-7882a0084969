{"libVersion": "3.8.9", "projectname": "feedback", "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "compileHotReLoad": true, "useApiHook": false, "useApiHostProcess": false, "useStaticServer": false, "useLanDebug": true, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false}, "condition": {"miniprogram": {"list": [{"name": "subpackages/task-management/task-list/task-list", "pathName": "subpackages/task-management/task-list/task-list", "query": "", "scene": null, "launchMode": "default"}, {"name": "pages/login/login", "pathName": "pages/login/login", "query": "", "launchMode": "default", "scene": null}]}}}