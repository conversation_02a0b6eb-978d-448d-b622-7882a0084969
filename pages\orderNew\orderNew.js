// pages/orderNew/orderNew.js
// pages/orderDetail/orderDetail.js
const requestOrder = require('../../request/order')
const utils = require('../../utils/util');
const config = require('../../config/common');
const httpRequest = require('../../utils/http');
const common = require('../../config/common');
Page({
    /**
     * 页面的初始数据
     */
    data: {
        userId: "",
        mainCompanyId: "",
        mainRoleId: "",
        RolesId:"",
        FromData: {},
        CarMarkValue: '',
        RepairListId:[],
        RepairList: [],
        index: '',
        WxOperaPersonId:'',
        PersonId:''
    },
    bindPickerValue: function (e) {
        this.setData({
            index: e.detail.value
        })
    },
    callcamerapage() {
        wx.navigateTo({
            url: '../orderScanCode/orderScanCode',
        })
    },
    findCarName(number) {
        if (number && number != 0) {
            let data = { FuzzyStr: { CarMark: number, CompId: this.data.mainCompanyId } };
            requestOrder.GetCarInfo(data).then(res => {
                if (res.data.status == "suc") {
                    this.setData({
                        'FromData.CarId': res.data.data[0].CarId,
                        'FromData.CarName': res.data.data[0].CarName,
                        "FromData.OldTotalMileage": res.data.data[0].TotalMileage
                    })
                }
                else {
                    this.setData({
                        FromData: {}
                    })
                }
            }).catch(err => {
                console.log("失败", err);
            })
        }
    },
    outCarMark(e) {
        console.log('out',);
        this.findCarName(e.detail.value);


    },
    cancleOrder() {
        wx.navigateBack();
    },
    formSubmit(e) {
        if (!this.data.FromData.CarName) {
            wx.showToast({
                icon: 'error',
                title: '找不到该车辆！',
            })
        }
        else if (!e.detail.value.TotalMileage) {
            wx.showModal({
                showCancel: false,
                title: '警告',
                content: '请输入累计行驶公里数！',
                success(res) {
                    if (res.confirm) {
                        console.log('用户点击确定')
                    } else if (res.cancel) {
                        console.log('用户点击取消')
                    }
                }
            })
        }
        else if (e.detail.value.TotalMileage < this.data.FromData.OldTotalMileage) {
            wx.showModal({
                showCancel: false,
                title: '警告',
                content: '累计行驶公里数小于车辆主数据公里数，请重新输入！',
                success(res) {
                    if (res.confirm) {
                        console.log('用户点击确定')
                    } else if (res.cancel) {
                        console.log('用户点击取消')
                    }
                }
            })
        }
        else {
            let repairProjectDetail1=[];
            if(this.data.index>=0)
            {
                repairProjectDetail1=[{
                    RepairProjectId:this.data.RepairListId[this.data.index]
                }]
            }
            let data = {
                ProgId: "X_CarServApply",
                RolesId:this.data.RolesId,
                master: {
                    BillDate:utils.formatDate(new Date()),
                    OrgId: this.data.mainCompanyId,
                    TypeId: config.TypeId,
                    CompId: this.data.mainCompanyId,
                    ServReason: e.detail.value.FixReason,
                    Proposer: this.data.userId,
                    FixWay: 0,
                    CarId: this.data.FromData.CarId,
                    CarName: this.data.FromData.CarName,
                    CarMark: e.detail.value.CarMark,
                    TotalMileage: e.detail.value.TotalMileage,
                    PersonId:this.data.PersonId,
                    WxOperaPersonId:this.data.WxOperaPersonId
                },
                repairProjectDetail: repairProjectDetail1
            }
            requestOrder.CreatCarServApply(data).then(res => {
                if (res.data.status == "suc") {
                    wx.showModal({
                        showCancel: false,
                        title: '创建成功',
                        content: res.data.data,
                        complete: (rres) => {
                            if (rres.confirm) {
                                wx.switchTab({
                                    url: '../order/order',
                                })
                            }
                        }
                    })
                }
                else {
                    wx.showModal({
                        showCancel: false,
                        title: '创建失败',
                        content: res.data.errorMsg,
                    })
                }
            }).catch(err => {
                console.log("失败", err);
            })
        }

    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        let data = {
            ProgId: "X_CarServApply",
        }
        requestOrder.GetRepairProject(data).then(res => {
            console.log('list', res);
            if (res.data.status == "suc") {
                let templist = [];
                let templistId =[];
                res.data.data.forEach(item => {
                    templistId.push(item.RepairProjectId)
                    templist.push(item.RepairProjectName)
                })
                this.setData({
                    RepairListId:templistId,
                    RepairList: templist
                })
            }
        })
        this.setData({
            CarMarkValue: options.qrcode
        })

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        utils.getStorage(config.userInfo).then(res => {
            let tempCompanyID = res.Company.find((item) => {
                return item.IsMain == true;
            });
            let tempMaxRole = 0;
            let tempRolesId = '';
            res.Roles.forEach((item) => {
                if(item.RoleId==common.SJRoleId)
                {
                    tempRolesId=item.RoleId;
                }
                if (tempMaxRole <= item.RoleId) {
                    tempMaxRole = item.RoleId;
                }
            })
            this.setData({
                userId: res.userId,
                mainCompanyId: tempCompanyID.CompanyId,
                mainRoleId: tempMaxRole,
                RolesId:tempRolesId,
                WxOperaPersonId:res.PersonId,
                PersonId:res.PersonId
            })
            this.findCarName(this.data.CarMarkValue);
        }).catch(err => {
            wx.switchTab({
                url: '../index/index',
            })
        })
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})