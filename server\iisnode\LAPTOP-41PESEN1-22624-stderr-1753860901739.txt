[2025-07-30 07:35:26] [WARN] 慢请求检测 | {"method":"POST","url":"/login","statusCode":200,"duration":"11448ms"}
[2025-07-30 07:35:26] [WARN] 检测到慢请求 | {"method":"POST","url":"/login","duration":"11451ms","threshold":"2000ms"}
[2025-07-30 07:35:48] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"19914ms"}
[2025-07-30 07:35:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"19915ms","threshold":"2000ms"}
[2025-07-30 07:35:48] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"20339ms"}
[2025-07-30 07:35:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"20340ms","threshold":"2000ms"}
[2025-07-30 07:35:48] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"13285ms"}
[2025-07-30 07:35:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"13287ms","threshold":"2000ms"}
[2025-07-30 07:36:06] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"17809ms"}
[2025-07-30 07:36:06] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"17809ms","threshold":"2000ms"}
[2025-07-30 07:36:07] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"18232ms"}
[2025-07-30 07:36:07] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"18233ms","threshold":"2000ms"}
[2025-07-30 07:36:07] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"18813ms"}
[2025-07-30 07:36:07] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"18814ms","threshold":"2000ms"}
[2025-07-30 07:36:26] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"18854ms"}
[2025-07-30 07:36:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"18854ms","threshold":"2000ms"}
[2025-07-30 07:36:29] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"22375ms"}
[2025-07-30 07:36:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"22376ms","threshold":"2000ms"}
[2025-07-30 07:36:29] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"21327ms"}
[2025-07-30 07:36:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"21327ms","threshold":"2000ms"}
[2025-07-30 07:36:30] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"2001ms"}
[2025-07-30 07:36:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2002ms","threshold":"2000ms"}
[2025-07-30 07:36:31] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"2383ms"}
[2025-07-30 07:36:31] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2384ms","threshold":"2000ms"}
[2025-07-30 07:36:53] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"2412ms"}
[2025-07-30 07:36:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2413ms","threshold":"2000ms"}
[2025-07-30 07:37:13] [WARN] 慢请求检测 | {"method":"POST","url":"/logout","statusCode":200,"duration":"14871ms"}
[2025-07-30 07:37:13] [WARN] 检测到慢请求 | {"method":"POST","url":"/logout","duration":"14872ms","threshold":"2000ms"}
[2025-07-30 07:37:13] [WARN] 慢请求检测 | {"method":"POST","url":"/logout","statusCode":200,"duration":"8626ms"}
[2025-07-30 07:37:13] [WARN] 检测到慢请求 | {"method":"POST","url":"/logout","duration":"8626ms","threshold":"2000ms"}
[2025-07-30 07:37:13] [WARN] 慢请求检测 | {"method":"POST","url":"/logout","statusCode":200,"duration":"11266ms"}
[2025-07-30 07:37:13] [WARN] 检测到慢请求 | {"method":"POST","url":"/logout","duration":"11266ms","threshold":"2000ms"}
[2025-07-30 07:38:21] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"29141ms"}
[2025-07-30 07:38:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"29142ms","threshold":"2000ms"}
[2025-07-30 07:38:22] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"24232ms"}
[2025-07-30 07:38:22] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"24233ms","threshold":"2000ms"}
[2025-07-30 07:38:22] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"30249ms"}
[2025-07-30 07:38:22] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"30250ms","threshold":"2000ms"}
[2025-07-30 07:38:34] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"11649ms"}
[2025-07-30 07:38:34] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"11650ms","threshold":"2000ms"}
[2025-07-30 07:38:35] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"13444ms"}
[2025-07-30 07:38:35] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"13445ms","threshold":"2000ms"}
[2025-07-30 07:38:36] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"13625ms"}
[2025-07-30 07:38:36] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"13626ms","threshold":"2000ms"}
[2025-07-30 07:39:07] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"8217ms"}
[2025-07-30 07:39:07] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"8218ms","threshold":"2000ms"}
[2025-07-30 07:39:59] [WARN] 慢请求检测 | {"method":"POST","url":"/logout","statusCode":200,"duration":"9456ms"}
[2025-07-30 07:39:59] [WARN] 检测到慢请求 | {"method":"POST","url":"/logout","duration":"9457ms","threshold":"2000ms"}
[2025-07-30 07:39:59] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"3113ms"}
[2025-07-30 07:39:59] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"3114ms","threshold":"2000ms"}
[2025-07-30 07:39:59] [WARN] 慢请求检测 | {"method":"GET","url":"/profile","statusCode":200,"duration":"12662ms"}
[2025-07-30 07:39:59] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"12663ms","threshold":"2000ms"}
[2025-07-30 07:40:16] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"16190ms"}
[2025-07-30 07:40:16] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"16191ms","threshold":"2000ms"}
[2025-07-30 07:42:13] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:13] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:42:26] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:26] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:42:32] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:32] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:42:46] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:42:46] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:38] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:38] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:45] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:45] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:49] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-30 07:43:53] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT\n          cp.PersonId as id,\n          cgp.PersonName as username,\n          cgp.Phone as phone,\n          cp.BelongOrgId as company_id,\n          cp.PersonId as person_id\n        FROM dbo.comPerson cp\n        INNER JOIN dbo.comGroupPerson cgp ON CAST(cp.PersonId AS NVARCHAR(50)) = CAST(cgp.PersonId AS NVARCHAR(50))\n        WHERE cgp.Phone = @param0 AND ISNUMERIC(cp.PersonId) = 1 AND ISNUMERIC(cgp.PersonId) = 1\n      ","params":["13959261995"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-30 07:43:53] [ERROR] 根据手机号查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
