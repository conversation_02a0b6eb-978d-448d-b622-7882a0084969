// pages/task-list/task-list.js
Page({
  data: {
    tasks: [],
    allTasks: [], // 存储所有任务单，用于筛选
    loading: true,
    projectId: null,
    projectInfo: null,
    projectStats: null,
    currentCompany: null,
    userInfo: null,
    showDeleteModal: false,
    selectedTask: null,
    // 筛选相关
    currentFilter: 'all', // 当前筛选状态：all, 0, 1, 2, 3
    filterOptions: [
      { value: 'all', label: '全部', count: 0 },
      { value: '0', label: '待供', count: 0 },
      { value: '1', label: '正供', count: 0 },
      { value: '2', label: '供毕', count: 0 },
      { value: '3', label: '作废', count: 0 }
    ]
  },

  onLoad(options) {
    this.checkLogin();

    if (options.projectId) {
      this.setData({ projectId: options.projectId });
      this.loadProjectInfo(options.projectId);
      this.loadTasks(options.projectId);
    } else {
      wx.showToast({
        title: "缺少工程参数",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.projectId) {
      this.loadTasks(this.data.projectId);
    }
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync("userInfo");
    const currentCompany = wx.getStorageSync("currentCompany");

    if (!userInfo) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    if (!currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/index/index",
        });
      }, 2000);
      return;
    }

    this.setData({
      userInfo,
      currentCompany,
    });

    console.log('用户信息:', userInfo);
  },

  // 加载工程信息
  async loadProjectInfo(projectId) {
    try {
      const app = getApp();

      const res = await app.request({
        url: `/api/projects/${projectId}`,
        method: "GET",
      });

      if (res.data.success) {
        // 后端返回的数据结构是 {project, stats}
        const projectInfo = res.data.data.project;
        const projectStats = res.data.data.stats;

        console.log('项目信息:', projectInfo);
        console.log('项目统计:', projectStats);

        this.setData({
          projectInfo: projectInfo,
          projectStats: projectStats,
        });

        // 更新页面标题
        wx.setNavigationBarTitle({
          title: `${projectInfo.name} - 任务单列表`,
        });
      } else {
        wx.showToast({
          title: res.data.message || "加载工程信息失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载工程信息失败:", error);
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },

  // 加载任务单列表
  async loadTasks(projectId) {
    try {
      this.setData({ loading: true });
      const app = getApp();

      const res = await app.request({
        url: `/api/tasks/project/${projectId}`,
        method: "GET",
      });

      if (res.data.success) {
        const tasks = res.data.data.map((item) => ({
          ...item,
          scheduled_time_text: this.formatDateTime(item.scheduled_time),
          supply_status_text: this.getSupplyStatusText(item.supply_status),
          supply_status_class: this.getSupplyStatusClass(item.supply_status),
          feedback_count_text: `现场信息反馈记录: ${
            item.feedback_count || 0
          }`,
        }));

        this.setData({
          tasks,
          allTasks: tasks, // 保存所有任务单
          loading: false,
        });

        // 更新筛选选项的计数
        this.updateFilterCounts(tasks);

        console.log('任务列表加载完成，任务数量:', tasks.length);
      } else {
        wx.showToast({
          title: res.data.message || "加载失败",
          icon: "none",
        });
        this.setData({
          tasks: [],
          allTasks: [],
          loading: false,
        });
      }
    } catch (error) {
      console.error("加载任务单列表失败:", error);
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
      this.setData({ loading: false });
    }
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    const Formatter = require('../../../utils/formatter');
    return Formatter.formatDateTime(dateTimeStr, 'YYYY-MM-DD HH:mm');
  },

  // 点击任务单项
  onTaskTap(e) {
    const task = e.currentTarget.dataset.task;
    console.log('点击任务单，准备跳转到反馈单页面:', task);
    console.log('任务单ID:', task.id);
    const url = `/subpackages/feedback-management/feedback/feedback?taskId=${task.id}`;
    console.log('跳转URL:', url);
    wx.navigateTo({
      url: url,
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.projectId) {
      this.loadTasks(this.data.projectId);
    }
    wx.stopPullDownRefresh();
  },
  // 获取供应状态文本
  getSupplyStatusText(status) {
    const statusMap = {
      0: '待供',
      1: '正供',
      2: '供毕',
      3: '作废',
      // 兼容旧版本
      'pending': '待供',
      'supplying': '正供',
      'completed': '供毕',
      'cancelled': '作废'
    };
    return statusMap[status] || '未知';
  },

  // 获取供应状态样式类
  getSupplyStatusClass(status) {
    const classMap = {
      0: 'pending',
      1: 'supplying',
      2: 'completed',
      3: 'cancelled',
      // 兼容旧版本
      'pending': 'pending',
      'supplying': 'supplying',
      'completed': 'completed',
      'cancelled': 'cancelled'
    };
    return classMap[status] || 'pending';
  },

  // 更新筛选选项的计数
  updateFilterCounts(tasks) {
    const counts = {
      all: tasks.length,
      0: 0,
      1: 0,
      2: 0,
      3: 0
    };

    tasks.forEach(task => {
      const status = task.supply_status;
      if (counts[status] !== undefined) {
        counts[status]++;
      }
    });

    const filterOptions = this.data.filterOptions.map(option => ({
      ...option,
      count: counts[option.value] || 0
    }));

    this.setData({ filterOptions });
  },

  // 选择筛选条件
  onFilterSelect(e) {
    const filterValue = e.currentTarget.dataset.value;
    this.setData({
      currentFilter: filterValue
    });
    this.applyFilter(filterValue);
  },

  // 应用筛选
  applyFilter(filterValue) {
    let filteredTasks = this.data.allTasks;

    if (filterValue !== 'all') {
      const statusValue = parseInt(filterValue);
      filteredTasks = this.data.allTasks.filter(task =>
        task.supply_status === statusValue
      );
    }

    this.setData({
      tasks: filteredTasks
    });
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      currentFilter: 'all',
      tasks: this.data.allTasks
    });
  }






});
