[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:30:22] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 00:40:01] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2010,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 00:40:01] [INFO] 缓存失效 | {"pattern":"grouped_feedback:2015493","invalidatedCount":1}
[2025-07-28 01:07:56] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2011,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:07:56] [INFO] 缓存失效 | {"pattern":"grouped_feedback:2015493","invalidatedCount":1}
[2025-07-28 01:11:26] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2012,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:11:26] [INFO] 缓存失效 | {"pattern":"grouped_feedback:2015493","invalidatedCount":1}
[2025-07-28 01:20:32] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2013,"task_number":"B05-2200219","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:20:32] [INFO] 缓存失效 | {"pattern":"grouped_feedback:2015493","invalidatedCount":1}
[2025-07-28 01:23:50] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:23:50] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:42:32] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 01:44:13] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2014,"task_number":"B05-2200236","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 01:44:13] [INFO] 缓存失效 | {"pattern":"grouped_feedback:2015493","invalidatedCount":1}
[2025-07-28 03:17:15] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 03:17:15] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 03:17:15] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:39:17] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2015,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 05:43:06] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2016,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 05:44:39] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2017,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:59:52] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2018,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 08:37:39] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 08:37:39] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 08:42:01] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":3010,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 08:57:46] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":3011,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
