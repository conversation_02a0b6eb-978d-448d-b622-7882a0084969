const db = require("../config/database");
const logger = require('../utils/logger');

class Project {


  // 根据ID查找工程（优先使用原表，失败时使用备用表）
  static async findById(id) {
    try {
      logger.debug('根据ID查找工程', { id });

      // 首先尝试从原始表获取数据，使用JOIN查询获取施工单位名称
      try {
        const sql = `
          SELECT
            cp.ProjectId as id,
            cp.ProjectName as name,
            cp.ProjectId as code,
            cp.X_ConsUnitId as construction_unit_id,
            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,
            cp.X_OrgId as company_id,
            cp.X_ProImplement as implementation_status
          FROM dbo.comProject cp
          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
          WHERE cp.ProjectId = ?
        `;

        const [rows] = await db.execute(sql, [id]);

        if (rows.length > 0) {
          const project = rows[0];
          logger.debug('从原始表找到工程', { projectId: id, projectName: project.name, constructionUnit: project.construction_unit });
          return project;
        }

      } catch (originalError) {
        logger.warn('原始表查询失败，尝试使用备用表', { error: originalError.message });

        // 使用备用表
        const backupSql = `
          SELECT
            id,
            name,
            code,
            construction_unit_id,
            construction_unit,
            company_id,
            implementation_status
          FROM dbo.backup_projects
          WHERE id = ?
        `;

        const [backupRows] = await db.execute(backupSql, [id]);

        if (backupRows.length > 0) {
          const project = backupRows[0];
          logger.debug('从备用表找到工程', { projectId: id, projectName: project.name });
          return project;
        }
      }

      logger.debug('未找到工程', { id });
      return null;
    } catch (error) {
      logger.error('根据ID查找工程失败', error);
      throw error;
    }
  }

  // 获取公司的工程列表（优先使用原表，失败时使用备用表）
  static async getByCompanyId(companyId, filters = {}) {
    try {
      logger.debug("获取公司工程列表", { companyId, filters, companyIdType: typeof companyId });

      // 首先尝试从原始表获取数据，使用JOIN查询获取施工单位名称
      try {
        let sql = `
          SELECT
            cp.ProjectId as id,
            cp.ProjectName as name,
            cp.ProjectId as code,
            cp.X_ConsUnitId as construction_unit_id,
            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,
            cp.X_OrgId as company_id,
            cp.X_ProImplement as implementation_status
          FROM dbo.comProject cp
          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId
          WHERE cp.X_OrgId = ?
        `;

        logger.debug("尝试从原始表查询", { companyId });
        const [rows] = await db.execute(sql, [companyId]);

        // 为每个项目单独计算任务统计
        logger.debug("为项目计算任务统计", { projectCount: rows.length });

        const projectsWithStats = await Promise.all(
          rows.map(async (project) => {
            try {
              // 任务统计查询，包含供应状态统计
              const [taskRows] = await db.execute(
                `SELECT
                  COUNT(*) as task_count,
                  SUM(CASE WHEN ISNULL(X_SupplyState, 0) = 1 THEN 1 ELSE 0 END) as supplying_count,
                  SUM(CASE WHEN ISNULL(X_SupplyState, 0) = 2 THEN 1 ELSE 0 END) as completed_count
                 FROM dbo.X_ppProduceOrder WHERE ProjectId = ?`,
                [project.id]
              );

              // 查询反馈记录统计
              const [feedbackRows] = await db.execute(
                `
                SELECT COUNT(DISTINCT f.Id) as feedback_count
                FROM dbo.X_ppProduceOrder xpo
                INNER JOIN dbo.CU_feedbacks f ON xpo.BillNo = f.TaskNumber
                WHERE xpo.ProjectId = ? AND f.Status = 1
                `,
                [project.id]
              );

              const taskCount = taskRows[0]?.task_count || 0;
              const supplyingCount = taskRows[0]?.supplying_count || 0;
              const completedCount = taskRows[0]?.completed_count || 0;
              const feedbackCount = feedbackRows[0]?.feedback_count || 0;

              return {
                ...project,
                task_count: taskCount,
                supplying_count: supplyingCount,
                completed_count: completedCount,
                feedback_count: feedbackCount
                // construction_unit 已经通过 JOIN 查询获取，无需再次处理
              };
            } catch (statsError) {
              logger.warn("计算项目任务统计失败", { projectId: project.id, error: statsError.message });
              return {
                ...project,
                task_count: 0,
                supplying_count: 0,
                completed_count: 0,
                feedback_count: 0
                // construction_unit 已经通过 JOIN 查询获取，无需再次处理
              };
            }
          })
        );

        // 应用筛选条件
        let filteredRows = projectsWithStats;
        if (filters.keyword) {
          const keyword = filters.keyword.toLowerCase();
          filteredRows = filteredRows.filter(row =>
            row.name.toLowerCase().includes(keyword)
          );
        }

        logger.debug("项目列表查询成功", {
          totalProjects: projectsWithStats.length,
          filteredProjects: filteredRows.length,
          sampleProject: filteredRows[0] ? {
            id: filteredRows[0].id,
            name: filteredRows[0].name,
            task_count: filteredRows[0].task_count
          } : null
        });

        // 记录过滤结果
        logger.debug("工程过滤结果", {
          userCompanyId: companyId,
          totalProjects: rows.length,
          matchedProjects: filteredRows.length,
          availableCompanyIds: [...new Set(rows.map(r => r.company_id))]
        });

        // 应用其他筛选条件
        if (filters.keyword) {
          const keyword = filters.keyword.toLowerCase();
          filteredRows = filteredRows.filter(row =>
            row.name.toLowerCase().includes(keyword)
          );
        }

        if (filters.implementation_status) {
          filteredRows = filteredRows.filter(row =>
            row.implementation_status === parseInt(filters.implementation_status)
          );
        }

        // 按ProjectId降序排序
        filteredRows.sort((a, b) => b.id.localeCompare(a.id));

        logger.debug("原始表查询成功", {
          totalRows: rows.length,
          filteredRows: filteredRows.length,
          companyId
        });

        return filteredRows;

      } catch (originalError) {
        logger.warn("原始表查询失败，尝试使用备用表", { error: originalError.message });

        // 使用备用表
        const sql = `
          SELECT
            id,
            name,
            code,
            construction_unit_id,
            construction_unit,
            company_id,
            implementation_status,
            0 as task_count,
            0 as supplying_count,
            0 as completed_count,
            0 as feedback_count
          FROM dbo.backup_projects
          WHERE company_id = ?
        `;

        const [backupRows] = await db.execute(sql, [companyId]);

        // 应用筛选条件
        let filteredRows = backupRows;

        if (filters.keyword) {
          const keyword = filters.keyword.toLowerCase();
          filteredRows = filteredRows.filter(row =>
            row.name.toLowerCase().includes(keyword)
          );
        }

        if (filters.implementation_status) {
          filteredRows = filteredRows.filter(row =>
            row.implementation_status === parseInt(filters.implementation_status)
          );
        }

        logger.debug("备用表查询成功", {
          totalRows: backupRows.length,
          filteredRows: filteredRows.length,
          companyId
        });

        return filteredRows;
      }
    } catch (error) {
      logger.error('获取公司工程列表失败', error);
      throw error;
    }
  }

  // 获取公司的工程列表（分页版本）
  static async getByCompanyIdWithPagination(companyId, filters = {}, page = 1, pageSize = 10) {
    try {
      logger.debug("获取公司工程列表（分页）", { companyId, filters, page, pageSize });

      // 首先获取所有数据（复用现有逻辑）
      const allProjects = await this.getByCompanyId(companyId, filters);

      // 计算分页信息
      const total = allProjects.length;
      const totalPages = Math.ceil(total / pageSize);
      const offset = (page - 1) * pageSize;

      // 获取当前页数据
      const data = allProjects.slice(offset, offset + pageSize);

      logger.debug("分页查询结果", {
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages,
        dataCount: data.length
      });

      return {
        data,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages
      };
    } catch (error) {
      logger.error("获取公司工程列表（分页）失败", error);
      throw error;
    }
  }

  // 更新工程信息（注意：新数据库中不支持更新工程，只能从现有的comProject表中读取）
  static async update(id, projectData) {
    // 在新的业务系统中，工程数据来自comProject表，不支持更新操作
    throw new Error('新数据库中不支持更新工程，工程数据来自comProject表');
  }



  // 获取工程统计信息（从新数据库获取）
  static async getStats(projectId) {
    try {
      logger.debug('获取工程统计信息', { projectId });

      // 首先尝试原始查询，但添加日期有效性检查和格式转换
      try {
        const [rows] = await db.execute(
          `
          SELECT
            ISNULL(task_stats.task_count, 0) as total_tasks,
            ISNULL(task_stats.supplying_count, 0) as supplying_tasks,
            ISNULL(task_stats.completed_count, 0) as completed_tasks,
            ISNULL(feedback_stats.feedback_count, 0) as total_feedbacks
          FROM dbo.comProject cp
          LEFT JOIN (
            SELECT
              xpo.ProjectId,
              COUNT(*) as task_count,
              SUM(CASE WHEN ISNULL(xpo.X_SupplyState, 0) = 1 THEN 1 ELSE 0 END) as supplying_count,
              SUM(CASE WHEN ISNULL(xpo.X_SupplyState, 0) = 2 THEN 1 ELSE 0 END) as completed_count
            FROM dbo.X_ppProduceOrder xpo
            WHERE xpo.ProjectId = @param0
            GROUP BY xpo.ProjectId
          ) task_stats ON cp.ProjectId = task_stats.ProjectId
          LEFT JOIN (
            SELECT
              cp2.ProjectId,
              COUNT(DISTINCT f.Id) as feedback_count
            FROM dbo.comProject cp2
            INNER JOIN dbo.X_ppProduceOrder xpo2 ON cp2.ProjectId = xpo2.ProjectId
            INNER JOIN dbo.CU_feedbacks f ON xpo2.BillNo = f.TaskNumber
            WHERE cp2.ProjectId = @param2 AND f.Status = 1
            GROUP BY cp2.ProjectId
          ) feedback_stats ON cp.ProjectId = feedback_stats.ProjectId
          WHERE cp.ProjectId = @param3
        `,
          [projectId, projectId, projectId, projectId]
        );

        const result = rows[0] || {
          total_tasks: 0,
          supplying_tasks: 0,
          completed_tasks: 0,
          total_feedbacks: 0
        };

        logger.debug('工程统计信息查询成功', result);
        return result;
      } catch (originalError) {
        logger.warn('原始统计查询失败，使用简化查询', { error: originalError.message });

        // 备用方案：简化查询，不进行日期比较
        const [rows] = await db.execute(
          `
          SELECT
            ISNULL(task_stats.task_count, 0) as total_tasks,
            0 as supplying_tasks,
            0 as completed_tasks,
            ISNULL(feedback_stats.feedback_count, 0) as total_feedbacks
          FROM dbo.comProject cp
          LEFT JOIN (
            SELECT
              xpo.ProjectId,
              COUNT(*) as task_count
            FROM dbo.X_ppProduceOrder xpo
            WHERE xpo.ProjectId = @param0
            GROUP BY xpo.ProjectId
          ) task_stats ON cp.ProjectId = task_stats.ProjectId
          LEFT JOIN (
            SELECT
              cp2.ProjectId,
              COUNT(DISTINCT f.Id) as feedback_count
            FROM dbo.comProject cp2
            INNER JOIN dbo.X_ppProduceOrder xpo2 ON cp2.ProjectId = xpo2.ProjectId
            INNER JOIN dbo.CU_feedbacks f ON xpo2.BillNo = f.TaskNumber
            WHERE cp2.ProjectId = @param2 AND f.Status = 1
            GROUP BY cp2.ProjectId
          ) feedback_stats ON cp.ProjectId = feedback_stats.ProjectId
          WHERE cp.ProjectId = @param3
        `,
          [projectId, projectId, projectId, projectId]
        );

        const result = rows[0] || {
          total_tasks: 0,
          supplying_tasks: 0,
          completed_tasks: 0,
          total_feedbacks: 0
        };

        logger.debug('简化统计查询成功', result);
        return result;
      }
    } catch (error) {
      logger.error('获取工程统计信息失败', error);

      // 最后的备用方案：返回默认值
      logger.warn('使用默认统计值', { projectId });
      return {
        total_tasks: 0,
        supplying_tasks: 0,
        completed_tasks: 0,
        total_feedbacks: 0
      };
    }
  }
}

module.exports = Project;
