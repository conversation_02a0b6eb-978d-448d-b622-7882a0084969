[2025-07-28 05:34:45] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            co.OrgId as id,\n            co.OrgName as name,\n            co.OrgId as code,\n            1 as status,\n            NULL as created_by,\n            GETDATE() as created_at,\n            GETDATE() as updated_at,\n            GETDATE() as joined_at\n          FROM dbo.comPerson cp\n          INNER JOIN dbo.capOrganization co ON CAST(cp.BelongOrgId AS NVARCHAR(50)) = CAST(co.OrgId AS NVARCHAR(50))\n          WHERE CAST(cp.PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n            AND ISNUMERIC(cp.PersonId) = 1\n            AND ISNUMERIC(cp.BelongOrgId) = 1\n        ","params":["2015493"],"error":{"code":"ETIMEOUT","originalError":{"code":"ETIMEOUT"},"name":"RequestError","number":"ETIMEOUT","precedingErrors":[]}}
[2025-07-28 08:00:32] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:32] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-28 08:00:32] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count,\n               -- 计算最近操作时间（取UpdatedAt和CreatedAt的最大值）\n               CASE\n                 WHEN i.UpdatedAt > i.CreatedAt THEN i.UpdatedAt\n                 ELSE i.CreatedAt\n               END as last_operation_time\n        FROM CU_feedbacks i\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY last_operation_time DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000168000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":["2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:34] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-28 08:00:34] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:36] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-28 08:00:36] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
