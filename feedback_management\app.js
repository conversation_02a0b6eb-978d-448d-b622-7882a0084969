/**
 * 小程序主入口文件
 * 定义全局数据、生命周期函数和公共方法
 *
 * @fileoverview 现场信息反馈小程序主应用文件
 * <AUTHOR>
 * @version 1.0.0
 */

// 导入全局配置文件
const CONFIG = require("./config/index.js");
const errorManager = require("./utils/error-manager");

/**
 * 小程序应用实例
 * 包含全局数据、生命周期函数和公共方法
 */
App({
  /**
   * 全局数据
   * @type {Object}
   * @property {string} baseUrl - 服务器基础URL
   * @property {Object|null} userInfo - 用户信息
   * @property {string} version - 应用版本号
   * @property {boolean} isNetworkConnected - 网络连接状态
   */
  globalData: {
    baseUrl: CONFIG.SERVER_URL,
    userInfo: null,
    version: '1.0.0',
    isNetworkConnected: true
  },

  /**
   * 小程序启动时的生命周期函数
   * 在小程序初始化完成时触发，全局只触发一次
   *
   * @param {Object} options - 启动参数
   * @param {string} options.path - 启动页面路径
   * @param {Object} options.query - 启动页面参数
   * @param {string} options.scene - 启动场景值
   */
  onLaunch(options) {
    // 检查网络状态
    this.checkNetworkStatus();

    // 监听网络状态变化
    this.watchNetworkStatus();

    // 初始化全局配置
    this.initGlobalConfig();
  },

  /**
   * 小程序显示时的生命周期函数
   * 在小程序启动，或从后台进入前台显示时触发
   *
   * @param {Object} options - 显示参数
   */
  onShow(options) {
    // 检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 小程序隐藏时的生命周期函数
   * 在小程序从前台进入后台时触发
   */
  onHide() {
    // 小程序隐藏时的处理
  },

  /**
   * 小程序发生脚本错误或API调用报错时触发
   *
   * @param {string} error - 错误信息
   */
  onError(error) {
    console.error('小程序错误:', error);

    // 使用全局错误管理器处理错误
    errorManager.handleError(error, {
      source: 'app_global',
      timestamp: new Date().toISOString(),
      userInfo: this.globalData.userInfo
    }, {
      showToUser: true
    });
  },

  /**
   * 全局网络请求方法
   * 封装wx.request，提供统一的请求处理、认证和错误处理
   *
   * @param {Object} options - 请求配置
   * @param {string} options.url - 请求地址
   * @param {string} [options.method='GET'] - 请求方法
   * @param {Object} [options.data] - 请求数据
   * @param {Object} [options.header] - 请求头
   * @param {boolean} [options.needAuth=true] - 是否需要认证
   * @returns {Promise<Object>} 请求结果
   *
   * @example
   * // 基本用法
   * const app = getApp();
   * app.request({
   *   url: '/api/users',
   *   method: 'GET'
   * }).then(res => {
   *   console.log(res.data);
   * }).catch(err => {
   *   console.error(err);
   * });
   */
  request(options) {
    return new Promise((resolve, reject) => {
      // 检查网络状态
      if (!this.globalData.isNetworkConnected) {
        reject(new Error('网络连接不可用'));
        return;
      }

      const authToken = wx.getStorageSync("authToken");
      const currentCompany = wx.getStorageSync("currentCompany");

      // 默认请求配置
      const defaultOptions = {
        method: 'GET',
        timeout: CONFIG.API.TIMEOUT,
        header: {
          "Content-Type": "application/json",
        },
      };

      // 如果需要认证且有token，添加到header中
      if (options.needAuth !== false && authToken) {
        defaultOptions.header.Authorization = authToken;
      }

      // 如果有当前公司信息，添加到header中
      if (currentCompany) {
        try {
          // 使用encodeURIComponent对包含中文的JSON字符串进行编码
          const companyJson = JSON.stringify(currentCompany);
          defaultOptions.header["X-Current-Company"] = encodeURIComponent(companyJson);
        } catch (error) {
          console.warn('序列化公司信息失败:', error);
        }
      }

      // 合并默认配置和传入的配置
      const requestOptions = {
        ...defaultOptions,
        ...options,
        header: {
          ...defaultOptions.header,
          ...options.header,
        },
        // 处理URL：如果是完整URL则直接使用，否则拼接baseUrl
        url: options.url.startsWith("http")
          ? options.url
          : `${this.globalData.baseUrl}${options.url}`,
        success: (res) => {
          // 统一处理响应
          this.handleResponse(res, resolve, reject);
        },
        fail: (error) => {
          console.error('请求失败:', error);
          reject(error);
        },
      };

      wx.request(requestOptions);
    });
  },

  /**
   * 处理请求响应
   * 统一处理成功和错误响应
   *
   * @private
   * @param {Object} res - 响应对象
   * @param {Function} resolve - Promise resolve函数
   * @param {Function} reject - Promise reject函数
   */
  handleResponse(res, resolve, reject) {
    // 处理HTTP状态码
    if (res.statusCode === 401) {
      // 未授权，清除本地存储并跳转到登录页
      this.handleUnauthorized();
      reject(new Error('登录已过期'));
      return;
    }

    if (res.statusCode >= 400) {
      reject(new Error(`请求失败: ${res.statusCode}`));
      return;
    }

    resolve(res);
  },

  /**
   * 处理未授权响应
   * 清除本地存储并跳转到登录页
   *
   * @private
   */
  handleUnauthorized() {
    wx.removeStorageSync('authToken');
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('currentCompany');

    wx.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    });

    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }, 1500);
  },

  /**
   * 检查网络状态
   *
   * @private
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.globalData.isNetworkConnected = res.networkType !== 'none';
      }
    });
  },

  /**
   * 监听网络状态变化
   *
   * @private
   */
  watchNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      this.globalData.isNetworkConnected = res.isConnected;

      if (!res.isConnected) {
        wx.showToast({
          title: '网络连接已断开',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 检查登录状态
   *
   * @private
   */
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.globalData.userInfo = userInfo;
    }
  },

  /**
   * 初始化全局配置
   *
   * @private
   */
  initGlobalConfig() {
    // 设置全局配置
  },

  /**
   * 错误上报
   *
   * @private
   * @param {string} error - 错误信息
   */
  reportError(error) {
    // 这里可以实现错误上报逻辑
  }
});
