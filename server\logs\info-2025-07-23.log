[2025-07-23 03:10:15] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 03:10:15] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 03:10:16] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 03:10:16] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 03:11:14] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"4162ms","memoryDiff":{"rss":2416640,"heapUsed":14720984,"heapTotal":0,"external":-58690},"timestamp":"2025-07-23T03:11:14.814Z"}
[2025-07-23 03:11:19] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4697ms","memoryDiff":{"rss":2625536,"heapUsed":-12670256,"heapTotal":-262144,"external":-168482},"timestamp":"2025-07-23T03:11:19.523Z"}
[2025-07-23 03:11:27] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4888ms","memoryDiff":{"rss":421888,"heapUsed":14172592,"heapTotal":0,"external":39697},"timestamp":"2025-07-23T03:11:27.180Z"}
[2025-07-23 03:17:48] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2215ms","memoryDiff":{"rss":9940992,"heapUsed":5399416,"heapTotal":6086656,"external":-144230},"timestamp":"2025-07-23T03:17:48.710Z"}
[2025-07-23 03:17:50] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2475ms","memoryDiff":{"rss":3579904,"heapUsed":-831152,"heapTotal":2830336,"external":-196269},"timestamp":"2025-07-23T03:17:50.289Z"}
[2025-07-23 03:17:52] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2019ms","memoryDiff":{"rss":14606336,"heapUsed":24838160,"heapTotal":14155776,"external":397158},"timestamp":"2025-07-23T03:17:52.314Z"}
[2025-07-23 03:18:55] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2462ms","memoryDiff":{"rss":3567616,"heapUsed":-2556520,"heapTotal":-471040,"external":-200232},"timestamp":"2025-07-23T03:18:55.771Z"}
[2025-07-23 03:18:56] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2198ms","memoryDiff":{"rss":1359872,"heapUsed":13738792,"heapTotal":786432,"external":-97149},"timestamp":"2025-07-23T03:18:56.254Z"}
[2025-07-23 03:18:58] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2029ms","memoryDiff":{"rss":13520896,"heapUsed":13880280,"heapTotal":16515072,"external":-75170},"timestamp":"2025-07-23T03:18:58.331Z"}
[2025-07-23 03:19:00] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2147ms","memoryDiff":{"rss":9342976,"heapUsed":-17564376,"heapTotal":-6291456,"external":203809},"timestamp":"2025-07-23T03:19:00.482Z"}
[2025-07-23 06:08:44] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 06:08:44] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 06:37:21] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2162ms","memoryDiff":{"rss":12091392,"heapUsed":10882632,"heapTotal":5038080,"external":16825},"timestamp":"2025-07-23T06:37:21.816Z"}
[2025-07-23 06:37:23] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"2765ms","memoryDiff":{"rss":4849664,"heapUsed":15297440,"heapTotal":2568192,"external":403007},"timestamp":"2025-07-23T06:37:23.796Z"}
[2025-07-23 06:37:25] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2119ms","memoryDiff":{"rss":15208448,"heapUsed":11127344,"heapTotal":13893632,"external":-190668},"timestamp":"2025-07-23T06:37:25.921Z"}
[2025-07-23 06:37:30] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2048ms","memoryDiff":{"rss":16621568,"heapUsed":13842776,"heapTotal":16252928,"external":-82811},"timestamp":"2025-07-23T06:37:30.247Z"}
[2025-07-23 06:37:33] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3091ms","memoryDiff":{"rss":29110272,"heapUsed":-47165904,"heapTotal":-5562368,"external":-4766},"timestamp":"2025-07-23T06:37:33.637Z"}
[2025-07-23 07:10:49] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3091ms","memoryDiff":{"rss":14180352,"heapUsed":9815600,"heapTotal":9908224,"external":501350},"timestamp":"2025-07-23T07:10:49.398Z"}
[2025-07-23 07:10:49] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"3074ms","memoryDiff":{"rss":14114816,"heapUsed":8630120,"heapTotal":9908224,"external":479435},"timestamp":"2025-07-23T07:10:49.420Z"}
[2025-07-23 07:10:51] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2032ms","memoryDiff":{"rss":1953792,"heapUsed":11988184,"heapTotal":7864320,"external":-159705},"timestamp":"2025-07-23T07:10:51.436Z"}
[2025-07-23 07:11:05] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2780ms","memoryDiff":{"rss":499712,"heapUsed":29166960,"heapTotal":2359296,"external":107950},"timestamp":"2025-07-23T07:11:05.963Z"}
[2025-07-23 07:11:06] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3377ms","memoryDiff":{"rss":1355776,"heapUsed":26443048,"heapTotal":9175040,"external":-256353},"timestamp":"2025-07-23T07:11:06.598Z"}
[2025-07-23 07:11:06] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2917ms","memoryDiff":{"rss":958464,"heapUsed":21139408,"heapTotal":8802304,"external":-15180},"timestamp":"2025-07-23T07:11:06.625Z"}
[2025-07-23 07:13:14] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2071ms","memoryDiff":{"rss":8916992,"heapUsed":6831712,"heapTotal":18350080,"external":-39462},"timestamp":"2025-07-23T07:13:14.372Z"}
[2025-07-23 07:13:15] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3292ms","memoryDiff":{"rss":20324352,"heapUsed":25998728,"heapTotal":21123072,"external":150991},"timestamp":"2025-07-23T07:13:15.906Z"}
[2025-07-23 07:15:29] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3088ms","memoryDiff":{"rss":9957376,"heapUsed":-4806352,"heapTotal":4870144,"external":-82053},"timestamp":"2025-07-23T07:15:29.268Z"}
[2025-07-23 07:15:30] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=%E5%8E%82%E5%8C%BA","statusCode":200,"duration":"2392ms","memoryDiff":{"rss":13008896,"heapUsed":8474048,"heapTotal":13369344,"external":-53790},"timestamp":"2025-07-23T07:15:30.660Z"}
[2025-07-23 07:17:34] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2704ms","memoryDiff":{"rss":45678592,"heapUsed":17049088,"heapTotal":48443392,"external":238376},"timestamp":"2025-07-23T07:17:34.279Z"}
[2025-07-23 07:17:34] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2696ms","memoryDiff":{"rss":45436928,"heapUsed":17395960,"heapTotal":49491968,"external":245361},"timestamp":"2025-07-23T07:17:34.281Z"}
[2025-07-23 07:17:34] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"3227ms","memoryDiff":{"rss":45666304,"heapUsed":26152144,"heapTotal":50802688,"external":387685},"timestamp":"2025-07-23T07:17:34.816Z"}
[2025-07-23 07:17:37] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"2056ms","memoryDiff":{"rss":22818816,"heapUsed":22248632,"heapTotal":21966848,"external":133356},"timestamp":"2025-07-23T07:17:37.893Z"}
[2025-07-23 07:17:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=%E5%8E%82%E5%8C%BA","statusCode":200,"duration":"2325ms","memoryDiff":{"rss":26365952,"heapUsed":32102760,"heapTotal":24641536,"external":-5480},"timestamp":"2025-07-23T07:17:43.657Z"}
[2025-07-23 07:17:48] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"2285ms","memoryDiff":{"rss":16867328,"heapUsed":-74580272,"heapTotal":-58118144,"external":330535},"timestamp":"2025-07-23T07:17:48.357Z"}
[2025-07-23 07:17:57] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=%E5%8C%BB%E9%99%A2","statusCode":200,"duration":"2758ms","memoryDiff":{"rss":761856,"heapUsed":8054728,"heapTotal":6291456,"external":268617},"timestamp":"2025-07-23T07:17:57.417Z"}
[2025-07-23 07:18:05] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"2699ms","memoryDiff":{"rss":208896,"heapUsed":30509176,"heapTotal":26685440,"external":165581},"timestamp":"2025-07-23T07:18:05.077Z"}
[2025-07-23 07:22:37] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2037ms","memoryDiff":{"rss":36642816,"heapUsed":15891640,"heapTotal":41103360,"external":328564},"timestamp":"2025-07-23T07:22:37.074Z"}
[2025-07-23 07:22:39] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2361ms","memoryDiff":{"rss":7086080,"heapUsed":-4110288,"heapTotal":5242880,"external":-47661},"timestamp":"2025-07-23T07:22:39.439Z"}
[2025-07-23 07:22:40] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3389ms","memoryDiff":{"rss":12582912,"heapUsed":8742136,"heapTotal":8859648,"external":203632},"timestamp":"2025-07-23T07:22:40.841Z"}
[2025-07-23 07:23:05] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2279ms","memoryDiff":{"rss":36909056,"heapUsed":11111328,"heapTotal":40894464,"external":66279},"timestamp":"2025-07-23T07:23:05.079Z"}
[2025-07-23 07:23:05] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2303ms","memoryDiff":{"rss":37875712,"heapUsed":11637960,"heapTotal":41103360,"external":186835},"timestamp":"2025-07-23T07:23:05.090Z"}
[2025-07-23 07:23:07] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2223ms","memoryDiff":{"rss":7630848,"heapUsed":2476280,"heapTotal":3145728,"external":191267},"timestamp":"2025-07-23T07:23:07.317Z"}
[2025-07-23 07:23:09] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3980ms","memoryDiff":{"rss":13307904,"heapUsed":5340056,"heapTotal":9641984,"external":17013},"timestamp":"2025-07-23T07:23:09.404Z"}
[2025-07-23 07:23:11] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2443ms","memoryDiff":{"rss":16596992,"heapUsed":11992728,"heapTotal":16777216,"external":-159107},"timestamp":"2025-07-23T07:23:11.851Z"}
[2025-07-23 07:23:37] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2272ms","memoryDiff":{"rss":37306368,"heapUsed":17041504,"heapTotal":41365504,"external":382855},"timestamp":"2025-07-23T07:23:37.566Z"}
[2025-07-23 07:23:39] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2249ms","memoryDiff":{"rss":11014144,"heapUsed":-5348120,"heapTotal":3932160,"external":-79866},"timestamp":"2025-07-23T07:23:39.821Z"}
[2025-07-23 07:23:41] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3698ms","memoryDiff":{"rss":12267520,"heapUsed":8941736,"heapTotal":8597504,"external":230582},"timestamp":"2025-07-23T07:23:41.653Z"}
[2025-07-23 07:23:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2255ms","memoryDiff":{"rss":16695296,"heapUsed":13421936,"heapTotal":16515072,"external":-98504},"timestamp":"2025-07-23T07:23:43.912Z"}
[2025-07-23 07:24:06] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2098ms","memoryDiff":{"rss":37838848,"heapUsed":15976824,"heapTotal":40841216,"external":336155},"timestamp":"2025-07-23T07:24:06.716Z"}
[2025-07-23 07:24:08] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2157ms","memoryDiff":{"rss":7999488,"heapUsed":-3624208,"heapTotal":4718592,"external":-26227},"timestamp":"2025-07-23T07:24:08.876Z"}
[2025-07-23 07:24:10] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3326ms","memoryDiff":{"rss":5648384,"heapUsed":-4437608,"heapTotal":4141056,"external":101993},"timestamp":"2025-07-23T07:24:10.416Z"}
[2025-07-23 07:24:12] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2252ms","memoryDiff":{"rss":9740288,"heapUsed":24189928,"heapTotal":9175040,"external":373144},"timestamp":"2025-07-23T07:24:12.672Z"}
[2025-07-23 07:27:24] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2336ms","memoryDiff":{"rss":36777984,"heapUsed":16093064,"heapTotal":41103360,"external":355712},"timestamp":"2025-07-23T07:27:24.734Z"}
[2025-07-23 07:27:26] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2096ms","memoryDiff":{"rss":9654272,"heapUsed":-2640688,"heapTotal":5242880,"external":8457},"timestamp":"2025-07-23T07:27:26.834Z"}
[2025-07-23 07:27:28] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3313ms","memoryDiff":{"rss":11575296,"heapUsed":11077720,"heapTotal":9121792,"external":294973},"timestamp":"2025-07-23T07:27:28.380Z"}
[2025-07-23 07:29:40] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2191ms","memoryDiff":{"rss":35504128,"heapUsed":11872176,"heapTotal":40841216,"external":208396},"timestamp":"2025-07-23T07:29:40.075Z"}
[2025-07-23 07:29:40] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2208ms","memoryDiff":{"rss":37076992,"heapUsed":12129192,"heapTotal":40841216,"external":217795},"timestamp":"2025-07-23T07:29:40.077Z"}
[2025-07-23 07:29:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2264ms","memoryDiff":{"rss":7655424,"heapUsed":7156096,"heapTotal":5976064,"external":49516},"timestamp":"2025-07-23T07:29:43.417Z"}
[2025-07-23 07:29:53] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2026ms","memoryDiff":{"rss":12288000,"heapUsed":-35822040,"heapTotal":-21495808,"external":36381},"timestamp":"2025-07-23T07:29:53.105Z"}
[2025-07-23 07:29:54] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3154ms","memoryDiff":{"rss":9543680,"heapUsed":-20889968,"heapTotal":-22859776,"external":444239},"timestamp":"2025-07-23T07:29:54.533Z"}
[2025-07-23 07:30:02] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2083ms","memoryDiff":{"rss":20480,"heapUsed":13056808,"heapTotal":16777216,"external":-121228},"timestamp":"2025-07-23T07:30:02.060Z"}
[2025-07-23 07:30:04] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2184ms","memoryDiff":{"rss":16015360,"heapUsed":22836416,"heapTotal":17825792,"external":170737},"timestamp":"2025-07-23T07:30:04.248Z"}
[2025-07-23 07:30:05] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3501ms","memoryDiff":{"rss":30040064,"heapUsed":34770192,"heapTotal":30355456,"external":356282},"timestamp":"2025-07-23T07:30:05.955Z"}
[2025-07-23 07:30:08] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2605ms","memoryDiff":{"rss":1933312,"heapUsed":-70217776,"heapTotal":-63176704,"external":138735},"timestamp":"2025-07-23T07:30:08.564Z"}
[2025-07-23 07:31:15] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2020ms","memoryDiff":{"rss":37347328,"heapUsed":15832600,"heapTotal":41103360,"external":326665},"timestamp":"2025-07-23T07:31:15.370Z"}
[2025-07-23 07:31:17] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2039ms","memoryDiff":{"rss":8757248,"heapUsed":-3561600,"heapTotal":4456448,"external":-16084},"timestamp":"2025-07-23T07:31:17.414Z"}
[2025-07-23 07:31:18] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3209ms","memoryDiff":{"rss":11636736,"heapUsed":9713520,"heapTotal":8859648,"external":250600},"timestamp":"2025-07-23T07:31:18.890Z"}
[2025-07-23 07:31:38] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2168ms","memoryDiff":{"rss":35753984,"heapUsed":12379176,"heapTotal":40841216,"external":221244},"timestamp":"2025-07-23T07:31:38.112Z"}
[2025-07-23 07:31:41] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3041ms","memoryDiff":{"rss":14127104,"heapUsed":9866960,"heapTotal":11743232,"external":107382},"timestamp":"2025-07-23T07:31:41.487Z"}
[2025-07-23 07:31:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2070ms","memoryDiff":{"rss":15540224,"heapUsed":24194592,"heapTotal":14155776,"external":380798},"timestamp":"2025-07-23T07:31:43.562Z"}
[2025-07-23 08:06:18] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 08:06:18] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-23 08:18:19] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2059ms","memoryDiff":{"rss":40103936,"heapUsed":6141624,"heapTotal":42676224,"external":320445},"timestamp":"2025-07-23T08:18:19.833Z"}
[2025-07-23 08:19:31] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2337ms","memoryDiff":{"rss":40226816,"heapUsed":2336472,"heapTotal":44249088,"external":178848},"timestamp":"2025-07-23T08:19:31.706Z"}
[2025-07-23 08:19:33] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2259ms","memoryDiff":{"rss":5439488,"heapUsed":23830728,"heapTotal":3670016,"external":343488},"timestamp":"2025-07-23T08:19:33.970Z"}
[2025-07-23 08:19:35] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3358ms","memoryDiff":{"rss":27770880,"heapUsed":29400104,"heapTotal":26681344,"external":52069},"timestamp":"2025-07-23T08:19:35.444Z"}
[2025-07-23 08:20:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2021ms","memoryDiff":{"rss":42582016,"heapUsed":3207200,"heapTotal":43462656,"external":212967},"timestamp":"2025-07-23T08:20:25.009Z"}
[2025-07-23 08:20:27] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2194ms","memoryDiff":{"rss":5853184,"heapUsed":10228160,"heapTotal":2150400,"external":-168137},"timestamp":"2025-07-23T08:20:27.207Z"}
[2025-07-23 08:20:28] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3389ms","memoryDiff":{"rss":15400960,"heapUsed":12677656,"heapTotal":14622720,"external":-254518},"timestamp":"2025-07-23T08:20:28.721Z"}
[2025-07-23 08:38:44] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2029ms","memoryDiff":{"rss":41086976,"heapUsed":4690792,"heapTotal":43200512,"external":255794},"timestamp":"2025-07-23T08:38:44.300Z"}
[2025-07-23 08:38:47] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3107ms","memoryDiff":{"rss":15409152,"heapUsed":13051536,"heapTotal":14098432,"external":-245956},"timestamp":"2025-07-23T08:38:47.714Z"}
[2025-07-23 08:47:37] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2014ms","memoryDiff":{"rss":11526144,"heapUsed":27303784,"heapTotal":23330816,"external":276202},"timestamp":"2025-07-23T08:47:37.583Z"}
[2025-07-23 08:47:37] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2047ms","memoryDiff":{"rss":11526144,"heapUsed":27509312,"heapTotal":23330816,"external":285601},"timestamp":"2025-07-23T08:47:37.586Z"}
[2025-07-23 08:47:39] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2004ms","memoryDiff":{"rss":25587712,"heapUsed":31803896,"heapTotal":24838144,"external":279214},"timestamp":"2025-07-23T08:47:39.592Z"}
[2025-07-23 08:47:40] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2155ms","memoryDiff":{"rss":22708224,"heapUsed":-46267296,"heapTotal":-25604096,"external":-46601},"timestamp":"2025-07-23T08:47:40.986Z"}
[2025-07-23 08:47:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2114ms","memoryDiff":{"rss":1888256,"heapUsed":25799568,"heapTotal":0,"external":197356},"timestamp":"2025-07-23T08:47:43.104Z"}
[2025-07-23 08:48:33] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3737ms","memoryDiff":{"rss":2514944,"heapUsed":14554592,"heapTotal":2097152,"external":24362},"timestamp":"2025-07-23T08:48:33.931Z"}
