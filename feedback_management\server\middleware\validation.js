/**
 * 数据验证中间件
 * 提供统一的请求数据验证功能
 */

const ResponseUtil = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 验证规则类
 */
class ValidationRules {
  /**
   * 必填验证
   * @param {string} field - 字段名
   * @param {string} message - 错误消息
   */
  static required(field, message) {
    return {
      field,
      rule: 'required',
      message: message || `${field}不能为空`,
      validator: (value) => value !== undefined && value !== null && value !== ''
    };
  }

  /**
   * 字符串长度验证
   * @param {string} field - 字段名
   * @param {number} min - 最小长度
   * @param {number} max - 最大长度
   * @param {string} message - 错误消息
   */
  static length(field, min, max, message) {
    return {
      field,
      rule: 'length',
      message: message || `${field}长度必须在${min}-${max}之间`,
      validator: (value) => {
        if (typeof value !== 'string') return false;
        const length = value.trim().length;
        return length >= min && length <= max;
      }
    };
  }

  /**
   * 手机号验证
   * @param {string} field - 字段名
   * @param {string} message - 错误消息
   */
  static phone(field, message) {
    return {
      field,
      rule: 'phone',
      message: message || `${field}格式不正确`,
      validator: (value) => {
        if (typeof value !== 'string') return false;
        return /^1[3-9]\d{9}$/.test(value);
      }
    };
  }

  /**
   * 邮箱验证
   * @param {string} field - 字段名
   * @param {string} message - 错误消息
   */
  static email(field, message) {
    return {
      field,
      rule: 'email',
      message: message || `${field}格式不正确`,
      validator: (value) => {
        if (typeof value !== 'string') return false;
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      }
    };
  }

  /**
   * 数字验证
   * @param {string} field - 字段名
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @param {string} message - 错误消息
   */
  static number(field, min, max, message) {
    return {
      field,
      rule: 'number',
      message: message || `${field}必须是${min}-${max}之间的数字`,
      validator: (value) => {
        const num = Number(value);
        return !isNaN(num) && num >= min && num <= max;
      }
    };
  }

  /**
   * 枚举值验证
   * @param {string} field - 字段名
   * @param {Array} values - 允许的值
   * @param {string} message - 错误消息
   */
  static enum(field, values, message) {
    return {
      field,
      rule: 'enum',
      message: message || `${field}必须是以下值之一: ${values.join(', ')}`,
      validator: (value) => values.includes(value)
    };
  }

  /**
   * 数组验证
   * @param {string} field - 字段名
   * @param {number} minLength - 最小长度
   * @param {number} maxLength - 最大长度
   * @param {string} message - 错误消息
   */
  static array(field, minLength = 0, maxLength = Infinity, message) {
    return {
      field,
      rule: 'array',
      message: message || `${field}必须是数组且长度在${minLength}-${maxLength}之间`,
      validator: (value) => {
        if (!Array.isArray(value)) return false;
        return value.length >= minLength && value.length <= maxLength;
      }
    };
  }

  /**
   * 正则表达式验证
   * @param {string} field - 字段名
   * @param {RegExp} pattern - 正则表达式
   * @param {string} message - 错误消息
   */
  static pattern(field, pattern, message) {
    return {
      field,
      rule: 'pattern',
      message: message || `${field}格式不正确`,
      validator: (value) => {
        if (typeof value !== 'string') return false;
        return pattern.test(value);
      }
    };
  }

  /**
   * 自定义验证
   * @param {string} field - 字段名
   * @param {Function} validator - 验证函数
   * @param {string} message - 错误消息
   */
  static custom(field, validator, message) {
    return {
      field,
      rule: 'custom',
      message: message || `${field}验证失败`,
      validator
    };
  }
}

/**
 * 验证器类
 */
class Validator {
  /**
   * 验证数据
   * @param {Object} data - 要验证的数据
   * @param {Array} rules - 验证规则数组
   * @returns {Object} 验证结果 {valid: boolean, errors: Array}
   */
  static validate(data, rules) {
    const errors = [];

    for (const rule of rules) {
      const { field, validator, message } = rule;
      const value = data[field];

      if (!validator(value)) {
        errors.push({
          field,
          message,
          value
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 创建验证中间件
   * @param {Array} rules - 验证规则数组
   * @param {string} source - 数据源 'body' | 'query' | 'params'
   * @returns {Function} 中间件函数
   */
  static middleware(rules, source = 'body') {
    return (req, res, next) => {
      try {
        const data = req[source];
        const result = this.validate(data, rules);

        if (!result.valid) {
          logger.warn('数据验证失败', {
            url: req.url,
            method: req.method,
            errors: result.errors,
            data: source === 'body' ? req.body : (source === 'query' ? req.query : req.params)
          });

          return ResponseUtil.validationError(res, result.errors);
        }

        next();
      } catch (error) {
        logger.error('验证中间件错误', error);
        ResponseUtil.serverError(res, '验证过程中发生错误');
      }
    };
  }
}

/**
 * 常用验证规则预设
 */
const CommonValidations = {
  // 用户登录验证
  login: [
    ValidationRules.required('phone', '手机号不能为空'),
    ValidationRules.phone('phone'),
    ValidationRules.required('password', '密码不能为空'),
    ValidationRules.length('password', 6, 20, '密码长度必须在6-20位之间')
  ],



  // 分页参数验证
  pagination: [
    ValidationRules.number('page', 1, 1000, '页码必须是1-1000之间的数字'),
    ValidationRules.number('pageSize', 1, 100, '每页大小必须是1-100之间的数字')
  ],

  // ID参数验证
  id: [
    ValidationRules.required('id', 'ID不能为空'),
    ValidationRules.number('id', 1, Number.MAX_SAFE_INTEGER, 'ID必须是有效的数字')
  ],

  // 公司创建验证
  createCompany: [
    ValidationRules.required('name', '公司名称不能为空'),
    ValidationRules.length('name', 2, 100, '公司名称长度必须在2-100位之间')
  ],

  // 项目创建验证
  createProject: [
    ValidationRules.required('name', '项目名称不能为空'),
    ValidationRules.length('name', 2, 100, '项目名称长度必须在2-100位之间'),
    ValidationRules.required('construction_unit', '建设单位不能为空'),
    ValidationRules.length('construction_unit', 2, 100, '建设单位名称长度必须在2-100位之间')
  ],

  // 任务创建验证
  createTask: [
    ValidationRules.required('title', '任务标题不能为空'),
    ValidationRules.length('title', 2, 100, '任务标题长度必须在2-100位之间'),
    ValidationRules.required('project_id', '项目ID不能为空'),
    ValidationRules.number('project_id', 1, Number.MAX_SAFE_INTEGER, '项目ID必须是有效的数字')
  ]
};

module.exports = {
  ValidationRules,
  Validator,
  CommonValidations
};
