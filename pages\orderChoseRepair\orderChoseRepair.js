const requestModule = require('../../request/common');
const http = require('../../utils/http');
const utils = require('../../utils/util');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        CurrentBillNo:'',
        OrgId:'',
        IsApprove:1,
        FixState:0,
        FixPersonList:[],
        CurrentFixPerson:''
    },
    radioChange(e) {
       this.data.CurrentFixPerson=e.detail.value;
    },
    //实时获取弹窗的输入的值
    bindKeyInput(e) {
         console.log(e.detail.value)
    },
    openNews(){
      },
    // 取消
    hideCover() {
        wx.navigateBack();
    },

    // 同意
    showCover() {
      
    },
    // 点击确定后的事件处理 获取确认后的用户信息 并作出相应的处理
    formSubmit: function (e) {
        console.log(e);
         http(requestModule.UpdateFixPerson, {
                    data: {
                        ProgId: "X_CarServApply",
                        BillNo: this.data.CurrentBillNo,
                        FixPersonId:this.data.CurrentFixPerson
                    }
                }).then(res=>{
                    if(res.data.status=="suc")
                    {
                        wx.showModal({
                          title: '成功',  
                          content:'已成功指定维修人员',
                          showCancel:false,                     
                          complete: (res) => {
                            wx.switchTab({
                                url: '../../pages/order/order',
                            })
                          }
                        })
                    }
                }).catch(err=>{
                    console.log("失败信息",err);
                })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.data.CurrentBillNo = options.BillNo;
        this.data.OrgId = options.OrgId;
        http(requestModule.GetFixPerson, {
            data: {
                OrgId:options.OrgId
            }
        }).then(res=>{
            console.log('res',res);
            if(res.data.status=='suc')
            {
                this.setData({
                    FixPersonList:res.data.data
                })
            }
        }).catch(err=>{
            console.log('err',err);
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})
