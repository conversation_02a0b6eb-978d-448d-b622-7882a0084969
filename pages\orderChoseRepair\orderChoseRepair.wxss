/* pages/update_userinfo/update_userinfo.wxss */
.page{
    width: 100%;
    height: 100vh;
    background-color: rgb(146, 146, 146);
  }
  
  .bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    padding: 0 20rpx;
  }
  
  .btn-group {
    position: absolute;
    bottom: 0;
    width: 100%;
    /* margin-left:10%; */
    display: flex;
    justify-content: space-around;
    font-weight: bold;
    padding-bottom: 50rpx;
  }
  .weui-input {
    background-color: #f1f1f1;
    border-radius: 10rpx;
    padding: 16rpx 16rpx;
    margin-top: 50rpx;
    height: 60rpx;
    text-align: left;
    width: 420rpx;
  }
  
  .cover {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    background-color: gray;
  }
  
  .cover_child {
    width: 450rpx;
    height: 400rpx;
    background: rgb(18, 150, 219);
    border-radius: 20rpx;
    padding:50rpx;
    text-align: center;
    z-index: 5;
  }
  
  .child-title {
    white-space: nowrap;
    margin-top: 60rpx;
    height: 32rpx;
    font-size: larger;
    font-weight: bold;
    color: white;
    line-height: 36rpx;
    text-align: center;
    margin-bottom: 16rpx;
  }
  
  .cross {
    margin-bottom: 110rpx;
    bottom: 0rpx;
    position: fixed;
    width: 60rpx;
    height: 60rpx;
    z-index: 5;

  }
  .radio-group{
      margin-top: 50rpx;
      display: flex;
      justify-content: space-between;
      font-size: larger;
      font-weight: bolder;
      color: white;
      flex-wrap: wrap;
  }
  .radioinfo
{
    display: flex;
}
.radioinfo view
{
    color: white;
    font-size: large;
    font-weight: bolder;
    margin-top: 15rpx;
}
.fixName
{
    width: 120rpx;
    text-align: left;
}