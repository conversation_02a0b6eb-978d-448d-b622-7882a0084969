/* 任务单信息 */
.task-info {
  background-color: white;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.info-header {
  background: linear-gradient(135deg, #1296DB 0%, #42a5f5 100%);
  padding: 30rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.project-title {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.switch-task {
  padding: 8rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.switch-text {
  font-size: 24rpx;
  color: white;
}

.project-name {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}

.project-code {
  font-size: 26rpx;
  opacity: 0.9;
}

.construction-unit {
  font-size: 26rpx;
  opacity: 0.9;
}

.task-details {
  padding: 30rpx;
}

.task-details text {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333;
}

/* 现场信息反馈表单 */
.feedback-form {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.form-section, .media-section {
  padding: 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 28rpx;
  color: #333;
  display: block;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 反馈类别选择器 */
.category-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
  margin-top: 20rpx;
}

.category-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.category-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 反馈类别列表 */
.category-list {
  padding: 20rpx 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.category-item:last-child {
  border-bottom: none;
}

.category-item.selected {
  background-color: #f0f8ff;
}

.category-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.category-check {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-textarea {
  height: 120rpx;
  resize: none;
}

.time-display {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  text-align: center;
}

.feedback-user-display {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  text-align: center;
}

.location-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.location-display {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f9f9f9;
  text-align: center;
}

.location-display.authorized {
  color: #333;
  border-color: #1296DB;
  background-color: #ecf8ff;
}

.location-display.denied {
  border-color: #f44336;
  background-color: #ffebee;
}

.location-denied {
  color: #f44336;
}

.location-unavailable {
  color: #999;
}

/* 小地图容器 */
.mini-map-container {
  position: relative;
  margin: 20rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
}

.mini-map {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  min-height: 250rpx;
  max-height: 400rpx;
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background-color: #2196f3;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.refresh-button-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

.status-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx;
  background-color: #2196f3;
  border-radius: 6rpx;
  margin-top: 10rpx;
}

.status-button-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.test-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx;
  background-color: #ff9800;
  border-radius: 6rpx;
  margin-top: 10rpx;
}

.test-button-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.test-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx;
  background-color: #ff9800;
  border-radius: 6rpx;
  margin-top: 10rpx;
}

.test-button-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
}

.radio-item {
  display: inline-flex;
  align-items: center;
  margin-right: 40rpx;
  font-size: 28rpx;
}

.radio-item radio {
  margin-right: 10rpx;
}

/* 多媒体上传 */
.media-group {
  margin-bottom: 40rpx;
}

.media-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.media-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.duration-limit {
  font-size: 22rpx;
  color: #999;
  line-height: 22rpx;
}

.duration-limit.loading {
  color: #999;
  background-color: #f5f5f5;
  border-color: #e0e0e0;
}

.media-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 5rpx;
}

.media-actions .btnrecord-controls .btn{
  flex: 1;
  margin: 0;
}

/* 图片列表 */
.image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f8f8f8;
}

.image-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.image-item image.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



/* 图片占位符 */
.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.placeholder-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
  opacity: 0.6;
}

/* 图片加载中样式 */
.image-loading {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  color: #666;
}

.loading-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
  animation: spin 1.5s linear infinite;
}

.loading-text {
  font-size: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.placeholder-text {
  font-size: 24rpx;
  text-align: center;
  line-height: 1.4;
  padding: 0 10rpx;
}

/* 视频列表 */
.video-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.video-item {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.video-item video {
  width: 100%;
  height: 300rpx;
}

.uploaded-video {
  width: 100%;
  height: 400rpx; /* 增加高度以适应竖屏视频 */
  object-fit: contain; /* 保持视频原始比例，不裁剪 */
}

.preview-video {
  width: 100%;
  height: 300rpx;
  object-fit: contain;
}

.video-info {
  padding: 15rpx;
  background-color: #f5f5f5;
}

.video-duration {
  font-size: 24rpx;
  color: #666;
}



/* 录音控制 */
.record-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 5rpx;
}

.record-time {
  font-size: 28rpx;
  color: #f44336;
  font-weight: bold;
}

/* 音频列表 */
.audio-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.audio-item {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16rpx;
  border: 2rpx solid #e9ecef;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.audio-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.audio-item.playing {
  border-color: #1296DB;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  box-shadow: 0 6rpx 20rpx rgba(18, 150, 219, 0.15);
}

.audio-item.playing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1296DB, #42a5f5, #1296DB);
  background-size: 200% 100%;
  animation: audioProgress 2s linear infinite;
}

@keyframes audioProgress {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.audio-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #1296DB, #42a5f5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.audio-wave {
  font-size: 32rpx;
  color: white;
  transition: all 0.3s ease;
}

.audio-wave.active {
  animation: audioWave 1s ease-in-out infinite alternate;
}

@keyframes audioWave {
  0% { transform: scale(1); }
  100% { transform: scale(1.2); }
}

.audio-info {
  flex: 1;
}

.audio-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
  width: 100%;
}

.audio-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.audio-status {
  font-size: 22rpx;
  color: #1296DB;
  background-color: rgba(18, 150, 219, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(18, 150, 219, 0.2);
}

.audio-duration {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

.audio-controls {
  display: flex;
  gap: 12rpx;
  justify-content: center;
  width: 100%;
}

.audio-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-width: 160rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.audio-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.audio-btn:active::before {
  left: 100%;
}

.play-btn {
  background: linear-gradient(135deg, #1296DB, #42a5f5);
  color: white;
}

.play-btn.playing {
  background: linear-gradient(135deg, #f57c00, #ff9800);
}



.btn-icon {
  font-size: 28rpx;
  margin-bottom: 4rpx;
  line-height: 1;
}

.btn-text {
  font-size: 20rpx;
  line-height: 1;
  font-weight: 500;
}

/* 移动端优化 */
@media (max-width: 750rpx) {
  .audio-item {
    padding: 20rpx;
  }

  .audio-content {
    margin-bottom: 16rpx;
  }

  .audio-icon {
    width: 70rpx;
    height: 70rpx;
    margin-right: 16rpx;
  }

  .audio-wave {
    font-size: 28rpx;
  }

  .audio-name {
    font-size: 28rpx;
  }

  .audio-status {
    font-size: 20rpx;
    padding: 3rpx 10rpx;
  }

  .audio-duration {
    font-size: 22rpx;
  }

  .audio-btn {
    max-width: 140rpx;
    height: 70rpx;
  }

  .btn-icon {
    font-size: 24rpx;
  }

  .btn-text {
    font-size: 18rpx;
  }
}

/* 空状态优化 */
.audio-list:empty::after {
  content: '暂无录音文件';
  display: block;
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 28rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx dashed #dee2e6;
}

/* 录音按钮危险状态样式 */
.btn-danger {
  background: linear-gradient(135deg, #f44336, #e57373);
  color: white;
}

.btn-small.btn-danger {
  background-color: #f44336;
}

/* 提交区域 */
.submit-section {
  padding: 30rpx;
}

.submit-btn {
  width: 100%;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

/* 任务单选择弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-body {
  padding: 0 25rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  padding: 20rpx 0;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.task-item .task-info {
  flex: 1;
  background: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
}

.task-item .task-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.task-item .task-part {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.task-item .project-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  margin-bottom: 5rpx;
}

.task-item .task-project {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.task-item .task-construction {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.task-item .task-status {
  flex-shrink: 0;
  margin-left: 15rpx;
}

.status-tag {
  font-size: 20rpx;
  color: #666;
  padding: 4rpx 8rpx;
}

/* API选择器按钮 */
.api-selector-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.api-selector-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.2);
}

.api-selector-text {
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}

.api-current {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* API选择器弹窗 */
.api-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.api-selector-modal.show {
  opacity: 1;
  visibility: visible;
}

.api-selector-content {
  width: 90%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.api-selector-modal.show .api-selector-content {
  transform: scale(1);
}

.api-selector-header {
  padding: 40rpx 30rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.api-selector-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.api-selector-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

.api-options-list {
  padding: 20rpx 0;
}

.api-option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.api-option-item:last-child {
  border-bottom: none;
}

.api-option-item:active {
  background-color: #f8f9fa;
}

.api-option-item.selected {
  background-color: #e3f2fd;
}

.api-option-content {
  flex: 1;
}

.api-option-label {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.api-option-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.api-option-check {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #4caf50;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.api-selector-footer {
  display: flex;
  padding: 20rpx;
  gap: 20rpx;
  background: #f8f9fa;
}

.api-cancel-btn,
.api-test-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.api-cancel-btn {
  background: #e0e0e0;
  color: #666;
}

.api-cancel-btn:active {
  background: #d0d0d0;
}

.api-test-btn {
  background: #2196f3;
  color: white;
}

.api-test-btn:active {
  background: #1296DB;
}
