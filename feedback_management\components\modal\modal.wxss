/* 模态框组件样式 */

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease-out;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  animation: modalSlideIn 0.3s ease-out;
  max-width: 90%;
  margin: 40rpx;
}

.modal-header {
  position: relative;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: background-color 0.2s;
}

.modal-close:active {
  background-color: #e0e0e0;
}

.close-icon {
  font-size: 40rpx;
  color: #666666;
  line-height: 1;
}

.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-text {
  font-size: 32rpx;
  color: #666666;
  line-height: 1.6;
  word-break: break-all;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-button {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  transition: background-color 0.2s;
  cursor: pointer;
}

.modal-button:active {
  background-color: #f5f5f5;
}

.cancel-button {
  border-right: 1rpx solid #f0f0f0;
}

.confirm-button {
  font-weight: 600;
}

/* 动画效果 */
@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .modal-content {
    width: 90% !important;
    margin: 20rpx;
  }
  
  .modal-header {
    padding: 30rpx 30rpx 15rpx;
  }
  
  .modal-title {
    font-size: 32rpx;
  }
  
  .modal-body {
    padding: 30rpx;
  }
  
  .modal-text {
    font-size: 28rpx;
  }
  
  .modal-button {
    height: 88rpx;
    font-size: 28rpx;
  }
}

/* 无标题时的样式调整 */
.modal-content:not(:has(.modal-header)) .modal-body {
  padding-top: 40rpx;
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 6rpx;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3rpx;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3rpx;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
