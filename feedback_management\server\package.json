{"name": "server", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "db:init": "node database/setup-sqlserver.js", "db:test": "node database/setup-sqlserver.js --test"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.18.2", "express-session": "^1.18.1", "mime-types": "^2.1.35", "mssql": "^10.0.4", "multer": "^2.0.1", "nodemon": "^3.1.10"}}