/**
 * 位置权限管理工具类
 * 提供统一的位置权限检查、申请和获取位置信息的方法
 * 
 * @fileoverview 位置权限管理工具
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * 位置权限管理器
 */
class LocationPermissionManager {
  
  /**
   * 检查位置权限状态
   * @returns {Promise<Object>} 权限状态对象
   */
  static checkLocationPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          const locationAuth = res.authSetting['scope.userLocation'];
          resolve({
            hasPermission: locationAuth === true,
            isDenied: locationAuth === false,
            isUnknown: locationAuth === undefined
          });
        },
        fail: () => {
          resolve({
            hasPermission: false,
            isDenied: false,
            isUnknown: true
          });
        }
      });
    });
  }

  /**
   * 请求位置权限（简化版，不显示额外弹窗）
   * @param {Object} options - 配置选项
   * @returns {Promise<boolean>} 是否获得权限
   */
  static requestLocationPermission(options = {}) {
    return new Promise((resolve) => {
      // 直接尝试申请权限，不显示额外的确认弹窗
      wx.authorize({
        scope: 'scope.userLocation',
        success: () => {
          console.log('位置权限申请成功');
          resolve(true);
        },
        fail: (error) => {
          console.error('位置权限申请失败:', error);
          resolve(false);
        }
      });
    });
  }

  /**
   * 显示设置引导
   */
  static showSettingsGuide() {
    wx.showModal({
      title: '位置权限被拒绝',
      content: '请在设置中开启位置权限，以便记录反馈位置信息',
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting({
            success: (settingRes) => {
              if (settingRes.authSetting['scope.userLocation']) {
                wx.showToast({
                  title: '权限开启成功',
                  icon: 'success'
                });
              }
            }
          });
        }
      }
    });
  }

  /**
   * 获取当前位置信息
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} 位置信息
   */
  static getCurrentLocation(options = {}) {
    const defaultOptions = {
      type: 'gcj02',
      altitude: true,
      isHighAccuracy: true,
      highAccuracyExpireTime: 2000
    };
    
    const config = { ...defaultOptions, ...options };
    
    return new Promise((resolve, reject) => {
      wx.getLocation({
        ...config,
        success(res) {
          console.log('获取位置成功:', res);
          resolve({
            success: true,
            data: {
              longitude: res.longitude,
              latitude: res.latitude,
              accuracy: res.accuracy,
              altitude: res.altitude,
              verticalAccuracy: res.verticalAccuracy,
              horizontalAccuracy: res.horizontalAccuracy,
              speed: res.speed
            }
          });
        },
        fail(err) {
          console.error('获取位置失败:', err);
          
          let errorMsg = '获取位置失败';
          let errorCode = 'UNKNOWN_ERROR';
          
          if (err.errMsg && err.errMsg.includes('auth deny')) {
            errorMsg = '位置权限被拒绝';
            errorCode = 'PERMISSION_DENIED';
          } else if (err.errMsg && err.errMsg.includes('location fail')) {
            errorMsg = '定位服务不可用';
            errorCode = 'LOCATION_UNAVAILABLE';
          } else if (err.errMsg && err.errMsg.includes('requiredPrivateInfos')) {
            errorMsg = '位置API配置错误，请联系管理员';
            errorCode = 'API_CONFIG_ERROR';
          } else if (err.errMsg && err.errMsg.includes('permission denied')) {
            errorMsg = '位置权限被拒绝，请在设置中开启位置权限';
            errorCode = 'PERMISSION_DENIED';
          }
          
          reject({
            success: false,
            errorCode,
            errorMsg,
            originalError: err
          });
        }
      });
    });
  }

  /**
   * 检查并获取位置权限（简化流程，不显示额外弹窗）
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} 结果对象
   */
  static async checkAndRequestLocationPermission(options = {}) {
    try {
      // 1. 检查当前权限状态
      const permissionStatus = await LocationPermissionManager.checkLocationPermission();

      if (permissionStatus.hasPermission) {
        // 已有权限，直接返回成功
        return {
          success: true,
          hasPermission: true,
          message: '已有位置权限'
        };
      }

      if (permissionStatus.isDenied) {
        // 权限被拒绝，不显示额外弹窗，直接返回失败
        return {
          success: false,
          hasPermission: false,
          message: '位置权限被拒绝，请在设置中开启'
        };
      }

      // 2. 权限未知，请求权限
      const granted = await LocationPermissionManager.requestLocationPermission(options);

      return {
        success: granted,
        hasPermission: granted,
        message: granted ? '位置权限获取成功' : '位置权限被拒绝'
      };

    } catch (error) {
      console.error('位置权限检查失败:', error);
      return {
        success: false,
        hasPermission: false,
        message: '位置权限检查失败'
      };
    }
  }

  /**
   * 检查并获取位置信息（完整流程）
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} 位置信息结果
   */
  static async checkAndGetLocation(options = {}) {
    try {
      // 1. 检查并请求权限
      const permissionResult = await LocationPermissionManager.checkAndRequestLocationPermission(options);
      
      if (!permissionResult.success) {
        return {
          success: false,
          hasPermission: false,
          message: permissionResult.message
        };
      }
      
      // 2. 获取位置信息
      const locationResult = await LocationPermissionManager.getCurrentLocation(options);
      
      return {
        success: true,
        hasPermission: true,
        location: locationResult.data,
        message: '位置获取成功'
      };
      
    } catch (error) {
      console.error('获取位置信息失败:', error);
      
      // 如果是权限问题，引导用户设置
      if (error.errorCode === 'PERMISSION_DENIED') {
        LocationPermissionManager.showSettingsGuide();
      }
      
      return {
        success: false,
        hasPermission: error.errorCode !== 'PERMISSION_DENIED',
        message: error.errorMsg || '获取位置信息失败',
        errorCode: error.errorCode
      };
    }
  }
}

module.exports = LocationPermissionManager;
