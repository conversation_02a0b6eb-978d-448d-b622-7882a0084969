[2025-07-25 00:29:01] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2429ms","memoryDiff":{"rss":43626496,"heapUsed":10847192,"heapTotal":45035520,"external":473093},"timestamp":"2025-07-25T00:29:01.378Z"}
[2025-07-25 00:29:04] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3186ms","memoryDiff":{"rss":24457216,"heapUsed":24653576,"heapTotal":20709376,"external":-131954},"timestamp":"2025-07-25T00:29:04.570Z"}
[2025-07-25 00:29:05] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3954ms","memoryDiff":{"rss":32956416,"heapUsed":39222448,"heapTotal":29097984,"external":141887},"timestamp":"2025-07-25T00:29:05.360Z"}
[2025-07-25 00:29:07] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2266ms","memoryDiff":{"rss":25706496,"heapUsed":-50446112,"heapTotal":-10485760,"external":-497163},"timestamp":"2025-07-25T00:29:07.633Z"}
[2025-07-25 00:29:22] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2299ms","memoryDiff":{"rss":42586112,"heapUsed":4527824,"heapTotal":44249088,"external":252155},"timestamp":"2025-07-25T00:29:22.158Z"}
[2025-07-25 00:33:14] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2013ms","memoryDiff":{"rss":41275392,"heapUsed":6540488,"heapTotal":41889792,"external":322894},"timestamp":"2025-07-25T00:33:14.417Z"}
[2025-07-25 00:33:55] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":1014,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-25 00:33:55] [INFO] 缓存失效 | {"pattern":"grouped_feedback:2015493","invalidatedCount":1}
[2025-07-25 00:34:02] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2221ms","memoryDiff":{"rss":40853504,"heapUsed":7142200,"heapTotal":44511232,"external":338411},"timestamp":"2025-07-25T00:34:02.966Z"}
[2025-07-25 00:40:58] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2246ms","memoryDiff":{"rss":38674432,"heapUsed":17525192,"heapTotal":43986944,"external":322023},"timestamp":"2025-07-25T00:40:58.069Z"}
[2025-07-25 00:40:58] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2232ms","memoryDiff":{"rss":38674432,"heapUsed":17407896,"heapTotal":43986944,"external":321461},"timestamp":"2025-07-25T00:40:58.071Z"}
[2025-07-25 00:41:00] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2091ms","memoryDiff":{"rss":5865472,"heapUsed":-885248,"heapTotal":1572864,"external":17442},"timestamp":"2025-07-25T00:41:00.165Z"}
[2025-07-25 00:41:01] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3436ms","memoryDiff":{"rss":20951040,"heapUsed":8733048,"heapTotal":19079168,"external":-136331},"timestamp":"2025-07-25T00:41:01.772Z"}
[2025-07-25 00:41:04] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2374ms","memoryDiff":{"rss":17424384,"heapUsed":17231352,"heapTotal":16515072,"external":51843},"timestamp":"2025-07-25T00:41:04.153Z"}
[2025-07-25 00:43:21] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 00:43:21] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:01:07] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:01:07] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:01:07] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:01:07] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:01:07] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:01:09] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2352ms","memoryDiff":{"rss":33509376,"heapUsed":7910744,"heapTotal":34234368,"external":153536},"timestamp":"2025-07-25T01:01:09.795Z"}
[2025-07-25 01:01:09] [INFO] API响应较慢 | {"method":"GET","url":"/user/grouped","statusCode":304,"duration":"2097ms","memoryDiff":{"rss":29806592,"heapUsed":9857720,"heapTotal":31612928,"external":153246},"timestamp":"2025-07-25T01:01:09.799Z"}
[2025-07-25 01:01:10] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3079ms","memoryDiff":{"rss":35090432,"heapUsed":22344784,"heapTotal":34705408,"external":556718},"timestamp":"2025-07-25T01:01:10.750Z"}
[2025-07-25 01:01:16] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2982ms","memoryDiff":{"rss":11362304,"heapUsed":10233960,"heapTotal":9961472,"external":-53973},"timestamp":"2025-07-25T01:01:16.263Z"}
[2025-07-25 01:01:16] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3375ms","memoryDiff":{"rss":19869696,"heapUsed":19893608,"heapTotal":18821120,"external":317471},"timestamp":"2025-07-25T01:01:16.917Z"}
[2025-07-25 01:01:42] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2420ms","memoryDiff":{"rss":40493056,"heapUsed":395432,"heapTotal":42676224,"external":107323},"timestamp":"2025-07-25T01:01:42.558Z"}
[2025-07-25 01:02:57] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2221ms","memoryDiff":{"rss":42860544,"heapUsed":5450552,"heapTotal":43200512,"external":303954},"timestamp":"2025-07-25T01:02:57.679Z"}
[2025-07-25 01:03:00] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2825ms","memoryDiff":{"rss":18116608,"heapUsed":35700712,"heapTotal":18350080,"external":348551},"timestamp":"2025-07-25T01:03:00.508Z"}
[2025-07-25 01:03:01] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3344ms","memoryDiff":{"rss":29982720,"heapUsed":38883480,"heapTotal":29884416,"external":105725},"timestamp":"2025-07-25T01:03:01.049Z"}
[2025-07-25 01:09:19] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2137ms","memoryDiff":{"rss":40214528,"heapUsed":15261120,"heapTotal":43462656,"external":246831},"timestamp":"2025-07-25T01:09:19.744Z"}
[2025-07-25 01:09:22] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2867ms","memoryDiff":{"rss":18362368,"heapUsed":12083120,"heapTotal":14733312,"external":-147276},"timestamp":"2025-07-25T01:09:22.614Z"}
[2025-07-25 01:09:23] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3374ms","memoryDiff":{"rss":27791360,"heapUsed":25112144,"heapTotal":23592960,"external":166694},"timestamp":"2025-07-25T01:09:23.146Z"}
[2025-07-25 01:22:29] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2574ms","memoryDiff":{"rss":41603072,"heapUsed":974448,"heapTotal":42151936,"external":140460},"timestamp":"2025-07-25T01:22:29.530Z"}
[2025-07-25 01:23:54] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2052ms","memoryDiff":{"rss":44036096,"heapUsed":3310392,"heapTotal":43986944,"external":215435},"timestamp":"2025-07-25T01:23:54.791Z"}
[2025-07-25 01:24:22] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3617ms","memoryDiff":{"rss":43319296,"heapUsed":16385768,"heapTotal":46137344,"external":600005},"timestamp":"2025-07-25T01:24:22.706Z"}
[2025-07-25 01:24:23] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"4217ms","memoryDiff":{"rss":47050752,"heapUsed":17629360,"heapTotal":41533440,"external":280430},"timestamp":"2025-07-25T01:24:23.320Z"}
[2025-07-25 01:24:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2876ms","memoryDiff":{"rss":7057408,"heapUsed":6047048,"heapTotal":4665344,"external":-371213},"timestamp":"2025-07-25T01:24:25.585Z"}
[2025-07-25 01:24:26] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3477ms","memoryDiff":{"rss":15745024,"heapUsed":18904984,"heapTotal":13578240,"external":381235},"timestamp":"2025-07-25T01:24:26.207Z"}
[2025-07-25 01:25:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2200ms","memoryDiff":{"rss":42287104,"heapUsed":613560,"heapTotal":43724800,"external":119299},"timestamp":"2025-07-25T01:25:25.628Z"}
[2025-07-25 01:25:28] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2005ms","memoryDiff":{"rss":970752,"heapUsed":22884888,"heapTotal":0,"external":392251},"timestamp":"2025-07-25T01:25:28.844Z"}
[2025-07-25 01:25:32] [INFO] API响应较慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"2020ms","memoryDiff":{"rss":0,"heapUsed":487912,"heapTotal":0,"external":21079},"timestamp":"2025-07-25T01:25:32.196Z"}
[2025-07-25 01:32:43] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2264ms","memoryDiff":{"rss":42397696,"heapUsed":15588712,"heapTotal":43724800,"external":245013},"timestamp":"2025-07-25T01:32:43.199Z"}
[2025-07-25 01:33:58] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2121ms","memoryDiff":{"rss":2244608,"heapUsed":-2135568,"heapTotal":-786432,"external":176441},"timestamp":"2025-07-25T01:33:58.243Z"}
[2025-07-25 01:34:02] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2061ms","memoryDiff":{"rss":2826240,"heapUsed":-6077688,"heapTotal":2621440,"external":-236912},"timestamp":"2025-07-25T01:34:02.055Z"}
[2025-07-25 01:34:15] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2209ms","memoryDiff":{"rss":-688128,"heapUsed":-5167504,"heapTotal":6553600,"external":-58315},"timestamp":"2025-07-25T01:34:15.471Z"}
[2025-07-25 01:49:00] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:49:00] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:54:09] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:54:09] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:55:53] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:55:53] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:57:08] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:57:08] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:57:49] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:57:49] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:57:49] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:57:49] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 01:58:01] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4054ms","memoryDiff":{"rss":22097920,"heapUsed":18293248,"heapTotal":18665472,"external":15481},"timestamp":"2025-07-25T01:58:01.963Z"}
[2025-07-25 01:58:02] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"4715ms","memoryDiff":{"rss":31358976,"heapUsed":47888080,"heapTotal":30093312,"external":451219},"timestamp":"2025-07-25T01:58:02.654Z"}
[2025-07-25 01:58:05] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2597ms","memoryDiff":{"rss":8847360,"heapUsed":-19885976,"heapTotal":-14155776,"external":-100565},"timestamp":"2025-07-25T01:58:05.256Z"}
[2025-07-25 02:12:17] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2259ms","memoryDiff":{"rss":43945984,"heapUsed":5657536,"heapTotal":46084096,"external":293970},"timestamp":"2025-07-25T02:12:17.185Z"}
[2025-07-25 02:12:45] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2208ms","memoryDiff":{"rss":42582016,"heapUsed":3913424,"heapTotal":45297664,"external":237935},"timestamp":"2025-07-25T02:12:45.720Z"}
[2025-07-25 02:16:37] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2294ms","memoryDiff":{"rss":40079360,"heapUsed":17391288,"heapTotal":44511232,"external":308428},"timestamp":"2025-07-25T02:16:37.884Z"}
[2025-07-25 02:16:37] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2296ms","memoryDiff":{"rss":40075264,"heapUsed":17588712,"heapTotal":44511232,"external":327024},"timestamp":"2025-07-25T02:16:37.900Z"}
[2025-07-25 02:16:47] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2058ms","memoryDiff":{"rss":5500928,"heapUsed":-8993312,"heapTotal":2097152,"external":-445464},"timestamp":"2025-07-25T02:16:47.049Z"}
[2025-07-25 02:21:43] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2316ms","memoryDiff":{"rss":40611840,"heapUsed":18100832,"heapTotal":44511232,"external":332848},"timestamp":"2025-07-25T02:21:43.676Z"}
[2025-07-25 02:34:40] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2721ms","memoryDiff":{"rss":7897088,"heapUsed":-2277408,"heapTotal":5767168,"external":-243420},"timestamp":"2025-07-25T02:34:40.605Z"}
[2025-07-25 02:34:41] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2916ms","memoryDiff":{"rss":10592256,"heapUsed":26841408,"heapTotal":13893632,"external":-27662},"timestamp":"2025-07-25T02:34:41.340Z"}
[2025-07-25 02:34:43] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4016ms","memoryDiff":{"rss":31924224,"heapUsed":36903880,"heapTotal":36642816,"external":-25800},"timestamp":"2025-07-25T02:34:43.127Z"}
[2025-07-25 02:34:43] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2550ms","memoryDiff":{"rss":32563200,"heapUsed":32695600,"heapTotal":33239040,"external":326576},"timestamp":"2025-07-25T02:34:43.158Z"}
[2025-07-25 02:34:46] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3378ms","memoryDiff":{"rss":27176960,"heapUsed":28268088,"heapTotal":27140096,"external":236744},"timestamp":"2025-07-25T02:34:46.511Z"}
[2025-07-25 02:34:51] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3017ms","memoryDiff":{"rss":872448,"heapUsed":27780472,"heapTotal":13107200,"external":222268},"timestamp":"2025-07-25T02:34:51.989Z"}
[2025-07-25 02:34:53] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4300ms","memoryDiff":{"rss":225280,"heapUsed":44799952,"heapTotal":33234944,"external":391029},"timestamp":"2025-07-25T02:34:53.700Z"}
[2025-07-25 02:34:56] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2336ms","memoryDiff":{"rss":266240,"heapUsed":16490984,"heapTotal":27000832,"external":-197016},"timestamp":"2025-07-25T02:34:56.039Z"}
[2025-07-25 02:45:06] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 02:45:06] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 02:45:41] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2651ms","memoryDiff":{"rss":3035136,"heapUsed":19983872,"heapTotal":3878912,"external":320406},"timestamp":"2025-07-25T02:45:41.194Z"}
[2025-07-25 03:09:35] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:09:35] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:09:35] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:08] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:08] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:08] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:40] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:40] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:40] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:10:41] [INFO] 所有缓存已清空
[2025-07-25 03:10:41] [INFO] 缓存管理器已销毁
[2025-07-25 03:11:16] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:11:16] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:11:16] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:11:42] [INFO] 所有缓存已清空
[2025-07-25 03:11:42] [INFO] 缓存管理器已销毁
[2025-07-25 03:12:13] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:12:13] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:12:13] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:12:14] [INFO] 所有缓存已清空
[2025-07-25 03:12:14] [INFO] 缓存管理器已销毁
[2025-07-25 03:13:01] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:13:01] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:14:22] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2361ms","memoryDiff":{"rss":6909952,"heapUsed":7714584,"heapTotal":1835008,"external":369387},"timestamp":"2025-07-25T03:14:22.728Z"}
[2025-07-25 03:14:23] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2894ms","memoryDiff":{"rss":8019968,"heapUsed":5954536,"heapTotal":5238784,"external":103411},"timestamp":"2025-07-25T03:14:23.688Z"}
[2025-07-25 03:14:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2626ms","memoryDiff":{"rss":26447872,"heapUsed":20425904,"heapTotal":25690112,"external":-146004},"timestamp":"2025-07-25T03:14:25.358Z"}
[2025-07-25 03:14:27] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3590ms","memoryDiff":{"rss":44355584,"heapUsed":37646640,"heapTotal":42151936,"external":243338},"timestamp":"2025-07-25T03:14:27.282Z"}
[2025-07-25 03:14:29] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2560ms","memoryDiff":{"rss":24657920,"heapUsed":25987544,"heapTotal":23994368,"external":225950},"timestamp":"2025-07-25T03:14:29.846Z"}
[2025-07-25 03:14:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2497ms","memoryDiff":{"rss":8597504,"heapUsed":-66668192,"heapTotal":-53739520,"external":-466028},"timestamp":"2025-07-25T03:14:32.349Z"}
[2025-07-25 03:16:34] [INFO] API响应较慢 | {"method":"GET","url":"/project/B05AJA220002","statusCode":304,"duration":"4651ms","memoryDiff":{"rss":32059392,"heapUsed":18079720,"heapTotal":32976896,"external":398394},"timestamp":"2025-07-25T03:16:34.937Z"}
[2025-07-25 03:28:57] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:28:57] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 03:31:47] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2572ms","memoryDiff":{"rss":20832256,"heapUsed":25598000,"heapTotal":19398656,"external":286147},"timestamp":"2025-07-25T03:31:47.062Z"}
[2025-07-25 03:31:48] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4041ms","memoryDiff":{"rss":41361408,"heapUsed":-22541160,"heapTotal":14884864,"external":-200248},"timestamp":"2025-07-25T03:31:48.882Z"}
[2025-07-25 03:31:51] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2791ms","memoryDiff":{"rss":8192,"heapUsed":26144072,"heapTotal":0,"external":255799},"timestamp":"2025-07-25T03:31:51.678Z"}
[2025-07-25 05:33:14] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4333ms","memoryDiff":{"rss":37679104,"heapUsed":18832536,"heapTotal":43986944,"external":323994},"timestamp":"2025-07-25T05:33:14.698Z"}
[2025-07-25 05:33:17] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3032ms","memoryDiff":{"rss":7376896,"heapUsed":9081176,"heapTotal":5713920,"external":340420},"timestamp":"2025-07-25T05:33:17.982Z"}
[2025-07-25 05:33:38] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2320ms","memoryDiff":{"rss":6041600,"heapUsed":3661192,"heapTotal":1835008,"external":134540},"timestamp":"2025-07-25T05:33:38.241Z"}
[2025-07-25 05:33:39] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2684ms","memoryDiff":{"rss":11927552,"heapUsed":21105664,"heapTotal":12845056,"external":53512},"timestamp":"2025-07-25T05:33:39.231Z"}
[2025-07-25 05:33:39] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"3244ms","memoryDiff":{"rss":25350144,"heapUsed":23963480,"heapTotal":24588288,"external":-91024},"timestamp":"2025-07-25T05:33:39.812Z"}
[2025-07-25 05:36:10] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2251ms","memoryDiff":{"rss":39571456,"heapUsed":17061248,"heapTotal":43200512,"external":307700},"timestamp":"2025-07-25T05:36:10.417Z"}
[2025-07-25 05:36:12] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2075ms","memoryDiff":{"rss":6557696,"heapUsed":2055496,"heapTotal":3407872,"external":-16618},"timestamp":"2025-07-25T05:36:12.497Z"}
[2025-07-25 05:36:13] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3286ms","memoryDiff":{"rss":10711040,"heapUsed":-572696,"heapTotal":7544832,"external":-55156},"timestamp":"2025-07-25T05:36:13.985Z"}
[2025-07-25 05:36:16] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2024ms","memoryDiff":{"rss":24252416,"heapUsed":26747216,"heapTotal":23855104,"external":260140},"timestamp":"2025-07-25T05:36:16.012Z"}
[2025-07-25 05:38:58] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2032ms","memoryDiff":{"rss":40062976,"heapUsed":21069464,"heapTotal":43724800,"external":480553},"timestamp":"2025-07-25T05:38:58.500Z"}
[2025-07-25 05:39:25] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2072ms","memoryDiff":{"rss":40218624,"heapUsed":16507936,"heapTotal":43200512,"external":284173},"timestamp":"2025-07-25T05:39:25.468Z"}
[2025-07-25 05:39:27] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2470ms","memoryDiff":{"rss":16084992,"heapUsed":8200056,"heapTotal":6238208,"external":-22067},"timestamp":"2025-07-25T05:39:27.326Z"}
[2025-07-25 05:39:31] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"3192ms","memoryDiff":{"rss":41832448,"heapUsed":44530960,"heapTotal":41103360,"external":421274},"timestamp":"2025-07-25T05:39:31.457Z"}
[2025-07-25 05:39:34] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2787ms","memoryDiff":{"rss":32337920,"heapUsed":-51371344,"heapTotal":-26476544,"external":-287690},"timestamp":"2025-07-25T05:39:34.587Z"}
[2025-07-25 05:41:00] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4274ms","memoryDiff":{"rss":44355584,"heapUsed":5107232,"heapTotal":43200512,"external":259040},"timestamp":"2025-07-25T05:41:00.565Z"}
[2025-07-25 05:41:05] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4795ms","memoryDiff":{"rss":3424256,"heapUsed":21945008,"heapTotal":3670016,"external":301536},"timestamp":"2025-07-25T05:41:05.385Z"}
[2025-07-25 05:41:13] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4334ms","memoryDiff":{"rss":20992000,"heapUsed":-34565616,"heapTotal":-11534336,"external":-177591},"timestamp":"2025-07-25T05:41:13.063Z"}
[2025-07-25 05:43:42] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2426ms","memoryDiff":{"rss":41639936,"heapUsed":5806408,"heapTotal":45035520,"external":299470},"timestamp":"2025-07-25T05:43:42.518Z"}
[2025-07-25 05:47:10] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:47:10] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:48:28] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:48:28] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:50:35] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:50:35] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:52:31] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:52:31] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:53:04] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:53:04] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:54:45] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:54:45] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 05:59:07] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2260ms","memoryDiff":{"rss":32542720,"heapUsed":27003080,"heapTotal":27664384,"external":177076},"timestamp":"2025-07-25T05:59:07.038Z"}
[2025-07-25 05:59:08] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"3479ms","memoryDiff":{"rss":50413568,"heapUsed":47759512,"heapTotal":48443392,"external":576532},"timestamp":"2025-07-25T05:59:08.576Z"}
[2025-07-25 05:59:10] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2069ms","memoryDiff":{"rss":11767808,"heapUsed":-89328296,"heapTotal":-65798144,"external":-607188},"timestamp":"2025-07-25T05:59:10.649Z"}
[2025-07-25 06:13:18] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2916ms","memoryDiff":{"rss":18022400,"heapUsed":23980264,"heapTotal":19660800,"external":224381},"timestamp":"2025-07-25T06:13:18.864Z"}
[2025-07-25 06:13:21] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2739ms","memoryDiff":{"rss":25309184,"heapUsed":25679912,"heapTotal":24117248,"external":227398},"timestamp":"2025-07-25T06:13:21.607Z"}
[2025-07-25 06:13:27] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2954ms","memoryDiff":{"rss":25772032,"heapUsed":18503432,"heapTotal":26214400,"external":-117404},"timestamp":"2025-07-25T06:13:27.943Z"}
[2025-07-25 06:13:27] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2960ms","memoryDiff":{"rss":25772032,"heapUsed":18458520,"heapTotal":26214400,"external":-118049},"timestamp":"2025-07-25T06:13:27.965Z"}
[2025-07-25 06:13:32] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"4082ms","memoryDiff":{"rss":9158656,"heapUsed":-46493240,"heapTotal":-48496640,"external":-257145},"timestamp":"2025-07-25T06:13:32.029Z"}
[2025-07-25 06:13:32] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"4770ms","memoryDiff":{"rss":9170944,"heapUsed":-33588832,"heapTotal":-40108032,"external":-44461},"timestamp":"2025-07-25T06:13:32.753Z"}
[2025-07-25 06:13:35] [INFO] API响应较慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2641ms","memoryDiff":{"rss":598016,"heapUsed":26374520,"heapTotal":24379392,"external":270169},"timestamp":"2025-07-25T06:13:35.398Z"}
[2025-07-25 06:14:46] [INFO] API响应较慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"2650ms","memoryDiff":{"rss":21569536,"heapUsed":19793032,"heapTotal":19922944,"external":31134},"timestamp":"2025-07-25T06:14:46.708Z"}
[2025-07-25 06:14:47] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":200,"duration":"2399ms","memoryDiff":{"rss":16859136,"heapUsed":-23235976,"heapTotal":3670016,"external":-218253},"timestamp":"2025-07-25T06:14:47.006Z"}
[2025-07-25 06:14:49] [INFO] API响应较慢 | {"method":"GET","url":"/","statusCode":304,"duration":"2772ms","memoryDiff":{"rss":-1339392,"heapUsed":29300016,"heapTotal":3407872,"external":303904},"timestamp":"2025-07-25T06:14:49.787Z"}
[2025-07-25 06:20:29] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-25 06:20:29] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
