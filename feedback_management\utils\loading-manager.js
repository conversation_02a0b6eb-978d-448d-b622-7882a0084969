/**
 * 全局Loading管理器
 * 统一管理loading状态，避免多个loading同时显示的问题
 * 支持loading队列、延迟显示、自动隐藏等功能
 */

class LoadingManager {
  constructor() {
    // loading计数器
    this.loadingCount = 0;
    // 当前loading状态
    this.isShowing = false;
    // loading队列
    this.loadingQueue = [];
    // 延迟显示定时器
    this.delayTimer = null;
    // 自动隐藏定时器
    this.autoHideTimer = null;
    // 配置
    this.config = {
      // 延迟显示时间（毫秒）- 避免闪烁
      delayTime: 300,
      // 最大显示时间（毫秒）- 防止loading卡住
      maxShowTime: 30000,
      // 默认loading文本
      defaultTitle: '加载中...',
      // 是否显示遮罩
      mask: true
    };
  }

  /**
   * 显示loading
   * @param {string} title - loading文本
   * @param {Object} options - 配置选项
   * @param {boolean} options.mask - 是否显示遮罩
   * @param {number} options.delay - 延迟显示时间
   * @param {number} options.maxTime - 最大显示时间
   * @returns {string} loading ID，用于后续隐藏
   */
  show(title = this.config.defaultTitle, options = {}) {
    const loadingId = this.generateId();
    const loadingItem = {
      id: loadingId,
      title,
      mask: options.mask !== undefined ? options.mask : this.config.mask,
      delay: options.delay !== undefined ? options.delay : this.config.delayTime,
      maxTime: options.maxTime !== undefined ? options.maxTime : this.config.maxShowTime,
      startTime: Date.now()
    };

    // 添加到队列
    this.loadingQueue.push(loadingItem);
    this.loadingCount++;

    // 如果当前没有显示loading，开始显示
    if (!this.isShowing) {
      this.startLoading(loadingItem);
    }


    return loadingId;
  }

  /**
   * 隐藏loading
   * @param {string} loadingId - loading ID，如果不提供则隐藏最新的loading
   */
  hide(loadingId = null) {
    if (this.loadingCount <= 0) {
      return;
    }

    // 如果提供了ID，从队列中移除指定项
    if (loadingId) {
      const index = this.loadingQueue.findIndex(item => item.id === loadingId);
      if (index !== -1) {
        this.loadingQueue.splice(index, 1);
        this.loadingCount--;
        console.log(`Loading隐藏: ID ${loadingId}, 当前计数: ${this.loadingCount}`);
      }
    } else {
      // 否则移除最后一个
      if (this.loadingQueue.length > 0) {
        this.loadingQueue.pop();
        this.loadingCount--;
      }
    }

    // 如果没有更多loading，隐藏显示
    if (this.loadingCount <= 0) {
      this.hideLoading();
    }
  }

  /**
   * 强制隐藏所有loading
   */
  hideAll() {
    this.loadingCount = 0;
    this.loadingQueue = [];
    this.hideLoading();
  }

  /**
   * 开始显示loading
   * @private
   * @param {Object} loadingItem - loading项
   */
  startLoading(loadingItem) {
    // 清除之前的延迟定时器
    if (this.delayTimer) {
      clearTimeout(this.delayTimer);
      this.delayTimer = null;
    }

    // 延迟显示，避免闪烁
    this.delayTimer = setTimeout(() => {
      if (this.loadingCount > 0 && !this.isShowing) {
        this.showLoading(loadingItem);
      }
    }, loadingItem.delay);
  }

  /**
   * 实际显示loading
   * @private
   * @param {Object} loadingItem - loading项
   */
  showLoading(loadingItem) {
    try {
      wx.showLoading({
        title: loadingItem.title,
        mask: loadingItem.mask
      });
      
      this.isShowing = true;
      
      // 设置自动隐藏定时器，防止loading卡住
      if (this.autoHideTimer) {
        clearTimeout(this.autoHideTimer);
      }
      
      this.autoHideTimer = setTimeout(() => {
        console.warn('Loading显示时间过长，自动隐藏');
        this.hideAll();
      }, loadingItem.maxTime);
      
    } catch (error) {
      console.error('显示loading失败:', error);
      this.isShowing = false;
    }
  }

  /**
   * 隐藏loading显示
   * @private
   */
  hideLoading() {
    // 清除定时器
    if (this.delayTimer) {
      clearTimeout(this.delayTimer);
      this.delayTimer = null;
    }
    
    if (this.autoHideTimer) {
      clearTimeout(this.autoHideTimer);
      this.autoHideTimer = null;
    }

    // 隐藏loading
    if (this.isShowing) {
      try {
        wx.hideLoading();
      } catch (error) {
        console.error('隐藏loading失败:', error);
      }
      this.isShowing = false;
    }
  }

  /**
   * 生成唯一ID
   * @private
   * @returns {string} 唯一ID
   */
  generateId() {
    return 'loading_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取当前loading状态
   * @returns {Object} loading状态信息
   */
  getStatus() {
    return {
      isShowing: this.isShowing,
      loadingCount: this.loadingCount,
      queueLength: this.loadingQueue.length,
      currentTitle: this.loadingQueue.length > 0 ? this.loadingQueue[0].title : null
    };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 检查是否有超时的loading
   * @private
   */
  checkTimeout() {
    const now = Date.now();
    const timeoutItems = this.loadingQueue.filter(item => 
      now - item.startTime > item.maxTime
    );

    if (timeoutItems.length > 0) {
      console.warn(`发现${timeoutItems.length}个超时的loading项`);
      timeoutItems.forEach(item => this.hide(item.id));
    }
  }

  /**
   * 显示带进度的loading
   * @param {string} title - loading文本
   * @param {number} progress - 进度（0-100）
   * @param {Object} options - 配置选项
   * @returns {string} loading ID
   */
  showProgress(title, progress = 0, options = {}) {
    const progressTitle = `${title} ${Math.round(progress)}%`;
    return this.show(progressTitle, options);
  }

  /**
   * 更新进度
   * @param {string} loadingId - loading ID
   * @param {number} progress - 进度（0-100）
   * @param {string} title - 可选的新标题
   */
  updateProgress(loadingId, progress, title = null) {
    const loadingItem = this.loadingQueue.find(item => item.id === loadingId);
    if (loadingItem) {
      const baseTitle = title || loadingItem.title.replace(/ \d+%$/, '');
      const progressTitle = `${baseTitle} ${Math.round(progress)}%`;
      
      // 如果当前正在显示这个loading，更新显示
      if (this.isShowing && this.loadingQueue[0].id === loadingId) {
        try {
          wx.hideLoading();
          wx.showLoading({
            title: progressTitle,
            mask: loadingItem.mask
          });
        } catch (error) {
          console.error('更新进度失败:', error);
        }
      }
      
      loadingItem.title = progressTitle;
    }
  }
}

// 创建全局实例
const loadingManager = new LoadingManager();

// 定期检查超时的loading（每10秒）
setInterval(() => {
  loadingManager.checkTimeout();
}, 10000);

module.exports = loadingManager;
