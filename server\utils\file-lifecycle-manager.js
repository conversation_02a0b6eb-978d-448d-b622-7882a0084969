/**
 * 文件生命周期管理器
 * 管理文件从创建到删除的完整生命周期
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const db = require('../config/database');
const UploadManager = require('./upload-manager');

class FileLifecycleManager {
  constructor() {
    this.uploadDir = path.join(__dirname, '..', 'upload');
    this.tempDir = path.join(this.uploadDir, 'temp');
    this.backupDir = path.join(this.uploadDir, 'backup');
    
    // 确保备份目录存在
    this.ensureBackupDirectory();
  }

  /**
   * 确保备份目录存在
   */
  ensureBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 处理文件上传后的生命周期
   * @param {Object} fileInfo - 文件信息
   * @param {string} feedbackId - 反馈ID
   * @returns {Promise<Object>} 处理结果
   */
  async processUploadedFile(fileInfo, feedbackId) {
    try {
      const filePath = fileInfo.path;
      const originalName = fileInfo.originalname;
      const mimeType = fileInfo.mimetype;
      
      // 1. 生成文件校验和
      const checksum = await UploadManager.generateChecksum(filePath);
      
      // 2. 检查是否需要压缩
      let compressedPath = null;
      let compressionInfo = null;
      
      if (this.shouldCompress(fileInfo)) {
        const compressResult = await this.compressFile(fileInfo);
        if (compressResult.compressed) {
          compressedPath = compressResult.outputPath;
          compressionInfo = compressResult;
        }
      }
      
      // 3. 创建缩略图（如果是图片）
      let thumbnailPath = null;
      if (mimeType.startsWith('image/')) {
        thumbnailPath = await this.createThumbnail(filePath);
      }
      
      // 4. 记录到数据库
      const mediaRecord = {
        FeedbackId: feedbackId,
        MediaType: this.getMediaType(mimeType),
        FileName: fileInfo.filename,
        OriginalFileName: originalName,
        FilePath: this.getRelativePath(filePath),
        CompressedPath: compressedPath ? this.getRelativePath(compressedPath) : null,
        ThumbnailPath: thumbnailPath ? this.getRelativePath(thumbnailPath) : null,
        FileSize: fileInfo.size,
        Checksum: checksum,
        CompressionRatio: compressionInfo ? compressionInfo.compressionRatio : null,
        LastAccessTime: new Date()
      };
      
      const mediaId = await this.saveMediaRecord(mediaRecord);
      
      // 5. 创建备份（可选）
      if (this.shouldBackup(fileInfo)) {
        await this.createBackup(filePath, mediaId);
      }
      
      return {
        success: true,
        mediaId: mediaId,
        checksum: checksum,
        compressed: !!compressedPath,
        thumbnailCreated: !!thumbnailPath,
        backed_up: this.shouldBackup(fileInfo)
      };
      
    } catch (error) {
      console.error('处理上传文件失败:', error);
      throw error;
    }
  }

  /**
   * 判断是否需要压缩
   */
  shouldCompress(fileInfo) {
    const mimeType = fileInfo.mimetype;
    const fileSize = fileInfo.size;
    
    // 图片大于2MB压缩
    if (mimeType.startsWith('image/') && fileSize > 2 * 1024 * 1024) {
      return true;
    }
    
    // 视频大于50MB压缩
    if (mimeType.startsWith('video/') && fileSize > 50 * 1024 * 1024) {
      return true;
    }
    
    return false;
  }

  /**
   * 压缩文件
   */
  async compressFile(fileInfo) {
    try {
      const inputPath = fileInfo.path;
      const outputPath = inputPath.replace(/(\.[^.]+)$/, '_compressed$1');
      
      // 调用压缩功能
      const result = await UploadManager.compressImage(inputPath, outputPath);
      
      return {
        compressed: result.compressed,
        outputPath: result.compressed ? outputPath : null,
        originalSize: result.originalSize,
        compressedSize: result.compressedSize,
        compressionRatio: result.compressionRatio
      };
    } catch (error) {
      console.error('文件压缩失败:', error);
      return { compressed: false };
    }
  }

  /**
   * 创建缩略图
   */
  async createThumbnail(imagePath) {
    try {
      const thumbnailPath = imagePath.replace(/(\.[^.]+)$/, '_thumb$1');
      
      // TODO: 实现缩略图生成逻辑
      // 目前只是复制文件作为占位符
      fs.copyFileSync(imagePath, thumbnailPath);
      
      return thumbnailPath;
    } catch (error) {
      console.error('创建缩略图失败:', error);
      return null;
    }
  }

  /**
   * 判断是否需要备份
   */
  shouldBackup(fileInfo) {
    // 大于10MB的文件创建备份
    return fileInfo.size > 10 * 1024 * 1024;
  }

  /**
   * 创建文件备份
   */
  async createBackup(filePath, mediaId) {
    try {
      const fileName = path.basename(filePath);
      const backupFileName = `${mediaId}_${fileName}`;
      const backupPath = path.join(this.backupDir, backupFileName);
      
      fs.copyFileSync(filePath, backupPath);
      
      console.log(`创建备份: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.error('创建备份失败:', error);
      return null;
    }
  }

  /**
   * 获取媒体类型
   */
  getMediaType(mimeType) {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return 'other';
  }

  /**
   * 获取相对路径
   */
  getRelativePath(fullPath) {
    return path.relative(this.uploadDir, fullPath).replace(/\\/g, '/');
  }

  /**
   * 保存媒体记录到数据库
   */
  async saveMediaRecord(mediaRecord) {
    try {
      const result = await db.execute(
        `INSERT INTO CU_feedback_media 
         (FeedbackId, MediaType, FileName, OriginalFileName, FilePath, CompressedPath, 
          ThumbnailPath, FileSize, Checksum, CompressionRatio, LastAccessTime)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          mediaRecord.FeedbackId,
          mediaRecord.MediaType,
          mediaRecord.FileName,
          mediaRecord.OriginalFileName,
          mediaRecord.FilePath,
          mediaRecord.CompressedPath,
          mediaRecord.ThumbnailPath,
          mediaRecord.FileSize,
          mediaRecord.Checksum,
          mediaRecord.CompressionRatio,
          mediaRecord.LastAccessTime
        ]
      );
      
      return result[1].insertId;
    } catch (error) {
      console.error('保存媒体记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新文件访问时间
   */
  async updateAccessTime(mediaId) {
    try {
      await db.execute(
        'UPDATE CU_feedback_media SET LastAccessTime = GETDATE() WHERE Id = ?',
        [mediaId]
      );
    } catch (error) {
      console.error('更新访问时间失败:', error);
    }
  }

  /**
   * 删除文件及其相关资源
   */
  async deleteFile(mediaId) {
    try {
      // 获取文件信息
      const result = await db.execute(
        'SELECT * FROM CU_feedback_media WHERE Id = ?',
        [mediaId]
      );
      
      if (result[0].length === 0) {
        throw new Error('文件记录不存在');
      }
      
      const mediaRecord = result[0][0];
      
      // 删除主文件
      const mainFilePath = path.join(this.uploadDir, mediaRecord.FilePath);
      if (fs.existsSync(mainFilePath)) {
        fs.unlinkSync(mainFilePath);
      }
      
      // 删除压缩文件
      if (mediaRecord.CompressedPath) {
        const compressedFilePath = path.join(this.uploadDir, mediaRecord.CompressedPath);
        if (fs.existsSync(compressedFilePath)) {
          fs.unlinkSync(compressedFilePath);
        }
      }
      
      // 删除缩略图
      if (mediaRecord.ThumbnailPath) {
        const thumbnailFilePath = path.join(this.uploadDir, mediaRecord.ThumbnailPath);
        if (fs.existsSync(thumbnailFilePath)) {
          fs.unlinkSync(thumbnailFilePath);
        }
      }
      
      // 删除备份文件
      const backupFilePath = path.join(this.backupDir, `${mediaId}_${mediaRecord.FileName}`);
      if (fs.existsSync(backupFilePath)) {
        fs.unlinkSync(backupFilePath);
      }
      
      // 删除数据库记录
      await db.execute(
        'UPDATE CU_feedback_media SET Status = 0 WHERE Id = ?',
        [mediaId]
      );
      
      console.log(`删除文件完成: ${mediaId}`);
      return true;
    } catch (error) {
      console.error('删除文件失败:', error);
      throw error;
    }
  }

  /**
   * 验证文件完整性
   */
  async verifyFileIntegrity(mediaId) {
    try {
      const result = await db.execute(
        'SELECT FilePath, Checksum FROM CU_feedback_media WHERE Id = ?',
        [mediaId]
      );
      
      if (result[0].length === 0) {
        return { valid: false, reason: 'record_not_found' };
      }
      
      const record = result[0][0];
      const filePath = path.join(this.uploadDir, record.FilePath);
      
      if (!fs.existsSync(filePath)) {
        return { valid: false, reason: 'file_not_found' };
      }
      
      const currentChecksum = await UploadManager.generateChecksum(filePath);
      
      if (currentChecksum !== record.Checksum) {
        return { valid: false, reason: 'checksum_mismatch' };
      }
      
      return { valid: true };
    } catch (error) {
      console.error('验证文件完整性失败:', error);
      return { valid: false, reason: 'verification_error' };
    }
  }
}

module.exports = FileLifecycleManager;
