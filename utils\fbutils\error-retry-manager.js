/**
 * 错误处理和重试管理器
 * 提供统一的错误处理、重试机制和用户友好的错误提示
 */

class ErrorRetryManager {
  constructor() {
    this.defaultRetryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      retryConditions: [
        'NETWORK_ERROR',
        'TIMEOUT',
        'SERVER_ERROR',
        'TEMPORARY_FAILURE'
      ]
    };
    
    this.errorMessages = {
      // 网络错误
      'ECONNRESET': '网络连接中断，请检查网络后重试',
      'ENOTFOUND': '无法连接到服务器，请检查网络设置',
      'ETIMEDOUT': '连接超时，请检查网络或稍后重试',
      'ECONNREFUSED': '服务器拒绝连接，请稍后重试',
      'NETWORK_ERROR': '网络连接失败，请检查网络设置',
      'TIMEOUT': '请求超时，请稍后重试',

      // 微信小程序特定错误
      'UPLOAD_FAIL': '文件上传失败，请检查网络连接',
      'DOMAIN_ERROR': '域名未配置，请联系管理员',

      // 文件错误
      'FILE_TOO_LARGE': '文件过大，请选择较小的文件',
      'INVALID_FILE_TYPE': '不支持的文件类型',
      'FILE_CORRUPTED': '文件已损坏，请重新选择',
      'STORAGE_FULL': '存储空间不足，请清理后重试',

      // 权限错误
      'PERMISSION_DENIED': '权限不足，请检查应用权限设置',
      'AUTH_EXPIRED': '登录已过期，请重新登录',
      'ACCESS_FORBIDDEN': '访问被拒绝，请联系管理员',

      // 服务器错误
      'SERVER_ERROR': '服务器内部错误，请稍后重试',
      'SERVICE_UNAVAILABLE': '服务暂时不可用，请稍后重试',
      'DATABASE_ERROR': '数据库错误，请稍后重试',

      // 默认错误
      'UNKNOWN_ERROR': '未知错误，请重试或联系技术支持'
    };
  }

  /**
   * 执行带重试的异步操作
   * @param {Function} operation - 要执行的操作
   * @param {Object} retryConfig - 重试配置
   * @param {Object} context - 上下文信息
   * @returns {Promise} 操作结果
   */
  async executeWithRetry(operation, retryConfig = {}, context = {}) {
    const config = { ...this.defaultRetryConfig, ...retryConfig };
    let lastError = null;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        // 如果不是第一次尝试，显示重试信息
        if (attempt > 0) {
          this.showRetryMessage(attempt, config.maxRetries, context);
        }
        
        const result = await operation();
        
        // 成功时清除重试信息
        if (attempt > 0) {
          this.hideRetryMessage();
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        // 检查是否应该重试
        if (attempt >= config.maxRetries || !this.shouldRetry(error, config)) {
          break;
        }
        
        // 计算延迟时间
        const delay = this.calculateDelay(attempt, config);
        
        console.log(`操作失败，${delay}ms后重试 (${attempt + 1}/${config.maxRetries}):`, error.message);
        
        // 等待后重试
        await this.delay(delay);
      }
    }
    
    // 所有重试都失败了
    this.hideRetryMessage();
    throw this.enhanceError(lastError, context);
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error, config) {
    const errorType = this.getErrorType(error);
    return config.retryConditions.includes(errorType);
  }

  /**
   * 获取错误类型
   */
  getErrorType(error) {
    if (!error) return 'UNKNOWN_ERROR';

    const message = error.message || error.errMsg || '';
    const code = error.code || error.errno || '';
    const errMsg = error.errMsg || '';

    console.log('分析错误类型:', { message, code, errMsg, error });

    // 网络错误
    if (message.includes('ECONNRESET') || code === 'ECONNRESET') return 'NETWORK_ERROR';
    if (message.includes('ENOTFOUND') || code === 'ENOTFOUND') return 'NETWORK_ERROR';
    if (message.includes('ETIMEDOUT') || code === 'ETIMEDOUT') return 'TIMEOUT';
    if (message.includes('timeout') || errMsg.includes('timeout')) return 'TIMEOUT';

    // 微信小程序特定错误
    if (errMsg.includes('uploadFile:fail')) return 'UPLOAD_FAIL';
    if (errMsg.includes('request:fail')) return 'NETWORK_ERROR';
    if (errMsg.includes('url not in domain list')) return 'DOMAIN_ERROR';

    // 服务器错误
    if (error.status >= 500 && error.status < 600) return 'SERVER_ERROR';
    if (message.includes('server error')) return 'SERVER_ERROR';

    // 权限错误
    if (error.status === 401) return 'AUTH_EXPIRED';
    if (error.status === 403) return 'ACCESS_FORBIDDEN';
    if (message.includes('permission') || errMsg.includes('permission')) return 'PERMISSION_DENIED';

    // 文件错误
    if (message.includes('file too large')) return 'FILE_TOO_LARGE';
    if (message.includes('invalid file type')) return 'INVALID_FILE_TYPE';

    // 如果有具体的错误信息，返回具体错误
    if (message && message !== '未知错误，请重试或联系技术支持') {
      return message;
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * 计算延迟时间
   */
  calculateDelay(attempt, config) {
    const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
    return Math.min(delay, config.maxDelay);
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 增强错误信息
   */
  enhanceError(error, context) {
    const errorType = this.getErrorType(error);
    let userMessage = this.errorMessages[errorType];

    // 如果没有预定义的错误消息，使用错误类型作为消息
    if (!userMessage) {
      if (errorType !== 'UNKNOWN_ERROR') {
        userMessage = errorType;
      } else {
        userMessage = error.message || error.errMsg || this.errorMessages['UNKNOWN_ERROR'];
      }
    }

    const enhancedError = new Error(userMessage);
    enhancedError.originalError = error;
    enhancedError.errorType = errorType;
    enhancedError.context = context;
    enhancedError.timestamp = new Date().toISOString();

    // 保留原始错误的详细信息
    if (error.errMsg) enhancedError.errMsg = error.errMsg;
    if (error.errno) enhancedError.errno = error.errno;
    if (error.code) enhancedError.code = error.code;

    return enhancedError;
  }

  /**
   * 显示重试消息
   */
  showRetryMessage(attempt, maxRetries, context) {
    if (typeof wx !== 'undefined') {
      wx.showLoading({
        title: `重试中 ${attempt}/${maxRetries}`,
        mask: true
      });
    }
  }

  /**
   * 隐藏重试消息
   */
  hideRetryMessage() {
    if (typeof wx !== 'undefined') {
      wx.hideLoading();
    }
  }

  /**
   * 显示用户友好的错误提示
   */
  showErrorToUser(error, options = {}) {
    const {
      title = '操作失败',
      showRetryButton = true,
      onRetry = null,
      duration = 3000
    } = options;
    
    const errorType = this.getErrorType(error);
    const message = this.errorMessages[errorType] || error.message || '未知错误';
    
    if (typeof wx !== 'undefined') {
      if (showRetryButton && onRetry) {
        wx.showModal({
          title: title,
          content: message,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm && onRetry) {
              onRetry();
            }
          }
        });
      } else {
        wx.showToast({
          title: message,
          icon: 'none',
          duration: duration
        });
      }
    } else {
      console.error(`${title}: ${message}`, error);
    }
  }

  /**
   * 创建重试包装器
   */
  createRetryWrapper(operation, retryConfig = {}) {
    return (...args) => {
      return this.executeWithRetry(
        () => operation(...args),
        retryConfig,
        { operation: operation.name, args }
      );
    };
  }

  /**
   * 网络请求重试包装器
   */
  wrapNetworkRequest(requestFunction, retryConfig = {}) {
    const networkConfig = {
      ...this.defaultRetryConfig,
      retryConditions: ['NETWORK_ERROR', 'TIMEOUT', 'SERVER_ERROR'],
      ...retryConfig
    };
    
    return this.createRetryWrapper(requestFunction, networkConfig);
  }

  /**
   * 文件上传重试包装器
   */
  wrapFileUpload(uploadFunction, retryConfig = {}) {
    const uploadConfig = {
      ...this.defaultRetryConfig,
      maxRetries: 2, // 文件上传重试次数较少
      retryConditions: ['NETWORK_ERROR', 'TIMEOUT'],
      ...retryConfig
    };
    
    return this.createRetryWrapper(uploadFunction, uploadConfig);
  }

  /**
   * 记录错误日志
   */
  logError(error, context = {}) {
    const errorLog = {
      timestamp: new Date().toISOString(),
      errorType: this.getErrorType(error),
      message: error.message || error.errMsg,
      stack: error.stack,
      context: context,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown'
    };
    
    console.error('Error logged:', errorLog);
    
    // 这里可以添加错误上报逻辑
    // 比如发送到错误监控服务
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats() {
    // 这里可以实现错误统计逻辑
    return {
      totalErrors: 0,
      errorsByType: {},
      recentErrors: []
    };
  }
}

module.exports = ErrorRetryManager;
