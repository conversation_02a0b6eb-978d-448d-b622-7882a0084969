/**
 * 媒体显示管理器
 * 专门处理反馈详情页面的媒体文件显示、播放和错误处理
 */

const ErrorRetryManager = require('./error-retry-manager');

class MediaDisplayManager {
  constructor(page) {
    this.page = page;
    this.audioContext = null;
    this.currentPlayingIndex = -1;

    // 初始化错误重试管理器
    this.errorRetryManager = new ErrorRetryManager();

    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2
    };
    this.mediaStates = {
      imageLoadErrors: {},
      videoLoadErrors: {},
      audioLoadErrors: {},
      playingProgress: {},
    };
  }

  /**
   * 初始化媒体显示管理器
   */
  init() {
    console.log('MediaDisplayManager initialized');
  }

  /**
   * 处理媒体文件URL路径
   * @param {Object} mediaItem - 媒体文件对象
   * @returns {string} 处理后的完整URL
   */
  processMediaUrl(mediaItem) {
    if (!mediaItem || !mediaItem.FilePath) {
      console.warn('媒体文件路径为空:', mediaItem);
      return '';
    }

    const app = getApp();
    let fullPath = '';

    // 如果已经是完整的HTTP URL，直接使用
    if (mediaItem.FilePath.startsWith("http://") || mediaItem.FilePath.startsWith("https://")) {
      fullPath = mediaItem.FilePath;
    }
    // 如果是以/api/files/开头的路径，拼接baseUrl
    else if (mediaItem.FilePath.startsWith("/api/files/")) {
      fullPath = `${app.globalData.baseUrl}${mediaItem.FilePath}`;
    }
    // 如果是以/upload/开头的路径，直接拼接baseUrl
    else if (mediaItem.FilePath.startsWith("/upload/")) {
      fullPath = `${app.globalData.baseUrl}${mediaItem.FilePath}`;
    }
    // 如果是相对路径，尝试/api/files/前缀
    else {
      fullPath = `${app.globalData.baseUrl}/api/files/${mediaItem.FilePath}`;
    }

    return fullPath;
  }

  /**
   * 预览图片（支持高清原图）
   * @param {string} currentSrc - 当前图片URL
   * @param {Array} imageList - 图片列表
   * @param {Object} options - 预览选项
   */
  previewImages(currentSrc, imageList = [], options = {}) {
    const {
      enableOriginal = true,
      showMenuItems = ['saveImage', 'shareImage'],
      enableRotate = true,
      enableZoom = true
    } = options;

    // 构建图片URL列表
    const urls = imageList.length > 0 
      ? imageList.map(item => this.processMediaUrl(item))
      : [currentSrc];

    // 过滤掉无效的URL
    const validUrls = urls.filter(url => url && url.trim() !== '');

    if (validUrls.length === 0) {
      wx.showToast({
        title: '没有可预览的图片',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: currentSrc,
      urls: validUrls,
      showmenu: true,
      fail: (error) => {
        console.error('预览图片失败:', error);
        this.handleImageError(currentSrc, 0, () => {
          // 重试预览
          this.previewImages(currentSrc, imageList, options);
        });
      }
    });
  }

  /**
   * 播放音频
   * @param {number} index - 音频索引
   * @param {string} src - 音频URL
   * @param {Object} callbacks - 回调函数
   */
  playAudio(index, src, callbacks = {}) {
    const {
      onPlay = () => {},
      onPause = () => {},
      onEnded = () => {},
      onError = () => {},
      onTimeUpdate = () => {}
    } = callbacks;

    // 停止当前播放的音频
    this.stopAllAudio();

    // 创建新的音频上下文
    this.audioContext = wx.createInnerAudioContext();
    this.audioContext.src = src;
    this.currentPlayingIndex = index;

    // 监听播放事件
    this.audioContext.onPlay(() => {
      console.log('音频开始播放:', index);
      onPlay(index);
    });

    this.audioContext.onPause(() => {
      console.log('音频暂停:', index);
      this.currentPlayingIndex = -1;
      onPause(index);
    });

    this.audioContext.onEnded(() => {
      console.log('音频播放结束:', index);
      this.currentPlayingIndex = -1;
      this.audioContext = null;
      onEnded(index);
    });

    this.audioContext.onError((error) => {
      console.error('音频播放错误:', error);
      this.currentPlayingIndex = -1;
      this.audioContext = null;
      
      // 尝试重试播放
      this.retryAudioPlay(index, src, callbacks, 1);
      onError(error, index);
    });

    // 监听播放进度
    this.audioContext.onTimeUpdate(() => {
      const currentTime = this.audioContext.currentTime;
      const duration = this.audioContext.duration;
      onTimeUpdate(currentTime, duration, index);
    });

    // 开始播放
    this.audioContext.play();
  }

  /**
   * 重试音频播放
   */
  async retryAudioPlay(index, src, callbacks, retryCount) {
    if (retryCount > this.retryConfig.maxRetries) {
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
      return;
    }

    const delay = Math.min(
      this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, retryCount - 1),
      this.retryConfig.maxDelay
    );

    setTimeout(() => {
      console.log(`重试音频播放 (${retryCount}/${this.retryConfig.maxRetries}):`, src);
      this.playAudio(index, src, callbacks);
    }, delay);
  }

  /**
   * 暂停音频
   * @param {number} index - 音频索引
   */
  pauseAudio(index) {
    if (this.audioContext && this.currentPlayingIndex === index) {
      this.audioContext.pause();
    }
  }

  /**
   * 停止所有音频播放
   */
  stopAllAudio() {
    if (this.audioContext) {
      this.audioContext.stop();
      this.audioContext.destroy();
      this.audioContext = null;
    }
    this.currentPlayingIndex = -1;
  }

  /**
   * 处理图片加载错误
   */
  handleImageError(src, index, retryCallback) {
    console.error('图片加载失败:', src);
    
    // 记录错误状态
    this.mediaStates.imageLoadErrors[index] = true;
    
    // 尝试备用URL
    if (src && src.includes('/api/files/')) {
      const alternativeUrl = src.replace('/api/files/', '/upload/');
      
      // 测试备用URL
      this.testImageUrl(alternativeUrl).then(isAccessible => {
        if (isAccessible && retryCallback) {
          retryCallback(alternativeUrl, index, false);
          return;
        }
        this.showImageError('network', { src, index });
      }).catch(() => {
        this.showImageError('network', { src, index });
      });
    } else {
      this.showImageError('url_format', { src, index });
    }
  }

  /**
   * 测试图片URL是否可访问
   */
  testImageUrl(url) {
    return new Promise((resolve) => {
      wx.getImageInfo({
        src: url,
        success: () => resolve(true),
        fail: () => resolve(false)
      });
    });
  }

  /**
   * 显示图片错误信息
   */
  showImageError(type, data) {
    const messages = {
      network: '图片加载失败，请检查网络连接',
      url_format: '图片路径格式错误',
      not_found: '图片文件不存在'
    };

    console.warn(`图片错误 (${type}):`, data);
    
    // 可以在这里添加更多的错误处理逻辑
    // 比如显示占位图片、重试按钮等
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '未知大小';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 格式化时长
   */
  formatDuration(seconds) {
    if (!seconds || seconds === 0) return '00:00';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * 下载媒体文件
   */
  downloadMedia(mediaItem, options = {}) {
    const { showProgress = true } = options;
    
    if (!mediaItem || !mediaItem.FilePath) {
      wx.showToast({
        title: '文件路径无效',
        icon: 'none'
      });
      return;
    }

    const fileUrl = this.processMediaUrl(mediaItem);
    
    if (showProgress) {
      wx.showLoading({
        title: '下载中...',
        mask: true
      });
    }

    wx.downloadFile({
      url: fileUrl,
      success: (res) => {
        if (showProgress) {
          wx.hideLoading();
        }
        
        if (res.statusCode === 200) {
          // 根据文件类型处理下载结果
          if (mediaItem.MediaType === 'image') {
            this.saveImageToAlbum(res.tempFilePath);
          } else {
            wx.showToast({
              title: '下载完成',
              icon: 'success'
            });
          }
        } else {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        if (showProgress) {
          wx.hideLoading();
        }
        console.error('下载失败:', error);
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 保存图片到相册
   */
  saveImageToAlbum(tempFilePath) {
    wx.saveImageToPhotosAlbum({
      filePath: tempFilePath,
      success: () => {
        wx.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
      },
      fail: (error) => {
        if (error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '需要相册权限',
            content: '保存图片需要访问您的相册，请在设置中开启权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      }
    });
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.stopAllAudio();
    this.mediaStates = {
      imageLoadErrors: {},
      videoLoadErrors: {},
      audioLoadErrors: {},
      playingProgress: {},
    };
  }
}

module.exports = MediaDisplayManager;
