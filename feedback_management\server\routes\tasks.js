// 导入所需的模块和配置
const express = require("express");
const router = express.Router();
const Task = require("../models/Task");
const Project = require("../models/Project");
const {
  requireAuth,
} = require("../middleware/auth");





// 获取当前公司的所有任务单列表
router.get("/", requireAuth, async (req, res) => {
  try {
    const companyId = req.session.currentCompany.id;
    const { supply_status, keyword, date_from, date_to } = req.query;

    // 构建筛选条件对象
    const filters = {};
    if (supply_status) filters.supply_status = supply_status;
    if (keyword) filters.keyword = keyword;
    if (date_from) filters.date_from = date_from;
    if (date_to) filters.date_to = date_to;

    // 调用模型方法获取当前公司的所有任务单
    const tasks = await Task.getByCompanyId(companyId, filters);

    // 返回成功响应，包含任务单列表数据
    res.json({
      success: true,
      data: tasks,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取任务单列表失败",
    });
  }
});

// 获取工程的任务单列表
router.get(
  "/project/:projectId",
  requireAuth,
  async (req, res) => {
    try {
      // 从请求参数中获取工程ID
      const projectId = req.params.projectId;
      // 从当前会话中获取公司ID，确保用户操作的是自己公司的数据
      const companyId = req.session.currentCompany.id;
      // 从查询参数中解构获取筛选条件，包括供货状态、关键词、起始日期和结束日期
      const { supply_status, keyword, date_from, date_to } = req.query;

      // 检查工程是否存在且属于当前公司
      const project = await Project.findById(projectId);
      if (!project || project.company_id !== companyId) {
        return res.status(404).json({
          success: false,
          message: "工程不存在",
        });
      }

      // 构建筛选条件对象
      const filters = {};
      if (supply_status) filters.supply_status = supply_status;
      if (keyword) filters.keyword = keyword;
      if (date_from) filters.date_from = date_from;
      if (date_to) filters.date_to = date_to;

      // 调用模型方法获取任务单列表
      const tasks = await Task.getByProjectId(projectId, filters);

      // 返回成功响应，包含任务单列表数据
      res.json({
        success: true,
        data: tasks,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "获取任务单列表失败",
      });
    }
  }
);



// 获取任务单详情
router.get("/:taskNumber", requireAuth, async (req, res) => {
  try {
    const taskNumber = req.params.taskNumber;
    const companyId = req.session.currentCompany.id;

    const task = await Task.findById(taskNumber);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "任务单不存在",
      });
    }

    // 检查任务单所属工程是否属于当前公司（使用新的数据库结构）
    const db = require("../config/database");
    const [projectResult] = await db.execute(
      `
      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId
      FROM dbo.comProject cp
      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?
      `,
      [task.project_id, companyId]
    );

    if (projectResult.length === 0) {
      return res.status(403).json({
        success: false,
        message: "无权访问该任务单",
      });
    }

    // 获取现场信息反馈记录
    const feedbacks = await Task.getFeedbacks(taskNumber);

    res.json({
      success: true,
      data: {
        task,
        feedbacks,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取任务单详情失败",
    });
  }
});

// 更新任务单信息
router.put("/:id", requireAuth, async (req, res) => {
  try {
    const taskId = req.params.id;
    const companyId = req.session.currentCompany.id;

    // 检查任务单是否存在
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "任务单不存在",
      });
    }

    // 检查任务单所属工程是否属于当前公司
    const project = await Project.findById(task.project_id);
    if (!project || project.company_id !== companyId) {
      return res.status(403).json({
        success: false,
        message: "无权修改该任务单",
      });
    }

    const {
      task_number,
      part_name,
      concrete_grade,
      strength_grade,
      supply_status,
    } = req.body;

    // 如果修改任务单号，检查是否重复
    if (task_number && task_number !== task.task_number) {
      const exists = await Task.checkTaskNumberExists(
        task.project_id,
        task_number,
        taskId
      );
      if (exists) {
        return res.status(400).json({
          success: false,
          message: "任务单号已存在",
        });
      }
    }

    const updated = await Task.update(taskId, {
      task_number,
      part_name,
      concrete_grade,
      strength_grade,
      concrete_volume,
      supply_status,
    });

    if (!updated) {
      return res.status(400).json({
        success: false,
        message: "更新任务单信息失败",
      });
    }

    res.json({
      success: true,
      message: "更新任务单信息成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "更新任务单信息失败",
    });
  }
});



module.exports = router;
