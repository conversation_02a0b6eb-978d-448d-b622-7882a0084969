[2025-07-24 01:03:24] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:24] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":[2015493],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:24] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:03:24] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:24] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1576ms","memoryDiff":{"rss":405504,"heapUsed":1448760,"heapTotal":262144,"external":34077},"timestamp":"2025-07-24T01:03:24.637Z"}
[2025-07-24 01:03:26] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:26] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:03:26] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:26] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:03:26] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":[2015493],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:26] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:03:26] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1542ms","memoryDiff":{"rss":172032,"heapUsed":984720,"heapTotal":0,"external":39049},"timestamp":"2025-07-24T01:03:26.181Z"}
[2025-07-24 01:03:27] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:27] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:29] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:29] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:03:29] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:29] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:03:30] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:32] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:03:32] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:06:33] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":[2015493],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:33] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:06:33] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1570ms","memoryDiff":{"rss":3555328,"heapUsed":-1633664,"heapTotal":2621440,"external":10478},"timestamp":"2025-07-24T01:06:33.603Z"}
[2025-07-24 01:06:33] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:35] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:35] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:06:36] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:36] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:38] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:38] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:06:38] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:38] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:06:39] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:41] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:06:41] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:07:51] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:51] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":[2015493],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:51] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:07:51] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1558ms","memoryDiff":{"rss":1409024,"heapUsed":337096,"heapTotal":1310720,"external":30514},"timestamp":"2025-07-24T01:07:51.939Z"}
[2025-07-24 01:07:53] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:53] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:07:55] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:55] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:56] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:56] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:07:56] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:56] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:07:58] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:59] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:07:59] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:07] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":["2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:07] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:07] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:07] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1626ms","memoryDiff":{"rss":4997120,"heapUsed":150640,"heapTotal":3563520,"external":34384},"timestamp":"2025-07-24T01:08:07.994Z"}
[2025-07-24 01:08:09] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:09] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:11] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:11] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:12] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:12] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:12] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:12] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:16] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":["2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:16] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:16] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:16] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1553ms","memoryDiff":{"rss":-880640,"heapUsed":165320,"heapTotal":-524288,"external":10320},"timestamp":"2025-07-24T01:08:16.578Z"}
[2025-07-24 01:08:18] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:18] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:19] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:19] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:21] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:21] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:21] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:21] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:24] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT PersonId, BelongOrgId\n        FROM dbo.comPerson\n        WHERE CAST(PersonId AS NVARCHAR(50)) = CAST(@param0 AS NVARCHAR(50))\n          AND ISNUMERIC(PersonId) = 1\n      ","params":["2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:24] [ERROR] 根据ID查找用户失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:24] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:24] [ERROR] API请求失败 | {"method":"GET","url":"/profile","statusCode":500,"duration":"1552ms","memoryDiff":{"rss":188416,"heapUsed":551944,"heapTotal":0,"external":12734},"timestamp":"2025-07-24T01:08:24.693Z"}
[2025-07-24 01:08:26] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:26] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:27] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:27] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:29] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:29] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:29] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:29] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:08:30] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:32] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = @param0\n        ","params":["1007"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":823,"state":2,"class":24,"message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":823,"lineNumber":2,"state":2,"class":24,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:08:32] [ERROR] 获取公司工程列表失败 | {"name":"RequestError","message":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.","stack":"RequestError: The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:09:42] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:42] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:09:42] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:42] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"29ms","memoryDiff":{"rss":32768,"heapUsed":269488,"heapTotal":0,"external":9738},"timestamp":"2025-07-24T01:09:42.512Z"}
[2025-07-24 01:09:44] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:44] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:09:44] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:44] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"18ms","memoryDiff":{"rss":0,"heapUsed":270104,"heapTotal":0,"external":17930},"timestamp":"2025-07-24T01:09:44.352Z"}
[2025-07-24 01:09:44] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:44] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:44] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"585ms","memoryDiff":{"rss":8192,"heapUsed":246528,"heapTotal":0,"external":17962},"timestamp":"2025-07-24T01:09:44.966Z"}
[2025-07-24 01:09:46] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:46] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:46] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"312ms","memoryDiff":{"rss":1044480,"heapUsed":-5098048,"heapTotal":208896,"external":-190706},"timestamp":"2025-07-24T01:09:46.837Z"}
[2025-07-24 01:09:52] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:52] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:09:52] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:53] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"1133ms","memoryDiff":{"rss":946176,"heapUsed":11700704,"heapTotal":0,"external":177713},"timestamp":"2025-07-24T01:09:53.010Z"}
[2025-07-24 01:09:54] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:54] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:54] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"1203ms","memoryDiff":{"rss":4096,"heapUsed":5013512,"heapTotal":5242880,"external":-167681},"timestamp":"2025-07-24T01:09:54.235Z"}
[2025-07-24 01:09:54] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:54] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:09:54] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:54] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"832ms","memoryDiff":{"rss":114688,"heapUsed":1098864,"heapTotal":7340032,"external":-90113},"timestamp":"2025-07-24T01:09:54.880Z"}
[2025-07-24 01:09:54] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:54] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:09:54] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"87ms","memoryDiff":{"rss":208896,"heapUsed":4832280,"heapTotal":208896,"external":283985},"timestamp":"2025-07-24T01:09:54.991Z"}
[2025-07-24 01:13:00] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:13:00] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"76ms","memoryDiff":{"rss":458752,"heapUsed":508624,"heapTotal":0,"external":10822},"timestamp":"2025-07-24T01:13:00.541Z"}
[2025-07-24 01:13:00] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:13:00] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"64ms","memoryDiff":{"rss":196608,"heapUsed":245872,"heapTotal":0,"external":17930},"timestamp":"2025-07-24T01:13:00.630Z"}
[2025-07-24 01:13:00] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"36ms","memoryDiff":{"rss":237568,"heapUsed":246352,"heapTotal":0,"external":-9300},"timestamp":"2025-07-24T01:13:00.701Z"}
[2025-07-24 01:13:00] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:00] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"40ms","memoryDiff":{"rss":188416,"heapUsed":237976,"heapTotal":0,"external":17962},"timestamp":"2025-07-24T01:13:00.804Z"}
[2025-07-24 01:13:07] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:07] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:13:07] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:07] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"34ms","memoryDiff":{"rss":204800,"heapUsed":-338200,"heapTotal":-1048576,"external":-18452},"timestamp":"2025-07-24T01:13:07.997Z"}
[2025-07-24 01:13:08] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:08] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:13:08] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:08] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"85ms","memoryDiff":{"rss":507904,"heapUsed":249848,"heapTotal":0,"external":9738},"timestamp":"2025-07-24T01:13:08.100Z"}
[2025-07-24 01:13:08] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:08] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:08] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"111ms","memoryDiff":{"rss":0,"heapUsed":238096,"heapTotal":0,"external":17962},"timestamp":"2025-07-24T01:13:08.215Z"}
[2025-07-24 01:13:08] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:08] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:08] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"36ms","memoryDiff":{"rss":12288,"heapUsed":-342376,"heapTotal":0,"external":-17764},"timestamp":"2025-07-24T01:13:08.417Z"}
[2025-07-24 01:13:22] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:13:22] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"23ms","memoryDiff":{"rss":12288,"heapUsed":246376,"heapTotal":0,"external":9738},"timestamp":"2025-07-24T01:13:22.181Z"}
[2025-07-24 01:13:22] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:13:22] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"54ms","memoryDiff":{"rss":417792,"heapUsed":335408,"heapTotal":0,"external":17954},"timestamp":"2025-07-24T01:13:22.242Z"}
[2025-07-24 01:13:22] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"56ms","memoryDiff":{"rss":233472,"heapUsed":-391448,"heapTotal":262144,"external":-34754},"timestamp":"2025-07-24T01:13:22.287Z"}
[2025-07-24 01:13:22] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:13:22] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"53ms","memoryDiff":{"rss":0,"heapUsed":233712,"heapTotal":0,"external":17962},"timestamp":"2025-07-24T01:13:22.366Z"}
[2025-07-24 01:27:19] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:19] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:27:19] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:19] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"27ms","memoryDiff":{"rss":-225280,"heapUsed":-171592,"heapTotal":0,"external":-8338},"timestamp":"2025-07-24T01:27:19.780Z"}
[2025-07-24 01:27:19] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:19] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:19] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"16ms","memoryDiff":{"rss":4096,"heapUsed":232480,"heapTotal":0,"external":17962},"timestamp":"2025-07-24T01:27:19.816Z"}
[2025-07-24 01:27:20] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:20] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:27:20] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:20] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"14ms","memoryDiff":{"rss":4096,"heapUsed":234032,"heapTotal":0,"external":9738},"timestamp":"2025-07-24T01:27:20.814Z"}
[2025-07-24 01:27:20] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:20] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:20] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"18ms","memoryDiff":{"rss":184320,"heapUsed":-395560,"heapTotal":1048576,"external":-18380},"timestamp":"2025-07-24T01:27:20.851Z"}
[2025-07-24 01:27:21] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:21] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:27:21] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:21] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"15ms","memoryDiff":{"rss":0,"heapUsed":237192,"heapTotal":0,"external":17930},"timestamp":"2025-07-24T01:27:21.610Z"}
[2025-07-24 01:27:21] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:21] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:21] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"17ms","memoryDiff":{"rss":4096,"heapUsed":233592,"heapTotal":0,"external":9770},"timestamp":"2025-07-24T01:27:21.650Z"}
[2025-07-24 01:27:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:27:49] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"27ms","memoryDiff":{"rss":843776,"heapUsed":327680,"heapTotal":0,"external":17930},"timestamp":"2025-07-24T01:27:49.320Z"}
[2025-07-24 01:27:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT DISTINCT\n          cp.ProjectId as project_id,\n          cp.ProjectName as project_name,\n          cp.ProjectId as project_code,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit\n        FROM dbo.comProject cp WITH (NOLOCK)\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        INNER JOIN dbo.X_ppProduceOrder xpo ON cp.ProjectId = xpo.ProjectId\n        INNER JOIN dbo.CU_feedbacks f WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n          ON xpo.BillNo = f.TaskNumber\n        WHERE cp.X_OrgId = @param0 AND f.FeedbackUserId = @param1 AND f.Status = 1\n        ORDER BY cp.ProjectName\n        ","params":["1007","2015493"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] 获取分类反馈单数据失败 | {"name":"RequestError","message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","stack":"RequestError: Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.\n    at handleError (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\mssql\\lib\\tedious\\request.js:384:15)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at RequestTokenHandler.onErrorMessage (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\handler.js:285:21)\n    at Readable.<anonymous> (d:\\Code-workspace\\miniprogram-practice\\feedback_management\\server\\node_modules\\tedious\\lib\\token\\token-stream-parser.js:18:33)\n    at Readable.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushObjectMode (node:internal/streams/readable:538:3)\n    at Readable.push (node:internal/streams/readable:393:5)\n    at nextAsync (node:internal/streams/from:194:22)"}
[2025-07-24 01:27:49] [ERROR] getOrSet执行失败 | {"key":"grouped_feedback:2015493:1007","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'dbo.CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] API请求失败 | {"method":"GET","url":"/user/grouped","statusCode":500,"duration":"42ms","memoryDiff":{"rss":319488,"heapUsed":334592,"heapTotal":0,"external":9762},"timestamp":"2025-07-24T01:27:49.367Z"}
[2025-07-24 01:27:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"30ms","memoryDiff":{"rss":409600,"heapUsed":-422800,"heapTotal":1048576,"external":17962},"timestamp":"2025-07-24T01:27:49.385Z"}
[2025-07-24 01:27:49] [ERROR] SQL Server查询执行失败 | {"query":"\n        SELECT i.Id as id, i.TaskNumber as task_number, i.FeedbackUserId as feedback_user_id,\n               FORMAT(i.FeedbackTime, 'yyyy-MM-dd HH:mm:ss') as feedback_time,\n               CAST(i.Notes AS NVARCHAR(MAX)) as notes, i.Category as category,\n               i.Status as status, i.Longitude as longitude, i.Latitude as latitude,\n               i.LocationDesc as location_desc, i.LocationStatus as location_status,\n               cgp.PersonName as feedback_user_name,\n               xpo.X_JZPart as part_name,\n               cp.ProjectName as project_name,\n               ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n               (SELECT COUNT(*) FROM CU_feedback_media im WHERE im.FeedbackId = i.Id AND im.Status = 1) as media_count\n        FROM CU_feedbacks i WITH (INDEX(IX_CU_feedbacks_FeedbackUserId_Status))\n        LEFT JOIN dbo.comPerson cper ON i.FeedbackUserId = cper.PersonId\n        LEFT JOIN dbo.comGroupPerson cgp ON cper.PersonId = cgp.PersonId\n        LEFT JOIN dbo.X_ppProduceOrder xpo ON i.TaskNumber = xpo.BillNo\n        LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n        LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n        WHERE i.FeedbackUserId = @param0 AND i.Status = 1 AND cp.X_OrgId = @param1\n        ORDER BY i.FeedbackTime DESC\n        OFFSET @param2 ROWS FETCH NEXT @param3 ROWS ONLY\n      ","params":["2015493","1007",0,20],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] getOrSet执行失败 | {"key":"user_feedback:2015493:1007:20:0","error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedbacks_FeedbackUserId_Status' on table 'CU_feedbacks' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":2}},"name":"RequestError","number":308,"lineNumber":2,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 01:27:49] [ERROR] API请求失败 | {"method":"GET","url":"/user/simple","statusCode":500,"duration":"34ms","memoryDiff":{"rss":245760,"heapUsed":238104,"heapTotal":0,"external":-18380},"timestamp":"2025-07-24T01:27:49.422Z"}
[2025-07-24 01:30:49] [ERROR] API请求失败 | {"method":"GET","url":"/health","statusCode":401,"duration":"3ms","memoryDiff":{"rss":90112,"heapUsed":45688,"heapTotal":0,"external":24},"timestamp":"2025-07-24T01:30:49.142Z"}
[2025-07-24 01:31:34] [ERROR] API请求失败 | {"method":"GET","url":"/health","statusCode":401,"duration":"3ms","memoryDiff":{"rss":65536,"heapUsed":39704,"heapTotal":0,"external":24},"timestamp":"2025-07-24T01:31:34.082Z"}
[2025-07-24 01:32:20] [ERROR] API请求失败 | {"method":"GET","url":"/ping","statusCode":401,"duration":"3ms","memoryDiff":{"rss":65536,"heapUsed":39240,"heapTotal":0,"external":24},"timestamp":"2025-07-24T01:32:20.334Z"}
[2025-07-24 01:35:29] [ERROR] API请求失败 | {"method":"GET","url":"/health","statusCode":401,"duration":"6ms","memoryDiff":{"rss":159744,"heapUsed":155752,"heapTotal":0,"external":24},"timestamp":"2025-07-24T01:35:29.215Z"}
[2025-07-24 01:37:39] [ERROR] API请求失败 | {"method":"GET","url":"/health","statusCode":401,"duration":"3ms","memoryDiff":{"rss":65536,"heapUsed":91352,"heapTotal":0,"external":24},"timestamp":"2025-07-24T01:37:39.365Z"}
[2025-07-24 01:38:38] [ERROR] API请求失败 | {"method":"GET","url":"/health","statusCode":401,"duration":"5ms","memoryDiff":{"rss":16384,"heapUsed":85080,"heapTotal":0,"external":24},"timestamp":"2025-07-24T01:38:38.050Z"}
[2025-07-24 02:30:17] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1009"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 02:30:22] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1005"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:24:51] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:28:28] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:30:24] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:32:33] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:34:02] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:34:31] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:36:10] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:36:28] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:37:03] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:37:39] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:40:59] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1012"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 08:54:28] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1013"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 09:00:06] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1009"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 09:01:10] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1009"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 09:02:10] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1013"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 09:06:15] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1013"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
[2025-07-24 09:06:57] [ERROR] SQL Server查询执行失败 | {"query":"SELECT Id, FeedbackId, MediaType, FileName, FilePath, FileSize, Duration, UploadTime, Status, CreatedAt, UpdatedAt\n         FROM CU_feedback_media WITH (INDEX(IX_CU_feedback_media_FeedbackId_Status))\n         WHERE FeedbackId = @param0 AND Status = 1\n         ORDER BY CreatedAt ASC","params":["1013"],"error":{"code":"EREQUEST","originalError":{"info":{"name":"ERROR","handlerName":"onErrorMessage","number":308,"state":1,"class":16,"message":"Index 'IX_CU_feedback_media_FeedbackId_Status' on table 'CU_feedback_media' (specified in the FROM clause) does not exist.","serverName":"LAPTOP-41PESEN1","procName":"","lineNumber":1}},"name":"RequestError","number":308,"lineNumber":1,"state":1,"class":16,"serverName":"LAPTOP-41PESEN1","procName":"","precedingErrors":[]}}
