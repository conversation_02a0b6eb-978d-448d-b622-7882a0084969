/**
 * 登录页面
 * 处理用户登录功能
 *
 * @fileoverview 用户登录页面，支持手机号登录
 * <AUTHOR>
 * @version 1.0.0
 */

const app = getApp();
// 导入位置权限管理器
const LocationPermissionManager = require("../../utils/location-permission-manager");

/**
 * 登录页面配置
 */
Page({
  /**
   * 页面的初始数据
   * @type {Object}
   */
  data: {
    /** @type {string} 手机号 */
    phone: "",

    /** @type {string} 密码 */
    password: "",

    /** @type {boolean} 登录加载状态 */
    loading: false,


  },

  /**
   * 页面加载时的生命周期函数
   * 检查用户是否已登录，如果已登录则跳转到首页
   *
   * @param {Object} options - 页面参数
   */
  onLoad(options) {
    // 检查是否已登录
    const userInfo = wx.getStorageSync("userInfo");
    if (userInfo) {
      wx.switchTab({
        url: "/pages/index/index",
      });
    }
  },

  /**
   * 手机号输入事件处理
   *
   * @param {Object} e - 事件对象
   * @param {string} e.detail.value - 输入的手机号
   */
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value.trim(),
    });
  },

  /**
   * 密码输入事件处理
   *
   * @param {Object} e - 事件对象
   * @param {string} e.detail.value - 输入的密码
   */
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value,
    });
  },

  // 登录
  onLogin: function () {
    var that = this;
    var phone = this.data.phone;
    var password = this.data.password;

    if (!phone || !password) {
      wx.showToast({
        title: "请输入手机号和密码",
        icon: "none",
      });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      wx.showToast({
        title: "手机号格式不正确",
        icon: "none",
      });
      return;
    }

    // 在登录前先请求位置权限确认
    that.requestLocationPermissionBeforeLogin(phone, password);
  },

  /**
   * 在登录前请求位置权限确认
   * @param {string} phone - 手机号
   * @param {string} password - 密码
   */
  async requestLocationPermissionBeforeLogin(phone, password) {
    const that = this;

    try {
      // 先检查当前权限状态
      const permissionStatus = await LocationPermissionManager.checkLocationPermission();

      if (permissionStatus.hasPermission) {
        // 已有权限，直接登录
        that.performLogin(phone, password);
        return;
      }

      // 显示统一的权限申请弹窗
      const userConfirmed = await new Promise((resolve) => {
        wx.showModal({
          title: '位置权限申请',
          content: '为了记录反馈位置信息，需要获取您的位置权限。点击"授权"将跳转到权限设置页面。',
          confirmText: '授权',
          cancelText: '取消',
          success: (res) => {
            resolve(res.confirm);
          },
          fail: () => {
            resolve(false);
          }
        });
      });

      if (!userConfirmed) {
        // 用户取消，提示无法登录
        wx.showToast({
          title: "需要位置权限才能登录",
          icon: "none",
          duration: 2000
        });
        return;
      }

      // 用户确认，直接申请权限或引导到设置
      if (permissionStatus.isDenied) {
        // 权限被拒绝，直接引导到设置页面
        wx.openSetting({
          success: (settingRes) => {
            if (settingRes.authSetting['scope.userLocation']) {
              wx.showToast({
                title: '权限开启成功',
                icon: 'success'
              });
              // 权限开启成功，执行登录
              setTimeout(() => {
                that.performLogin(phone, password);
              }, 1500);
            } else {
              wx.showToast({
                title: "需要位置权限才能登录",
                icon: "none",
              });
            }
          },
          fail: () => {
            wx.showToast({
              title: "请在设置中开启位置权限",
              icon: "none",
            });
          }
        });
      } else {
        // 权限未知，尝试申请
        wx.authorize({
          scope: 'scope.userLocation',
          success: () => {
            console.log('位置权限申请成功');
            wx.showToast({
              title: '权限获取成功',
              icon: 'success'
            });
            // 权限获取成功，执行登录
            setTimeout(() => {
              that.performLogin(phone, password);
            }, 1500);
          },
          fail: () => {
            console.log('位置权限申请失败，引导到设置');
            // 申请失败，引导到设置页面
            wx.openSetting({
              success: (settingRes) => {
                if (settingRes.authSetting['scope.userLocation']) {
                  wx.showToast({
                    title: '权限开启成功',
                    icon: 'success'
                  });
                  // 权限开启成功，执行登录
                  setTimeout(() => {
                    that.performLogin(phone, password);
                  }, 1500);
                } else {
                  wx.showToast({
                    title: "需要位置权限才能登录",
                    icon: "none",
                  });
                }
              },
              fail: () => {
                wx.showToast({
                  title: "请在设置中开启位置权限",
                  icon: "none",
                });
              }
            });
          }
        });
      }
    } catch (error) {
      console.error('位置权限确认失败:', error);
      wx.showToast({
        title: "权限确认失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 执行实际的登录操作
   * @param {string} phone - 手机号
   * @param {string} password - 密码
   */
  performLogin(phone, password) {
    const that = this;

    this.setData({ loading: true });

    const app = getApp();
    wx.request({
      url: `${app.globalData.baseUrl}/api/auth/login`,
      method: "POST",
      data: {
        phone: phone,
        password: password,
      },
      header: {
        "content-type": "application/json",
      },
      success: function (res) {
        that.setData({ loading: false });

        if (res.statusCode === 200) {
          if (res.data && res.data.success === true) {
            // 保存token
            if (res.data.data.token) {
              wx.setStorageSync("authToken", res.data.data.token);
            }

            // 保存用户信息
            try {
              wx.setStorageSync("userInfo", res.data.data.user);
              wx.setStorageSync("companies", res.data.data.companies);

              // 如果有公司列表，自动选择第一个公司
              if (
                res.data.data.companies &&
                res.data.data.companies.length > 0
              ) {
                const firstCompany = res.data.data.companies[0];
                wx.setStorageSync("currentCompany", firstCompany);

                // 调用切换公司API
                that.switchToFirstCompany(firstCompany);
              }
            } catch (e) {
              console.error("保存用户信息失败:", e);
            }

            wx.showToast({
              title: "登录成功",
              icon: "success",
            });

            // 直接跳转到首页，因为位置权限已经确认
            setTimeout(function () {
              wx.switchTab({
                url: "/pages/index/index",
              });
            }, 1500);
          } else {
            var errorMessage = "登录失败";
            if (res.data && res.data.message) {
              errorMessage = res.data.message;
            }
            wx.showToast({
              title: errorMessage,
              icon: "none",
            });
          }
        } else {
          var errorMsg = "登录失败";
          if (res.statusCode === 401) {
            errorMsg = "手机号或密码错误";
          } else if (res.statusCode === 400) {
            errorMsg = "请求参数错误";
          }
          wx.showToast({
            title: errorMsg,
            icon: "none",
          });
        }
      },
      fail: function (error) {
        that.setData({ loading: false });
        wx.showToast({
          title: "网络错误，请重试",
          icon: "none",
        });
      },
    });
  },

  // 切换到第一个公司
  switchToFirstCompany(company) {
    const app = getApp();
    wx.request({
      url: `${app.globalData.baseUrl}/api/auth/switch-company`,
      method: "POST",
      data: { companyId: company.id },
      header: {
        "content-type": "application/json",
        Authorization: wx.getStorageSync("authToken"),
      },
      success: function (res) {
        if (res.data && res.data.success) {
        }
      },
      fail: function (error) {
        // 自动切换公司失败，忽略错误
      },
    });
  },



});
