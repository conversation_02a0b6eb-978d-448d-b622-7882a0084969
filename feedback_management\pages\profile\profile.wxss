/* pages/profile/profile.wxss */
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息卡片 */
.profile-card {
  background-color: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.avatar-section {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #1296DB, #42a5f5);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.avatar-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
  display: block;
}

/* 公司信息卡片 */
.company-card, .companies-card, .menu-card {
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-header {
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.company-info {
  padding: 30rpx 40rpx;
}

.company-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}



/* 公司列表 */
.companies-list {
  padding: 0 40rpx;
}

.company-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.company-item:last-child {
  border-bottom: none;
}

.company-status {
  flex-shrink: 0;
}

.current-tag {
  background-color: #1296DB;
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 功能菜单 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
  width: 60rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-body {
  padding: 40rpx;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.form-label.required::after {
  content: " *";
  color: #ff4444;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1296DB;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-primary {
  background-color: #1296DB;
  color: white;
}

/* 密码提示样式 */
.password-tips {
  margin-top: 15rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #1296DB;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.tip-item {
  font-size: 22rpx;
  color: #888;
  display: block;
  line-height: 1.5;
  margin-bottom: 5rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}



/* 密码匹配提示样式 */
.password-match {
  margin-top: 15rpx;
}

.match-text {
  font-size: 24rpx;
  font-weight: bold;
}

.match-success {
  color: #4caf50;
}

.match-error {
  color: #ff4444;
}