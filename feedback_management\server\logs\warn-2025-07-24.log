[2025-07-24 00:49:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2168ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/52"}
[2025-07-24 01:03:24] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:03:24] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:03:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3268ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/54"}
[2025-07-24 01:03:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3126ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/53"}
[2025-07-24 01:03:27] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:03:27] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:03:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3075ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/54"}
[2025-07-24 01:03:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3090ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/54"}
[2025-07-24 01:03:30] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:03:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3093ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/54"}
[2025-07-24 01:06:33] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:06:35] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3087ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/55"}
[2025-07-24 01:06:36] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:06:36] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:06:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3076ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/55"}
[2025-07-24 01:06:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3070ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/55"}
[2025-07-24 01:06:39] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:06:41] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3083ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/55"}
[2025-07-24 01:07:51] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:07:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3110ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/56"}
[2025-07-24 01:07:55] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:07:55] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:07:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3091ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/56"}
[2025-07-24 01:07:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3092ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/56"}
[2025-07-24 01:07:58] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:07:59] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3082ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/56"}
[2025-07-24 01:08:07] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:09] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3147ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/57"}
[2025-07-24 01:08:11] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:11] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:12] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3089ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/57"}
[2025-07-24 01:08:12] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3087ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/57"}
[2025-07-24 01:08:16] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:18] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3098ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/58"}
[2025-07-24 01:08:19] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:19] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3090ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/58"}
[2025-07-24 01:08:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3086ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/58"}
[2025-07-24 01:08:24] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3109ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/59"}
[2025-07-24 01:08:27] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:27] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3090ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/59"}
[2025-07-24 01:08:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3084ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/59"}
[2025-07-24 01:08:30] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"The operating system returned error 21(设备未就绪。) to SQL Server during a read at offset 0x00000000654000 in file 'E:\\NBSDB\\NBSTEST.MDF'. Additional messages in the SQL Server error log and operating system error log may provide more detail. This is a severe system-level error condition that threatens database integrity and must be corrected immediately. Complete a full database consistency check (DBCC CHECKDB). This error can be caused by many factors; for more information, see SQL Server Books Online."}
[2025-07-24 01:08:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3064ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/59"}
[2025-07-24 01:09:40] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"24469ms","memoryDiff":{"rss":51949568,"heapUsed":24930800,"heapTotal":54734848,"external":235497},"timestamp":"2025-07-24T01:09:40.007Z"}
[2025-07-24 01:09:40] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"24470ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/60"}
[2025-07-24 01:09:40] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"45403ms","memoryDiff":{"rss":61972480,"heapUsed":31670248,"heapTotal":62390272,"external":305099},"timestamp":"2025-07-24T01:09:40.911Z"}
[2025-07-24 01:09:40] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"45403ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/60"}
[2025-07-24 01:09:42] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2470ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/61"}
[2025-07-24 01:09:46] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"51003ms","memoryDiff":{"rss":83472384,"heapUsed":35208960,"heapTotal":68034560,"external":442097},"timestamp":"2025-07-24T01:09:46.523Z"}
[2025-07-24 01:09:46] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"51004ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/60"}
[2025-07-24 01:09:46] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"5624ms","memoryDiff":{"rss":21508096,"heapUsed":3967704,"heapTotal":5644288,"external":139582},"timestamp":"2025-07-24T01:09:46.537Z"}
[2025-07-24 01:09:46] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"5624ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/61"}
[2025-07-24 01:09:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3206ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/61"}
[2025-07-24 01:09:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3918ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/61"}
[2025-07-24 01:09:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2450ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/61"}
[2025-07-24 01:09:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3867ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/61"}
[2025-07-24 01:27:28] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"2364ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/62"}
[2025-07-24 01:27:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2391ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/62"}
[2025-07-24 01:27:31] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2186ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/62"}
[2025-07-24 01:27:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2624ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/62"}
[2025-07-24 01:27:35] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2213ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/62"}
[2025-07-24 01:38:32] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"6081ms","memoryDiff":{"rss":46342144,"heapUsed":28514496,"heapTotal":45977600,"external":397430},"timestamp":"2025-07-24T01:38:32.392Z"}
[2025-07-24 01:38:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"6098ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/63"}
[2025-07-24 01:38:32] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"27064ms","memoryDiff":{"rss":44126208,"heapUsed":19129408,"heapTotal":45559808,"external":368803},"timestamp":"2025-07-24T01:38:32.527Z"}
[2025-07-24 01:38:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"27065ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/63"}
[2025-07-24 01:38:35] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2613ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/63"}
[2025-07-24 01:38:37] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2659ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/63"}
[2025-07-24 01:41:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2727ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/64"}
[2025-07-24 01:41:59] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2448ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/64"}
[2025-07-24 01:42:01] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3874ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/64"}
[2025-07-24 01:42:04] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2411ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/64"}
[2025-07-24 01:42:08] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2350ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/65"}
[2025-07-24 01:42:11] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2433ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/65"}
[2025-07-24 01:42:12] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3428ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/65"}
[2025-07-24 01:42:15] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2188ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/65"}
[2025-07-24 01:42:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2131ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/66"}
[2025-07-24 01:42:58] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2010ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/66"}
[2025-07-24 01:43:00] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3185ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/66"}
[2025-07-24 01:43:44] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2232ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/67"}
[2025-07-24 01:43:46] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2034ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/67"}
[2025-07-24 01:43:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2441ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/67"}
[2025-07-24 01:43:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2127ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/67"}
[2025-07-24 01:44:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2125ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/68"}
[2025-07-24 01:45:00] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2180ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/68"}
[2025-07-24 01:45:01] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3441ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/68"}
[2025-07-24 01:45:03] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2252ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/68"}
[2025-07-24 01:46:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3006ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:22] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2870ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4009ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:27] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4952ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:34] [WARN] API响应缓慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"6524ms","memoryDiff":{"rss":25096192,"heapUsed":26515288,"heapTotal":24117248,"external":221768},"timestamp":"2025-07-24T01:49:34.284Z"}
[2025-07-24 01:49:34] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"6524ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2418ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:42] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3718ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:43] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4542ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:45] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2395ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:49] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2719ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3888ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4727ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:49:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2847ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 01:58:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2256ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/69"}
[2025-07-24 02:25:51] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2162ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/77"}
[2025-07-24 02:25:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2852ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/77"}
[2025-07-24 02:25:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3548ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/77"}
[2025-07-24 02:30:04] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2071ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/78"}
[2025-07-24 02:30:07] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2644ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/78"}
[2025-07-24 02:30:08] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3260ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/78"}
[2025-07-24 02:30:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2252ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/78"}
[2025-07-24 02:30:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2027ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/78"}
[2025-07-24 02:34:04] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2407ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/81"}
[2025-07-24 02:34:08] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3236ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/81"}
[2025-07-24 02:34:08] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3887ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/81"}
[2025-07-24 02:34:19] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2065ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82"}
[2025-07-24 02:34:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3169ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82"}
[2025-07-24 02:34:23] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2132ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82"}
[2025-07-24 02:34:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220003","duration":"2278ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82"}
[2025-07-24 02:34:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"2232ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82"}
[2025-07-24 02:34:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4538ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/82"}
[2025-07-24 02:58:31] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16099ms","memoryDiff":{"rss":1282048,"heapUsed":123728,"heapTotal":0,"external":2857},"timestamp":"2025-07-24T02:58:31.276Z"}
[2025-07-24 02:58:31] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16100ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/85"}
[2025-07-24 03:01:00] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"13948ms","memoryDiff":{"rss":16384,"heapUsed":649984,"heapTotal":0,"external":-6655},"timestamp":"2025-07-24T03:01:00.527Z"}
[2025-07-24 03:01:00] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"13949ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/86"}
[2025-07-24 03:03:10] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"24298ms","memoryDiff":{"rss":34660352,"heapUsed":16582488,"heapTotal":42151936,"external":241180},"timestamp":"2025-07-24T03:03:10.398Z"}
[2025-07-24 03:03:10] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"24299ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/87"}
[2025-07-24 03:03:12] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"5892ms","memoryDiff":{"rss":43016192,"heapUsed":28439248,"heapTotal":44773376,"external":398324},"timestamp":"2025-07-24T03:03:12.013Z"}
[2025-07-24 03:03:12] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"5893ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/87"}
[2025-07-24 03:03:13] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"27152ms","memoryDiff":{"rss":59469824,"heapUsed":9823536,"heapTotal":54263808,"external":91282},"timestamp":"2025-07-24T03:03:13.265Z"}
[2025-07-24 03:03:13] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"27152ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/87"}
[2025-07-24 03:03:13] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3172ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/87"}
[2025-07-24 03:04:36] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16674ms","memoryDiff":{"rss":937984,"heapUsed":91088,"heapTotal":1048576,"external":10059},"timestamp":"2025-07-24T03:04:36.833Z"}
[2025-07-24 03:04:36] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16676ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/87"}
[2025-07-24 03:05:25] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"13464ms","memoryDiff":{"rss":-2338816,"heapUsed":129600,"heapTotal":-1048576,"external":3237},"timestamp":"2025-07-24T03:05:25.321Z"}
[2025-07-24 03:05:25] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"13464ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/88"}
[2025-07-24 03:15:09] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"15642ms","memoryDiff":{"rss":937984,"heapUsed":107224,"heapTotal":0,"external":11123},"timestamp":"2025-07-24T03:15:09.242Z"}
[2025-07-24 03:15:09] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"15643ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/89"}
[2025-07-24 03:27:59] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"27545ms","memoryDiff":{"rss":35721216,"heapUsed":19802296,"heapTotal":39792640,"external":491080},"timestamp":"2025-07-24T03:27:59.004Z"}
[2025-07-24 03:27:59] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"27545ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:28:14] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"43372ms","memoryDiff":{"rss":44666880,"heapUsed":17916392,"heapTotal":47448064,"external":445854},"timestamp":"2025-07-24T03:28:14.844Z"}
[2025-07-24 03:28:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"43374ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:28:14] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"23473ms","memoryDiff":{"rss":41852928,"heapUsed":11733120,"heapTotal":42414080,"external":213939},"timestamp":"2025-07-24T03:28:14.960Z"}
[2025-07-24 03:28:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"23473ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:28:15] [WARN] API响应缓慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"16444ms","memoryDiff":{"rss":9515008,"heapUsed":9391648,"heapTotal":8126464,"external":68914},"timestamp":"2025-07-24T03:28:15.480Z"}
[2025-07-24 03:28:15] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"16445ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:30:24] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"33516ms","memoryDiff":{"rss":4890624,"heapUsed":6074440,"heapTotal":10747904,"external":174807},"timestamp":"2025-07-24T03:30:24.479Z"}
[2025-07-24 03:30:24] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"33516ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:30:26] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"19855ms","memoryDiff":{"rss":39751680,"heapUsed":4861240,"heapTotal":43724800,"external":282927},"timestamp":"2025-07-24T03:30:26.458Z"}
[2025-07-24 03:30:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"19855ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:30:28] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2389ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/92"}
[2025-07-24 03:30:30] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"23527ms","memoryDiff":{"rss":49688576,"heapUsed":29897336,"heapTotal":53215232,"external":206173},"timestamp":"2025-07-24T03:30:30.119Z"}
[2025-07-24 03:30:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"23527ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/91"}
[2025-07-24 03:30:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"3232ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/92"}
[2025-07-24 03:30:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2531ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/92"}
[2025-07-24 03:30:34] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2219ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/92"}
[2025-07-24 03:31:26] [WARN] API响应缓慢 | {"method":"GET","url":"/B05AJA220003","statusCode":304,"duration":"16028ms","memoryDiff":{"rss":1355776,"heapUsed":387920,"heapTotal":1048576,"external":45051},"timestamp":"2025-07-24T03:31:26.630Z"}
[2025-07-24 03:31:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220003","duration":"16029ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/92"}
[2025-07-24 03:31:26] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":200,"duration":"16300ms","memoryDiff":{"rss":1167360,"heapUsed":203104,"heapTotal":1048576,"external":45204},"timestamp":"2025-07-24T03:31:26.919Z"}
[2025-07-24 03:31:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"16300ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/92"}
[2025-07-24 03:32:38] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"24858ms","memoryDiff":{"rss":41771008,"heapUsed":21144872,"heapTotal":44511232,"external":343706},"timestamp":"2025-07-24T03:32:38.411Z"}
[2025-07-24 03:32:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"24858ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:32:40] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"6567ms","memoryDiff":{"rss":38866944,"heapUsed":6015480,"heapTotal":41156608,"external":62369},"timestamp":"2025-07-24T03:32:40.150Z"}
[2025-07-24 03:32:40] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"6568ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:32:41] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"27761ms","memoryDiff":{"rss":50900992,"heapUsed":35092520,"heapTotal":54263808,"external":477220},"timestamp":"2025-07-24T03:32:41.330Z"}
[2025-07-24 03:32:41] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"27760ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:32:41] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3170ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:34:48] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220004","statusCode":200,"duration":"34244ms","memoryDiff":{"rss":1695744,"heapUsed":416488,"heapTotal":0,"external":38100},"timestamp":"2025-07-24T03:34:48.239Z"}
[2025-07-24 03:34:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220004","duration":"34244ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:34:48] [WARN] API响应缓慢 | {"method":"GET","url":"/B05AJA220004","statusCode":200,"duration":"34256ms","memoryDiff":{"rss":1871872,"heapUsed":646992,"heapTotal":0,"external":38432},"timestamp":"2025-07-24T03:34:48.241Z"}
[2025-07-24 03:34:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220004","duration":"34256ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:34:50] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"35749ms","memoryDiff":{"rss":12959744,"heapUsed":4289992,"heapTotal":12058624,"external":30984},"timestamp":"2025-07-24T03:34:50.972Z"}
[2025-07-24 03:34:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"35749ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:34:52] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3928ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 03:37:55] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16540ms","memoryDiff":{"rss":-991232,"heapUsed":716000,"heapTotal":0,"external":22163},"timestamp":"2025-07-24T03:37:55.385Z"}
[2025-07-24 03:37:55] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16539ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/93"}
[2025-07-24 05:34:51] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"20083ms","memoryDiff":{"rss":40710144,"heapUsed":27952976,"heapTotal":40316928,"external":489924},"timestamp":"2025-07-24T05:34:51.214Z"}
[2025-07-24 05:34:51] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"20086ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:34:52] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"28410ms","memoryDiff":{"rss":54149120,"heapUsed":44327120,"heapTotal":52957184,"external":563080},"timestamp":"2025-07-24T05:34:52.140Z"}
[2025-07-24 05:34:52] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"28410ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:34:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2244ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:34:53] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"29841ms","memoryDiff":{"rss":81612800,"heapUsed":63849488,"heapTotal":77471744,"external":376290},"timestamp":"2025-07-24T05:34:53.747Z"}
[2025-07-24 05:34:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"29841ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:35:53] [WARN] API响应缓慢 | {"method":"GET","url":"/B05AJA220003","statusCode":304,"duration":"16192ms","memoryDiff":{"rss":1482752,"heapUsed":453800,"heapTotal":1048576,"external":53253},"timestamp":"2025-07-24T05:35:53.881Z"}
[2025-07-24 05:35:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220003","duration":"16192ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:35:54] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"16384ms","memoryDiff":{"rss":1265664,"heapUsed":324224,"heapTotal":1048576,"external":45629},"timestamp":"2025-07-24T05:35:54.088Z"}
[2025-07-24 05:35:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"16384ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:38:01] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16320ms","memoryDiff":{"rss":159744,"heapUsed":141320,"heapTotal":1310720,"external":10597},"timestamp":"2025-07-24T05:38:01.562Z"}
[2025-07-24 05:38:01] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16321ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:40:02] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16359ms","memoryDiff":{"rss":647168,"heapUsed":132144,"heapTotal":262144,"external":2723},"timestamp":"2025-07-24T05:40:02.675Z"}
[2025-07-24 05:40:02] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16359ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:40:59] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16103ms","memoryDiff":{"rss":958464,"heapUsed":535272,"heapTotal":0,"external":21079},"timestamp":"2025-07-24T05:40:59.698Z"}
[2025-07-24 05:40:59] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16104ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:41:57] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16187ms","memoryDiff":{"rss":217088,"heapUsed":118760,"heapTotal":0,"external":2931},"timestamp":"2025-07-24T05:41:57.443Z"}
[2025-07-24 05:41:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16187ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:43:12] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16346ms","memoryDiff":{"rss":221184,"heapUsed":142984,"heapTotal":-786432,"external":2929},"timestamp":"2025-07-24T05:43:12.145Z"}
[2025-07-24 05:43:12] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16347ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/94"}
[2025-07-24 05:46:42] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16331ms","memoryDiff":{"rss":196608,"heapUsed":87400,"heapTotal":1310720,"external":-5493},"timestamp":"2025-07-24T05:46:42.946Z"}
[2025-07-24 05:46:42] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16332ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/95"}
[2025-07-24 05:47:42] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16048ms","memoryDiff":{"rss":155648,"heapUsed":112216,"heapTotal":262144,"external":2931},"timestamp":"2025-07-24T05:47:42.059Z"}
[2025-07-24 05:47:42] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16047ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/95"}
[2025-07-24 05:57:04] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"35584ms","memoryDiff":{"rss":36929536,"heapUsed":13596184,"heapTotal":40316928,"external":412137},"timestamp":"2025-07-24T05:57:04.953Z"}
[2025-07-24 05:57:04] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"35584ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 05:57:19] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"50029ms","memoryDiff":{"rss":35848192,"heapUsed":16780040,"heapTotal":39583744,"external":100843},"timestamp":"2025-07-24T05:57:19.413Z"}
[2025-07-24 05:57:19] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"50029ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 05:57:20] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"31115ms","memoryDiff":{"rss":41730048,"heapUsed":27939672,"heapTotal":41627648,"external":544413},"timestamp":"2025-07-24T05:57:20.506Z"}
[2025-07-24 05:57:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"31116ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 05:57:21] [WARN] API响应缓慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"16080ms","memoryDiff":{"rss":6811648,"heapUsed":-311056,"heapTotal":4456448,"external":-222121},"timestamp":"2025-07-24T05:57:21.067Z"}
[2025-07-24 05:57:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"16080ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 05:59:49] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16051ms","memoryDiff":{"rss":1003520,"heapUsed":205896,"heapTotal":1048576,"external":11167},"timestamp":"2025-07-24T05:59:49.293Z"}
[2025-07-24 05:59:49] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16052ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 06:01:37] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"18545ms","memoryDiff":{"rss":315392,"heapUsed":227760,"heapTotal":0,"external":3539},"timestamp":"2025-07-24T06:01:37.596Z"}
[2025-07-24 06:01:37] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"18545ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 06:02:48] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16354ms","memoryDiff":{"rss":806912,"heapUsed":-40712,"heapTotal":-786432,"external":-16005},"timestamp":"2025-07-24T06:02:48.595Z"}
[2025-07-24 06:02:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16355ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 06:04:13] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16042ms","memoryDiff":{"rss":196608,"heapUsed":135480,"heapTotal":1310720,"external":11023},"timestamp":"2025-07-24T06:04:13.953Z"}
[2025-07-24 06:04:13] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16043ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 06:06:28] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16556ms","memoryDiff":{"rss":0,"heapUsed":672712,"heapTotal":0,"external":2153},"timestamp":"2025-07-24T06:06:28.156Z"}
[2025-07-24 06:06:28] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16556ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/96"}
[2025-07-24 06:09:27] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"37194ms","memoryDiff":{"rss":13889536,"heapUsed":10194232,"heapTotal":15204352,"external":53262},"timestamp":"2025-07-24T06:09:27.709Z"}
[2025-07-24 06:09:27] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"37195ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/97"}
[2025-07-24 06:09:30] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"19634ms","memoryDiff":{"rss":51122176,"heapUsed":27296896,"heapTotal":54734848,"external":283285},"timestamp":"2025-07-24T06:09:30.162Z"}
[2025-07-24 06:09:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"19633ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/97"}
[2025-07-24 06:09:31] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"40510ms","memoryDiff":{"rss":64081920,"heapUsed":38030624,"heapTotal":66060288,"external":691515},"timestamp":"2025-07-24T06:09:31.011Z"}
[2025-07-24 06:09:31] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"40510ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/97"}
[2025-07-24 06:09:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2293ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/97"}
[2025-07-24 06:17:10] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16338ms","memoryDiff":{"rss":454656,"heapUsed":173680,"heapTotal":1310720,"external":18804},"timestamp":"2025-07-24T06:17:10.343Z"}
[2025-07-24 06:17:10] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16337ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/97"}
[2025-07-24 06:26:23] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"27581ms","memoryDiff":{"rss":39153664,"heapUsed":22002552,"heapTotal":44773376,"external":390557},"timestamp":"2025-07-24T06:26:23.725Z"}
[2025-07-24 06:26:23] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"27582ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:26:25] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"9400ms","memoryDiff":{"rss":43003904,"heapUsed":17335768,"heapTotal":40316928,"external":335342},"timestamp":"2025-07-24T06:26:25.561Z"}
[2025-07-24 06:26:25] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"9401ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:26:26] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"30182ms","memoryDiff":{"rss":42917888,"heapUsed":8578432,"heapTotal":45613056,"external":54719},"timestamp":"2025-07-24T06:26:26.336Z"}
[2025-07-24 06:26:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"30182ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:26:27] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3422ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:26:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2129ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:26:54] [WARN] API响应缓慢 | {"method":"GET","url":"/B05AJA220003","statusCode":304,"duration":"17329ms","memoryDiff":{"rss":-76124160,"heapUsed":-45644120,"heapTotal":-74395648,"external":-379800},"timestamp":"2025-07-24T06:26:54.417Z"}
[2025-07-24 06:26:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220003","duration":"17329ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:26:54] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"17487ms","memoryDiff":{"rss":-76058624,"heapUsed":-45717248,"heapTotal":-74395648,"external":-388299},"timestamp":"2025-07-24T06:26:54.592Z"}
[2025-07-24 06:26:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"17487ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:33:50] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16348ms","memoryDiff":{"rss":561152,"heapUsed":145840,"heapTotal":-1048576,"external":19831},"timestamp":"2025-07-24T06:33:50.936Z"}
[2025-07-24 06:33:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16347ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:35:19] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"15905ms","memoryDiff":{"rss":315392,"heapUsed":211368,"heapTotal":0,"external":11167},"timestamp":"2025-07-24T06:35:19.927Z"}
[2025-07-24 06:35:19] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"15906ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:36:00] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16124ms","memoryDiff":{"rss":131072,"heapUsed":-54048,"heapTotal":-786432,"external":21079},"timestamp":"2025-07-24T06:36:00.995Z"}
[2025-07-24 06:36:00] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16123ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/98"}
[2025-07-24 06:38:04] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"37161ms","memoryDiff":{"rss":19812352,"heapUsed":11430792,"heapTotal":25427968,"external":129001},"timestamp":"2025-07-24T06:38:04.120Z"}
[2025-07-24 06:38:04] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"37162ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/99"}
[2025-07-24 06:38:06] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"19357ms","memoryDiff":{"rss":43847680,"heapUsed":23061624,"heapTotal":48967680,"external":523800},"timestamp":"2025-07-24T06:38:06.326Z"}
[2025-07-24 06:38:06] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"19357ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/99"}
[2025-07-24 06:38:07] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"40278ms","memoryDiff":{"rss":48472064,"heapUsed":30525064,"heapTotal":53219328,"external":394883},"timestamp":"2025-07-24T06:38:07.223Z"}
[2025-07-24 06:38:07] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"40278ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/99"}
[2025-07-24 06:38:08] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2369ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/99"}
[2025-07-24 06:39:01] [WARN] API响应缓慢 | {"method":"GET","url":"/B05AJA220003","statusCode":304,"duration":"17813ms","memoryDiff":{"rss":1560576,"heapUsed":466080,"heapTotal":1048576,"external":46267},"timestamp":"2025-07-24T06:39:01.372Z"}
[2025-07-24 06:39:01] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220003","duration":"17813ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/99"}
[2025-07-24 06:39:01] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"17964ms","memoryDiff":{"rss":1368064,"heapUsed":351544,"heapTotal":1048576,"external":46835},"timestamp":"2025-07-24T06:39:01.538Z"}
[2025-07-24 06:39:01] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"17964ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/99"}
[2025-07-24 06:43:14] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"20596ms","memoryDiff":{"rss":290816,"heapUsed":167856,"heapTotal":0,"external":-5469},"timestamp":"2025-07-24T06:43:14.157Z"}
[2025-07-24 06:43:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"20595ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/100"}
[2025-07-24 06:43:20] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"5892ms","memoryDiff":{"rss":196608,"heapUsed":422832,"heapTotal":-786432,"external":8412},"timestamp":"2025-07-24T06:43:20.053Z"}
[2025-07-24 06:43:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"5893ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/100"}
[2025-07-24 06:43:20] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"6650ms","memoryDiff":{"rss":196608,"heapUsed":339208,"heapTotal":-786432,"external":11993},"timestamp":"2025-07-24T06:43:20.839Z"}
[2025-07-24 06:43:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"6650ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/100"}
[2025-07-24 06:44:44] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"14887ms","memoryDiff":{"rss":204800,"heapUsed":114352,"heapTotal":1310720,"external":2931},"timestamp":"2025-07-24T06:44:44.330Z"}
[2025-07-24 06:44:44] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"14887ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/101"}
[2025-07-24 06:48:46] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"20905ms","memoryDiff":{"rss":192512,"heapUsed":98976,"heapTotal":0,"external":11023},"timestamp":"2025-07-24T06:48:46.385Z"}
[2025-07-24 06:48:46] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"20906ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/102"}
[2025-07-24 06:53:27] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"30821ms","memoryDiff":{"rss":45543424,"heapUsed":20969784,"heapTotal":49229824,"external":248489},"timestamp":"2025-07-24T06:53:27.906Z"}
[2025-07-24 06:53:27] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"30820ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/105"}
[2025-07-24 06:53:28] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"28321ms","memoryDiff":{"rss":46051328,"heapUsed":15734040,"heapTotal":47394816,"external":405522},"timestamp":"2025-07-24T06:53:28.657Z"}
[2025-07-24 06:53:28] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"28323ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/106"}
[2025-07-24 06:53:30] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"33342ms","memoryDiff":{"rss":59899904,"heapUsed":32361824,"heapTotal":61341696,"external":192537},"timestamp":"2025-07-24T06:53:30.441Z"}
[2025-07-24 06:53:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"33342ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/105"}
[2025-07-24 06:53:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"2540ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/106"}
[2025-07-24 06:53:32] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2173ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/107"}
[2025-07-24 06:53:34] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3464ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/107"}
[2025-07-24 06:53:36] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2135ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/107"}
[2025-07-24 06:53:41] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2063ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/108"}
[2025-07-24 06:53:44] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3098ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/108"}
[2025-07-24 06:53:45] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3756ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/108"}
[2025-07-24 06:53:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2208ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/108"}
[2025-07-24 06:54:50] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"24677ms","memoryDiff":{"rss":5001216,"heapUsed":3296032,"heapTotal":4718592,"external":124227},"timestamp":"2025-07-24T06:54:50.707Z"}
[2025-07-24 06:54:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"24678ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/109"}
[2025-07-24 06:54:54] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"28542ms","memoryDiff":{"rss":41529344,"heapUsed":23328792,"heapTotal":44773376,"external":409859},"timestamp":"2025-07-24T06:54:54.558Z"}
[2025-07-24 06:54:54] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"28542ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/109"}
[2025-07-24 06:54:56] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"10442ms","memoryDiff":{"rss":45686784,"heapUsed":6986224,"heapTotal":40054784,"external":65724},"timestamp":"2025-07-24T06:54:56.478Z"}
[2025-07-24 06:54:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"10441ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/109"}
[2025-07-24 06:54:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3373ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/109"}
[2025-07-24 06:55:00] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2200ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/109"}
[2025-07-24 06:55:05] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2049ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/110"}
[2025-07-24 06:55:08] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3135ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/110"}
[2025-07-24 06:55:14] [WARN] API响应缓慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"8903ms","memoryDiff":{"rss":31834112,"heapUsed":-41899744,"heapTotal":-20447232,"external":-237853},"timestamp":"2025-07-24T06:55:14.665Z"}
[2025-07-24 06:55:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"8903ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/110"}
[2025-07-24 06:55:33] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"15800ms","memoryDiff":{"rss":-81776640,"heapUsed":-9122856,"heapTotal":-27262976,"external":-68907},"timestamp":"2025-07-24T06:55:33.686Z"}
[2025-07-24 06:55:33] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"15800ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:34] [WARN] API响应缓慢 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"19724ms","memoryDiff":{"rss":-71024640,"heapUsed":-2000512,"heapTotal":-16777216,"external":-150827},"timestamp":"2025-07-24T06:55:34.392Z"}
[2025-07-24 06:55:34] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"19724ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/110"}
[2025-07-24 06:55:37] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3888ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:38] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"20253ms","memoryDiff":{"rss":-23719936,"heapUsed":42753120,"heapTotal":26791936,"external":37469},"timestamp":"2025-07-24T06:55:38.151Z"}
[2025-07-24 06:55:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"20253ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4310ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:41] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2991ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:43] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4611ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2494ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:55:55] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2365ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/111"}
[2025-07-24 06:57:50] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"32466ms","memoryDiff":{"rss":47890432,"heapUsed":19092720,"heapTotal":49491968,"external":464882},"timestamp":"2025-07-24T06:57:50.083Z"}
[2025-07-24 06:57:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"32467ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:57:50] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"33079ms","memoryDiff":{"rss":48951296,"heapUsed":26081120,"heapTotal":51064832,"external":391103},"timestamp":"2025-07-24T06:57:50.705Z"}
[2025-07-24 06:57:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"33080ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:57:52] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"35323ms","memoryDiff":{"rss":76955648,"heapUsed":4513336,"heapTotal":50331648,"external":44621},"timestamp":"2025-07-24T06:57:52.955Z"}
[2025-07-24 06:57:52] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"35323ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:57:52] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"2861ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:57:55] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2687ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:57:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4793ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:57:58] [WARN] API响应缓慢 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"5420ms","memoryDiff":{"rss":27770880,"heapUsed":75364712,"heapTotal":52113408,"external":704675},"timestamp":"2025-07-24T06:57:58.378Z"}
[2025-07-24 06:57:58] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"5420ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (Linux; Android 15; PKX110 Build/AP3A.240617.008; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.157 Mobile Safari/537.36 XWEB/1380055 MMWEBSDK/20250503 MMWEBID/7407 MicroMessenger/8.0.61.2880(0x28003D52) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64 MiniProgramEnv/android"}
[2025-07-24 06:59:13] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":304,"duration":"26010ms","memoryDiff":{"rss":41844736,"heapUsed":23432320,"heapTotal":45035520,"external":436048},"timestamp":"2025-07-24T06:59:13.081Z"}
[2025-07-24 06:59:13] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"26010ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/115"}
[2025-07-24 06:59:14] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"7750ms","memoryDiff":{"rss":40615936,"heapUsed":14017032,"heapTotal":41889792,"external":103843},"timestamp":"2025-07-24T06:59:14.839Z"}
[2025-07-24 06:59:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"7750ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/115"}
[2025-07-24 06:59:15] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"28460ms","memoryDiff":{"rss":49528832,"heapUsed":10447744,"heapTotal":49020928,"external":111413},"timestamp":"2025-07-24T06:59:15.547Z"}
[2025-07-24 06:59:15] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"28460ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/115"}
[2025-07-24 06:59:16] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3156ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/115"}
[2025-07-24 07:00:20] [WARN] API响应缓慢 | {"method":"GET","url":"/B05AJA220003","statusCode":304,"duration":"16076ms","memoryDiff":{"rss":2306048,"heapUsed":408744,"heapTotal":1048576,"external":53243},"timestamp":"2025-07-24T07:00:20.538Z"}
[2025-07-24 07:00:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05AJA220003","duration":"16076ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/115"}
[2025-07-24 07:00:20] [WARN] API响应缓慢 | {"method":"GET","url":"/project/B05AJA220003","statusCode":304,"duration":"16244ms","memoryDiff":{"rss":1912832,"heapUsed":286192,"heapTotal":1048576,"external":45619},"timestamp":"2025-07-24T07:00:20.720Z"}
[2025-07-24 07:00:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"16244ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/115"}
[2025-07-24 07:03:11] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16304ms","memoryDiff":{"rss":299008,"heapUsed":96296,"heapTotal":0,"external":11023},"timestamp":"2025-07-24T07:03:11.931Z"}
[2025-07-24 07:03:11] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16304ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/116"}
[2025-07-24 07:10:04] [WARN] API响应缓慢 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"8397ms","memoryDiff":{"rss":-11444224,"heapUsed":-830824,"heapTotal":-13107200,"external":-250},"timestamp":"2025-07-24T07:10:04.194Z"}
[2025-07-24 07:10:04] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"8398ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/117"}
[2025-07-24 07:15:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4499ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:15:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2259ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:15:57] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3436ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:16:01] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"12558ms","memoryDiff":{"rss":74940416,"heapUsed":45062544,"heapTotal":73609216,"external":295050},"timestamp":"2025-07-24T07:16:01.871Z"}
[2025-07-24 07:16:01] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"12558ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:18:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2427ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:18:21] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2322ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:18:25] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2225ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/121"}
[2025-07-24 07:19:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2271ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/122"}
[2025-07-24 07:19:50] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2968ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/122"}
[2025-07-24 07:19:51] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3607ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/122"}
[2025-07-24 07:19:53] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2047ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/122"}
[2025-07-24 07:22:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2953ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/123"}
[2025-07-24 07:22:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"2935ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/123"}
[2025-07-24 07:22:29] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2870ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/123"}
[2025-07-24 07:22:31] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4412ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/123"}
[2025-07-24 07:22:33] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2316ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/123"}
[2025-07-24 08:13:37] [WARN] API响应缓慢 | {"method":"POST","url":"/","statusCode":200,"duration":"14294ms","memoryDiff":{"rss":327680,"heapUsed":79904,"heapTotal":-1048576,"external":737},"timestamp":"2025-07-24T08:13:37.153Z"}
[2025-07-24 08:13:37] [WARN] 检测到慢请求 | {"method":"POST","url":"/","duration":"14294ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/130"}
[2025-07-24 08:14:06] [WARN] API响应缓慢 | {"method":"GET","url":"/","statusCode":200,"duration":"8820ms","memoryDiff":{"rss":41447424,"heapUsed":16479784,"heapTotal":40579072,"external":335345},"timestamp":"2025-07-24T08:14:06.237Z"}
[2025-07-24 08:14:06] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"8820ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/131"}
[2025-07-24 08:14:09] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3147ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/131"}
[2025-07-24 08:14:12] [WARN] API响应缓慢 | {"method":"GET","url":"/profile","statusCode":200,"duration":"15510ms","memoryDiff":{"rss":96980992,"heapUsed":65307648,"heapTotal":90124288,"external":595759},"timestamp":"2025-07-24T08:14:12.936Z"}
[2025-07-24 08:14:12] [WARN] 检测到慢请求 | {"method":"GET","url":"/profile","duration":"15509ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/131"}
[2025-07-24 08:16:13] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2225ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/131"}
[2025-07-24 08:24:37] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2566ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/132"}
[2025-07-24 08:24:39] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2502ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/132"}
[2025-07-24 08:24:41] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3955ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/132"}
[2025-07-24 08:24:43] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2296ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/132"}
[2025-07-24 08:52:42] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2018ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/135"}
[2025-07-24 08:52:44] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2693ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/135"}
[2025-07-24 08:52:45] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3309ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/135"}
[2025-07-24 08:52:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2120ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/135"}
[2025-07-24 08:54:25] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2306ms","threshold":"2000ms","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2504010 MicroMessenger/8.0.5 Language/zh_CN webview/ sessionid/135"}
