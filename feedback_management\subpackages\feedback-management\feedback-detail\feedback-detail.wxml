<!-- pages/feedback-detail/feedback-detail.wxml -->
<view class="container" wx:if="{{!loading && feedback}}">
  <!-- 任务单信息 -->
  <view class="task-info" wx:if="{{feedback}}">
    <view class="info-header">
      <view class="header-left">
        <text class="project-name">{{feedback.project_name}}</text>
        <text class="construction-unit">{{feedback.construction_unit}}</text>
      </view>
    </view>
    <view class="task-details">
      <text class="task-number">任务单号：{{feedback.task_number}}</text>
      <text class="task-number">强度等级：{{feedback.strength_grade}}</text>
      <text class="part-name">部位名称：{{feedback.part_name}}</text>
      <text class="task-desc">施工单位：{{feedback.construction_unit}}</text>
      <text class="task-time">计划时间：{{feedback.scheduled_time}}</text>
      <view class="task-status">
        <text class="status-label">状态：</text>
        <text class="status-tag status-{{feedback.supply_status}}">{{feedback.supply_status === 'supplying' ? '正供' : '供毕'}}</text>
      </view>
    </view>
  </view>
  <!-- 反馈信息 -->
  <view class="info-section">
    <view class="section-title">反馈信息</view>
    <view class="info-card">
      <view class="info-row" wx:if="{{feedback.category}}">
        <text class="info-label">反馈类别：</text>
        <text class="info-value">{{feedback.category}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">反馈时间：</text>
        <text class="info-value">{{feedback.feedback_time_text}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">反馈员：</text>
        <text class="info-value">{{feedback.feedback_user_name}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">反馈位置：</text>
        <view class="location-info-container">
          <text class="info-value location-{{feedback.location_status}}" wx:if="{{feedback.location_status === 'authorized'}}">{{feedback.location_desc}}</text>
          <text class="info-value location-denied" wx:elif="{{feedback.location_status === 'denied'}}">{{feedback.location_desc}}</text>
          <text class="info-value location-unavailable" wx:else>位置信息不可用</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 情况描述 -->
  <view class="info-section" wx:if="{{feedback.notes}}">
    <view class="section-title">情况描述</view>
    <view class="notes-card">
      <text class="notes-text">{{feedback.notes}}</text>
    </view>
  </view>
  <!-- 多媒体文件 -->
  <view class="media-section" wx:if="{{media.length > 0}}">
    <view class="section-title">附件 ({{media.length}})</view>
    <!-- 图片 -->
    <view class="media-group" wx:if="{{images.length > 0}}">
      <view class="media-group-title">图片</view>
      <view class="image-grid">
        <view class="image-item" wx:for="{{images}}" wx:key="id">
          <image src="{{item.file_path}}" mode="aspectFill" bindtap="onPreviewImage" data-src="{{item.file_path}}" class="media-image" binderror="onImageError" bindload="onImageLoad"></image>
          <view class="image-info">
            <text class="file-name">{{item.file_name}}</text>
            <text class="file-size">{{item.file_size_text}}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 视频 -->
    <view class="media-group" wx:if="{{videos.length > 0}}">
      <view class="media-group-title">视频</view>
      <view class="video-list">
        <view class="video-item" wx:for="{{videos}}" wx:key="id">
          <video src="{{item.file_path}}" controls class="media-video" poster=""></video>
          <view class="video-info">
            <text class="file-name">{{item.file_name}}</text>
            <view class="file-meta">
              <text class="file-size">{{item.file_size_text}}</text>
              <text class="file-duration" wx:if="{{item.duration}}">{{item.duration_text}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 音频 -->
    <view class="media-group" wx:if="{{audios.length > 0}}">
      <view class="media-group-title">音频</view>
      <view class="audio-list">
        <view class="audio-item" wx:for="{{audios}}" wx:key="id">
          <view class="audio-player" bindtap="onPlayAudio" data-index="{{item.index}}">
            <view class="audio-icon">
              <text class="play-icon" wx:if="{{!item.isPlaying}}">▶</text>
              <text class="pause-icon" wx:else>∥</text>
            </view>
            <view class="audio-info">
              <text class="file-name">{{item.file_name}}</text>
              <view class="file-meta">
                <text class="file-size">{{item.file_size_text}}</text>
                <text class="file-duration" wx:if="{{item.duration}}">{{item.duration_text}}</text>
              </view>
            </view>
            <view class="audio-status">
              <text class="status-text" wx:if="{{item.isPlaying}}">播放中</text>
              <text class="status-text" wx:else>点击播放</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 无附件提示 -->
  <view class="no-media" wx:if="{{media.length === 0}}">
    <view class="no-media-icon">📎</view>
    <text class="no-media-text">暂无附件</text>
  </view>
</view>
<!-- 加载状态 -->
<view class="loading-state" wx:if="{{loading}}">
  <text class="loading-text">加载中...</text>
</view>