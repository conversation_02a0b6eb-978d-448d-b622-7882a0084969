const express = require("express");
const router = express.Router();
const Project = require("../models/Project");
const {
  requireAuth,
} = require("../middleware/auth");

// 获取当前公司的工程列表
router.get("/", requireAuth, async (req, res) => {
  try {
    // 检查必要的数据
    if (!req.session.currentCompany) {
      return res.status(400).json({
        success: false,
        message: "当前公司信息缺失",
      });
    }

    const companyId = req.session.currentCompany.id;
    const { keyword, page = 1, pageSize = 10 } = req.query;

    const filters = {};
    if (keyword) filters.keyword = keyword;

    console.log("=== 工程列表API调用 ===");
    console.log("查询公司ID:", companyId);
    console.log("查询过滤条件:", filters);
    console.log("原始查询参数:", req.query);
    console.log("解析后分页参数:", { page, pageSize, pageType: typeof page, pageSizeType: typeof pageSize });

    // 只有明确请求分页时才使用分页
    const needPagination = req.query.page !== undefined;

    // 不再强制启用分页，只有在明确请求时才分页
    const forceUsePagination = false;

    console.log("分页检查结果:", {
      hasPage: req.query.page !== undefined,
      pageValue: req.query.page,
      pageSizeValue: req.query.pageSize,
      needPagination,
      queryKeys: Object.keys(req.query),
      willUsePagination: needPagination
    });

    if (needPagination || forceUsePagination) {
      console.log("=== 使用分页查询 ===");
      // 使用分页查询
      const result = await Project.getByCompanyIdWithPagination(companyId, filters, parseInt(page), parseInt(pageSize));

      console.log("分页查询结果:", {
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: result.totalPages,
        dataCount: result.data.length
      });

      res.json({
        success: true,
        data: result.data,
        pagination: {
          page: result.page,
          pageSize: result.pageSize,
          total: result.total,
          totalPages: result.totalPages,
          hasNext: result.page < result.totalPages,
          hasPrev: result.page > 1
        }
      });
    } else {
      // 不分页，返回所有数据（保持向后兼容）
      const projects = await Project.getByCompanyId(companyId, filters);

      res.json({
        success: true,
        data: projects,
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取工程列表失败",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});



// 获取工程详情
router.get("/:id", requireAuth, async (req, res) => {
  try {
    const projectId = req.params.id;
    const companyId = req.session.currentCompany.id;

    const project = await Project.findById(projectId);
    if (!project || project.company_id !== companyId) {
      return res.status(404).json({
        success: false,
        message: "工程不存在",
      });
    }

    // 获取工程统计信息
    const stats = await Project.getStats(projectId);

    res.json({
      success: true,
      data: {
        project,
        stats,
      },
    });
  } catch (error) {
    console.error("获取工程详情错误:", error);
    res.status(500).json({
      success: false,
      message: "获取工程详情失败",
    });
  }
});

// 更新工程信息
router.put("/:id", requireAuth, async (req, res) => {
  try {
    const projectId = req.params.id;
    const companyId = req.session.currentCompany.id;

    // 检查工程是否存在且属于当前公司
    const project = await Project.findById(projectId);
    if (!project || project.company_id !== companyId) {
      return res.status(404).json({
        success: false,
        message: "工程不存在",
      });
    }

    const {
      name,
      code,
      construction_unit,
    } = req.body;

    const updated = await Project.update(projectId, {
      name,
      code,
      construction_unit,
    });

    if (!updated) {
      return res.status(400).json({
        success: false,
        message: "更新工程信息失败",
      });
    }

    res.json({
      success: true,
      message: "更新工程信息成功",
    });
  } catch (error) {
    console.error("更新工程信息错误:", error);
    res.status(500).json({
      success: false,
      message: "更新工程信息失败",
    });
  }
});



module.exports = router;
