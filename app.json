{"pages": ["pages/index/index", "pages/order/order", "pages/customer/customer", "pages/supplier/supplier", "pages/xiangtong/xiangtong", "pages/orderDetail/orderDetail", "pages/orderNew/orderNew", "pages/Login/Login", "pages/orderUpdate/orderUpdate", "pages/orderCarLeaderApprove/orderCarLeaderApprove", "pages/orderChoseRepair/orderChoseRepair", "pages/orderScanCode/orderScanCode", "pages/fbindex/fbindex", "pages/feedback-list/feedback-list"], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于记录反馈位置"}}, "requiredPrivateInfos": ["getLocation"], "subpackages": [{"root": "subpackages/project-management", "name": "project", "pages": ["project-list/project-list"]}, {"root": "subpackages/task-management", "name": "task", "pages": ["task-list/task-list"]}, {"root": "subpackages/feedback-management", "name": "feedback", "pages": ["feedback/feedback", "feedback-detail/feedback-detail", "camera-capture/camera-capture"]}], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#1296db", "navigationBarTitleText": "厦门路桥翔通", "navigationBarTextStyle": "white", "enablePullDownRefresh": true, "onReachBottomDistance": 30}, "lazyCodeLoading": "requiredComponents", "style": "v2", "usingComponents": {"modal": "/components/modal/modal"}, "sitemapLocation": "sitemap.json"}