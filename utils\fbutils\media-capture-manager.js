/**
 * 媒体捕获管理器
 * 统一管理拍照、录像、录音功能，包括临时存储、预览和上传
 */

const ErrorRetryManager = require('./error-retry-manager');

class MediaCaptureManager {
  constructor(page) {
    this.page = page;
    this.tempFiles = {
      images: [],
      videos: [],
      audios: []
    };
    this.uploadQueue = [];
    this.isUploading = false;

    // 初始化错误重试管理器
    this.errorRetryManager = new ErrorRetryManager();

    // 配置重试策略
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2
    };
  }

  /**
   * 初始化媒体捕获管理器
   */
  init() {
    this.checkPermissions();
    this.initTempStorage();
  }

  /**
   * 检查媒体权限
   */
  async checkPermissions() {
    try {
      // 检查相机权限
      const cameraAuth = await this.checkCameraPermission();
      // 检查录音权限
      const recordAuth = await this.checkRecordPermission();
      
      return {
        camera: cameraAuth,
        record: recordAuth
      };
    } catch (error) {
      console.error('权限检查失败:', error);
      return {
        camera: false,
        record: false
      };
    }
  }

  /**
   * 检查相机权限
   */
  checkCameraPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.camera'] === false) {
            // 用户拒绝了相机权限
            resolve(false);
          } else if (res.authSetting['scope.camera'] === true) {
            // 用户已授权相机权限
            resolve(true);
          } else {
            // 未询问过权限，需要主动申请
            wx.authorize({
              scope: 'scope.camera',
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          }
        },
        fail: () => resolve(false)
      });
    });
  }

  /**
   * 检查录音权限
   */
  checkRecordPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record'] === false) {
            resolve(false);
          } else if (res.authSetting['scope.record'] === true) {
            resolve(true);
          } else {
            wx.authorize({
              scope: 'scope.record',
              success: () => resolve(true),
              fail: () => resolve(false)
            });
          }
        },
        fail: () => resolve(false)
      });
    });
  }

  /**
   * 初始化临时存储
   */
  initTempStorage() {
    // 清理之前的临时文件
    this.clearTempFiles();
    
    // 初始化存储结构
    this.tempFiles = {
      images: [],
      videos: [],
      audios: []
    };
  }

  /**
   * 拍照功能
   */
  async capturePhoto() {
    return this.errorRetryManager.executeWithRetry(
      async () => {
        // 检查相机权限
        const hasPermission = await this.checkCameraPermission();
        if (!hasPermission) {
          this.showPermissionGuide('camera');
          throw new Error('PERMISSION_DENIED');
        }

        // 跳转到相机页面
        const result = await this.navigateToCamera('photo');
        if (result && result.tempFilePath) {
          return this.processPhotoCapture(result);
        }
        throw new Error('拍照取消或失败');
      },
      {
        maxRetries: 1, // 拍照操作不需要多次重试
        retryConditions: []
      },
      { operation: 'capturePhoto' }
    ).catch(error => {
      this.errorRetryManager.showErrorToUser(error, {
        title: '拍照失败',
        showRetryButton: true,
        onRetry: () => this.capturePhoto()
      });
      return null;
    });
  }

  /**
   * 录像功能
   */
  async captureVideo() {
    try {
      const hasPermission = await this.checkCameraPermission();
      if (!hasPermission) {
        this.showPermissionGuide('camera');
        return null;
      }

      const result = await this.navigateToCamera('video');
      if (result && result.tempFilePath) {
        return this.processVideoCapture(result);
      }
      return null;
    } catch (error) {
      console.error('录像失败:', error);
      this.showError('录像失败，请重试');
      return null;
    }
  }

  /**
   * 录音功能
   */
  async captureAudio() {
    try {
      const hasPermission = await this.checkRecordPermission();
      if (!hasPermission) {
        this.showPermissionGuide('record');
        return null;
      }

      return this.startAudioRecording();
    } catch (error) {
      console.error('录音失败:', error);
      this.showError('录音失败，请重试');
      return null;
    }
  }

  /**
   * 跳转到相机页面
   */
  navigateToCamera(mode) {
    return new Promise((resolve, reject) => {
      // 保存原有的回调函数
      const originalCallback = this.page.onCameraResult;

      // 设置临时回调函数
      this.page.onCameraResult = (result) => {
        // 恢复原有的回调函数
        if (originalCallback) {
          this.page.onCameraResult = originalCallback;
        }
        resolve(result);
      };

      // 跳转到相机页面
      wx.navigateTo({
        url: `../camera-capture/camera-capture?mode=${mode}`,
        success: () => {
          console.log(`跳转到相机页面成功: ${mode}`);
        },
        fail: (error) => {
          // 恢复原有的回调函数
          if (originalCallback) {
            this.page.onCameraResult = originalCallback;
          }
          console.error('跳转到相机页面失败:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * 处理拍照结果
   */
  async processPhotoCapture(result) {
    try {
      // 获取文件信息
      const fileInfo = await this.getFileInfo(result.tempFilePath);
      
      // 创建图片对象
      const imageItem = {
        id: this.generateTempId(),
        tempFilePath: result.tempFilePath,
        type: 'image',
        size: fileInfo.size,
        createTime: Date.now(),
        status: 'captured' // captured, uploading, uploaded, failed
      };

      // 添加到临时文件列表
      this.tempFiles.images.push(imageItem);
      
      // 更新页面数据
      this.updatePageData();
      
      return imageItem;
    } catch (error) {
      console.error('处理拍照结果失败:', error);
      throw error;
    }
  }

  /**
   * 处理录像结果
   */
  async processVideoCapture(result) {
    try {
      const fileInfo = await this.getFileInfo(result.tempFilePath);
      
      const videoItem = {
        id: this.generateTempId(),
        tempFilePath: result.tempFilePath,
        type: 'video',
        size: fileInfo.size,
        sizeText: this.formatFileSize(fileInfo.size),
        duration: result.duration || 0,
        createTime: Date.now(),
        status: 'captured'
      };

      this.tempFiles.videos.push(videoItem);
      this.updatePageData();
      
      return videoItem;
    } catch (error) {
      console.error('处理录像结果失败:', error);
      throw error;
    }
  }

  /**
   * 开始录音
   */
  startAudioRecording() {
    return new Promise((resolve, reject) => {
      const recorderManager = wx.getRecorderManager();
      
      // 录音配置
      const options = {
        duration: 60000, // 最大60秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3'
      };

      // 开始录音
      recorderManager.start(options);
      
      // 更新页面状态
      this.page.setData({
        recording: true,
        recordTime: 0
      });

      // 开始计时
      const timer = setInterval(() => {
        const recordTime = this.page.data.recordTime + 1;
        this.page.setData({ recordTime });

        if (recordTime >= 60) {
          this.stopAudioRecording(recorderManager, timer, resolve, reject);
        }
      }, 1000);

      // 设置停止录音的方法
      this.page.stopRecording = () => {
        this.stopAudioRecording(recorderManager, timer, resolve, reject);
      };

      // 监听录音结束
      recorderManager.onStop((res) => {
        clearInterval(timer);
        this.page.setData({
          recording: false,
          recordTime: 0
        });

        this.processAudioCapture(res).then(resolve).catch(reject);
      });

      // 监听录音错误
      recorderManager.onError((error) => {
        clearInterval(timer);
        this.page.setData({
          recording: false,
          recordTime: 0
        });
        reject(error);
      });
    });
  }

  /**
   * 停止录音
   */
  stopAudioRecording(recorderManager, timer, resolve, reject) {
    try {
      recorderManager.stop();
      clearInterval(timer);
    } catch (error) {
      reject(error);
    }
  }

  /**
   * 处理录音结果
   */
  async processAudioCapture(result) {
    try {
      const audioItem = {
        id: this.generateTempId(),
        tempFilePath: result.tempFilePath,
        type: 'audio',
        size: result.fileSize || 0,
        sizeText: this.formatFileSize(result.fileSize || 0),
        duration: Math.round(result.duration / 1000),
        createTime: Date.now(),
        status: 'captured'
      };

      this.tempFiles.audios.push(audioItem);
      this.updatePageData();

      return audioItem;
    } catch (error) {
      console.error('处理录音结果失败:', error);
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 生成临时ID
   */
  generateTempId() {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 更新页面数据
   */
  updatePageData() {
    const allImages = [...this.tempFiles.images];
    const allVideos = [...this.tempFiles.videos];
    const allAudios = [...this.tempFiles.audios];

    this.page.setData({
      images: allImages.map(item => item.tempFilePath),
      videos: allVideos,
      audios: allAudios,
      tempFiles: this.tempFiles
    });
  }

  /**
   * 删除临时文件
   */
  removeTempFile(id, type) {
    try {
      const fileList = this.tempFiles[type + 's'];
      const index = fileList.findIndex(item => item.id === id);

      if (index !== -1) {
        fileList.splice(index, 1);
        this.updatePageData();

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('删除临时文件失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  }

  /**
   * 清理所有临时文件
   */
  clearTempFiles() {
    this.tempFiles = {
      images: [],
      videos: [],
      audios: []
    };
    this.updatePageData();
  }

  /**
   * 批量上传所有媒体文件
   */
  async uploadAllMedia(feedbackId) {
    try {
      this.isUploading = true;
      const uploadResults = [];

      // 显示上传进度
      wx.showLoading({
        title: '上传中...',
        mask: true
      });

      // 上传图片
      for (const image of this.tempFiles.images) {
        try {
          image.status = 'uploading';
          this.updatePageData();

          const result = await this.uploadSingleFile(image, 'image');
          image.status = 'uploaded';
          uploadResults.push(result);
        } catch (error) {
          image.status = 'failed';
          console.error('图片上传失败:', error);
          console.error('原始错误:', error.originalError || error);
          console.error('错误详情:', {
            message: error.message,
            errMsg: error.errMsg,
            code: error.code,
            statusCode: error.statusCode
          });
        }
      }

      // 上传视频
      for (const video of this.tempFiles.videos) {
        try {
          video.status = 'uploading';
          this.updatePageData();

          const result = await this.uploadSingleFile(video, 'video');
          video.status = 'uploaded';
          uploadResults.push(result);
        } catch (error) {
          video.status = 'failed';
          console.error('视频上传失败:', error);
        }
      }

      // 上传音频
      for (const audio of this.tempFiles.audios) {
        try {
          audio.status = 'uploading';
          this.updatePageData();

          const result = await this.uploadSingleFile(audio, 'audio');
          audio.status = 'uploaded';
          uploadResults.push(result);
        } catch (error) {
          audio.status = 'failed';
          console.error('音频上传失败:', error);
        }
      }

      wx.hideLoading();
      this.isUploading = false;

      return uploadResults;
    } catch (error) {
      wx.hideLoading();
      this.isUploading = false;
      throw error;
    }
  }

  /**
   * 上传单个文件（带重试机制）
   */
  uploadSingleFile(fileItem, type) {
    const uploadOperation = () => {
      return new Promise((resolve, reject) => {
        const app = getApp();
        const uploadUrl = `${app.globalData.baseUrl}/api/upload/single`;

        console.log('开始上传文件:', {
          url: uploadUrl,
          filePath: fileItem.tempFilePath,
          type: type,
          baseUrl: app.globalData.baseUrl
        });

        wx.uploadFile({
          url: uploadUrl,
          filePath: fileItem.tempFilePath,
          name: 'file',
          header: {
            'X-Current-Company': wx.getStorageSync('currentCompany')?.id || ''
          },
          timeout: 90000,
          success: (res) => {
            console.log('上传响应:', res);
            try {
              const data = JSON.parse(res.data);
              console.log('解析后的响应数据:', data);

              if (data.success) {
                const result = {
                  file_type: type,
                  file_name: data.data.filename,
                  file_path: data.data.url,
                  file_size: data.data.size,
                  duration: fileItem.duration || null
                };
                console.log('上传成功，返回结果:', result);
                resolve(result);
              } else {
                console.error('服务器返回失败:', data);
                reject(new Error(data.message || '上传失败'));
              }
            } catch (error) {
              console.error('响应解析失败:', error, 'raw response:', res);
              reject(new Error('服务器响应解析失败'));
            }
          },
          fail: (error) => {
            // 记录详细的错误信息
            console.error('wx.uploadFile 失败:', error);

            // 转换微信错误为标准错误
            const standardError = new Error(error.errMsg || '上传失败');
            standardError.originalError = error;
            standardError.errMsg = error.errMsg;
            standardError.errno = error.errno;

            if (error.errMsg && error.errMsg.includes('timeout')) {
              standardError.code = 'ETIMEDOUT';
            } else if (error.errMsg && error.errMsg.includes('network')) {
              standardError.code = 'ECONNRESET';
            } else if (error.errMsg && error.errMsg.includes('fail')) {
              standardError.code = 'UPLOAD_FAIL';
            }

            reject(standardError);
          }
        });
      });
    };

    // 使用错误重试管理器包装上传操作
    return this.errorRetryManager.wrapFileUpload(uploadOperation, {
      maxRetries: 2,
      baseDelay: 2000,
      retryConditions: ['NETWORK_ERROR', 'TIMEOUT']
    })();
  }

  /**
   * 显示权限引导
   */
  showPermissionGuide(type) {
    const messages = {
      camera: '需要相机权限才能拍照和录像，请在设置中开启',
      record: '需要录音权限才能录音，请在设置中开启'
    };

    wx.showModal({
      title: '权限申请',
      content: messages[type],
      confirmText: '去设置',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting();
        }
      }
    });
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  }

  /**
   * 获取所有临时文件
   */
  getAllTempFiles() {
    return this.tempFiles;
  }

  /**
   * 获取上传状态
   */
  getUploadStatus() {
    return this.isUploading;
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.clearTempFiles();
    this.uploadQueue = [];
    this.isUploading = false;
  }
}

module.exports = MediaCaptureManager;
