创建目录: upload/others
上传目录结构初始化完成
[2025-07-28 05:46:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:46:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:17] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-07-28 05:46:21] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:46:21] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:46:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:46:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 05:46:26] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:46:26] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:46:27] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:46:27] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:46:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 05:46:30] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:46:30] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:48:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 3
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 3
[2025-07-28 05:48:10] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 05:48:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:48:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:48:15] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:48:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:48:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:48:35] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:48:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:49:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:49:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:49:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:49:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:49:56] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:49:56] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:51:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:51:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:51:21] [DEBUG] 工程统计信息查询成功 | {"total_tasks":0,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":0}
[2025-07-28 05:51:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:52:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:52:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:52:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:52:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:52:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:52:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:52:51] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:52:51] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:52:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:52:51] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 05:52:53] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:52:53] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:52:54] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:52:54] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:52:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 05:52:56] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:52:56] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:53:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:53:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:53:36] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":3}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:53:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:53:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:53:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 05:53:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
天地图代理请求: 纬度=24.5118, 经度=118.14577
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城',
    location: { lon: 118.14577, lat: 24.5118 },
    addressComponent: {
      address: '金湖三里30-46禹洲·香槟城',
      town: '禾山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '西北',
      county: '湖里区',
      city_code: '*********',
      address_position: '西北',
      poi: '禹洲·香槟城北门',
      province_code: '*********',
      town_code: '*********003',
      province: '福建省',
      road: '金湖路',
      road_distance: 67,
      address_distance: 31,
      poi_distance: 31
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-07-28 05:53:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:53:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:53:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 05:53:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:54:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:54:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:54:51] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:54:52] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:54:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:54:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 05:54:55] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:54:55] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:54:55] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:54:55] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:54:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-07-28 05:54:57] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:54:57] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 05:56:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:56:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:56:43] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":3}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:56:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:56:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:56:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-07-28 05:56:46] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:59:39] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:59:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:59:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:59:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:59:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:59:52] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":2018,"task_number":"B05-2200252","feedback_user_id":"2015493","location_status":"authorized"}
[2025-07-28 05:59:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-07-28 05:59:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 05:59:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 05:59:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 05:59:58] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 05:59:58] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 4
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 4
[2025-07-28 05:59:58] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 05:59:58] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 06:00:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 06:00:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 06:00:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 06:00:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 06:00:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 06:00:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 06:05:36] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 4
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 4
[2025-07-28 06:05:36] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2015493:1007","ttl":120000}
[2025-07-28 06:06:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 06:06:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-07-28 06:06:34] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-07-28 06:06:34] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-07-28 08:00:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2015493 公司ID: 1007
[2025-07-28 08:00:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 08:00:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-07-28 08:00:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
