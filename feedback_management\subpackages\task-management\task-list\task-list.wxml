<!-- pages/task-list/task-list.wxml -->
<view class="container">
  <!-- 页面头部 -->
  <view class="header" wx:if="{{projectInfo}}">
    <view class="project-info">
      <view class="project-title">
        <text class="project-name">工程名称：{{projectInfo.name}}</text>
        <text class="project-code" wx:if="{{projectInfo.code}}">工程编号：{{projectInfo.code}}</text>
      </view>
      <text class="construction-unit">施工单位：{{projectInfo.construction_unit}}</text>
    </view>

    <!-- 项目统计信息 -->
    <view class="project-stats" wx:if="{{projectStats}}">
      <view class="stats-title">任务单统计</view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{projectStats.total_tasks}}</view>
          <view class="stat-label">总任务数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{projectStats.supplying_tasks}}</view>
          <view class="stat-label">供应中</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{projectStats.completed_tasks}}</view>
          <view class="stat-label">已完成</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{projectStats.total_feedbacks}}</view>
          <view class="stat-label">反馈记录</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  <!-- 筛选器 -->
  <view class="filter-section">
    <view class="filter-header">
      <text class="section-title">新建现场反馈-选择任务单</text>
    </view>

    <!-- 筛选按钮组 -->
    <view class="filter-buttons">
      <view class="filter-button {{currentFilter === item.value ? 'active' : ''}}"
            wx:for="{{filterOptions}}"
            wx:key="value"
            bindtap="onFilterSelect"
            data-value="{{item.value}}">
        <text class="button-text">{{item.label}}({{item.count}})</text>
      </view>
    </view>
  </view>
  <view class="task-list" wx:if="{{!loading && tasks.length > 0}}">
    <view class="task-item" wx:for="{{tasks}}" wx:key="id">
      <view class="task-info" bindtap="onTaskTap" data-task="{{item}}">
        <view class="task-header">
          <view class="task-number">任务单号：{{item.task_number}}</view>
          <view class="supply-status {{item.supply_status_class}}">
            {{item.supply_status_text}}
          </view>
        </view>
        <view class="task-content">
          <view class="task-part">
            <text class="label">部位名称：</text>
            <text class="value">{{item.part_name}}</text>
          </view>

          <view class="task-concrete" wx:if="{{item.strength_grade}}">
            <text class="label">强度等级：</text>
            <text class="value">{{item.strength_grade}}</text>
          </view>
          <!-- <view class="task-volume" wx:if="{{item.concrete_volume}}">
            <text class="label">混凝土方量：</text>
            <text class="value">{{item.concrete_volume}}m³</text>
          </view> -->
          <view class="task-feedback-user" wx:if="{{item.feedback_user}}">
            <text class="label">反馈人员：</text>
            <text class="value">{{item.feedback_user}}</text>
          </view>
          <view class="task-time" wx:if="{{item.scheduled_time_text}}">
            <text class="label">计划时间：</text>
            <text class="value">{{item.scheduled_time_text}}</text>
          </view>
        </view>
        <view class="task-footer">
          <view class="feedback-count">{{item.feedback_count_text}}</view>
          <view class="arrow">></view>
        </view>
      </view>

    </view>
  </view>
  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && tasks.length === 0}}">
    <view class="empty-icon">📋</view>
    <text class="empty-text">暂无任务单</text>
    <text class="empty-desc">点击上方按钮新建任务单</text>
  </view>
</view>

