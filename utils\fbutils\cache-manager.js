/**
 * 请求缓存管理器
 * 提供智能缓存功能，避免重复请求相同数据
 */

class CacheManager {
  constructor() {
    // 内存缓存
    this.memoryCache = new Map();
    // 缓存配置
    this.config = {
      // 默认缓存时间（毫秒）
      defaultTTL: 5 * 60 * 1000, // 5分钟
      // 最大缓存条目数
      maxSize: 100,
      // 不同类型请求的缓存时间
      ttlByType: {
        'user-info': 10 * 60 * 1000,      // 用户信息：10分钟
        'company-list': 15 * 60 * 1000,   // 公司列表：15分钟
        'project-list': 5 * 60 * 1000,    // 项目列表：5分钟
        'task-list': 3 * 60 * 1000,       // 任务列表：3分钟
        'feedback-list': 2 * 60 * 1000, // 反馈记录：2分钟
        'stats': 1 * 60 * 1000,           // 统计数据：1分钟
      }
    };
  }

  /**
   * 生成缓存键
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @param {Object} data - 请求数据
   * @returns {string} 缓存键
   */
  generateKey(url, method = 'GET', data = {}) {
    const baseKey = `${method.toUpperCase()}:${url}`;
    if (method.toUpperCase() === 'GET' && Object.keys(data).length > 0) {
      const queryString = Object.keys(data)
        .sort()
        .map(key => `${key}=${JSON.stringify(data[key])}`)
        .join('&');
      return `${baseKey}?${queryString}`;
    }
    return baseKey;
  }

  /**
   * 获取缓存类型
   * @param {string} url - 请求URL
   * @returns {string} 缓存类型
   */
  getCacheType(url) {
    if (url.includes('/user') || url.includes('/profile')) return 'user-info';
    if (url.includes('/companies')) return 'company-list';
    if (url.includes('/projects')) return 'project-list';
    if (url.includes('/tasks')) return 'task-list';
    if (url.includes('/feedbacks')) return 'feedback-list';
    if (url.includes('/stats')) return 'stats';
    return 'default';
  }

  /**
   * 获取缓存TTL
   * @param {string} cacheType - 缓存类型
   * @returns {number} TTL（毫秒）
   */
  getTTL(cacheType) {
    return this.config.ttlByType[cacheType] || this.config.defaultTTL;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} data - 缓存数据
   * @param {number} ttl - 生存时间（毫秒）
   */
  set(key, data, ttl = null) {
    // 如果缓存已满，删除最旧的条目
    if (this.memoryCache.size >= this.config.maxSize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    const expiresAt = Date.now() + (ttl || this.config.defaultTTL);
    this.memoryCache.set(key, {
      data,
      expiresAt,
      createdAt: Date.now()
    });

    // 同时存储到本地存储（持久化）
    try {
      const cacheData = {
        data,
        expiresAt,
        createdAt: Date.now()
      };
      wx.setStorageSync(`cache_${key}`, cacheData);
    } catch (error) {
      console.warn('缓存持久化失败:', error);
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {*} 缓存数据，如果不存在或已过期则返回null
   */
  get(key) {
    // 先从内存缓存获取
    let cacheItem = this.memoryCache.get(key);
    
    // 如果内存中没有，尝试从本地存储获取
    if (!cacheItem) {
      try {
        cacheItem = wx.getStorageSync(`cache_${key}`);
        if (cacheItem) {
          // 恢复到内存缓存
          this.memoryCache.set(key, cacheItem);
        }
      } catch (error) {
        console.warn('从本地存储获取缓存失败:', error);
      }
    }

    if (!cacheItem) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > cacheItem.expiresAt) {
      this.remove(key);
      return null;
    }

    return cacheItem.data;
  }

  /**
   * 移除缓存
   * @param {string} key - 缓存键
   */
  remove(key) {
    this.memoryCache.delete(key);
    try {
      wx.removeStorageSync(`cache_${key}`);
    } catch (error) {
      console.warn('移除本地缓存失败:', error);
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear();
    
    // 清理本地存储中的缓存
    try {
      const storageInfo = wx.getStorageInfoSync();
      const cacheKeys = storageInfo.keys.filter(key => key.startsWith('cache_'));
      cacheKeys.forEach(key => {
        try {
          wx.removeStorageSync(key);
        } catch (error) {
          console.warn(`移除缓存 ${key} 失败:`, error);
        }
      });
    } catch (error) {
      console.warn('清理本地缓存失败:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  cleanExpired() {
    const now = Date.now();
    const expiredKeys = [];

    // 清理内存缓存中的过期项
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.remove(key));
  }

  /**
   * 根据URL模式清理缓存
   * @param {string} pattern - URL模式（支持通配符*）
   */
  clearByPattern(pattern) {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const keysToDelete = [];

    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.remove(key));
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;
    let totalSize = 0;

    for (const [key, item] of this.memoryCache.entries()) {
      totalSize += JSON.stringify(item.data).length;
      if (now > item.expiresAt) {
        expiredCount++;
      } else {
        validCount++;
      }
    }

    return {
      totalItems: this.memoryCache.size,
      validItems: validCount,
      expiredItems: expiredCount,
      totalSize: totalSize,
      maxSize: this.config.maxSize
    };
  }

  /**
   * 检查缓存是否存在且有效
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在有效缓存
   */
  has(key) {
    return this.get(key) !== null;
  }

  /**
   * 预热缓存（预加载常用数据）
   * @param {Array} urls - 要预加载的URL列表
   */
  async warmup(urls) {
    const promises = urls.map(async (url) => {
      try {
        // 这里需要实际的请求方法，暂时跳过
      } catch (error) {
        console.warn(`预热缓存失败 ${url}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }
}

// 创建全局缓存管理器实例
const cacheManager = new CacheManager();

// 定期清理过期缓存（每5分钟）
setInterval(() => {
  cacheManager.cleanExpired();
}, 5 * 60 * 1000);

module.exports = cacheManager;
